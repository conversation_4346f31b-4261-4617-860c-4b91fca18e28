<template>
  <div class="special-date">
    <el-card>
      <div class="content">
        <div class="content-header">
          <div class="content-header-title">组{{ props.index + 1 }}</div>
          <div class="content-header-action">
            <el-button type="text" @click="onClickDelete">删除</el-button>
          </div>
        </div>
        <div class="content-body">
          <el-form label-width="100px">
            <el-form-item label="日期">
              <el-date-picker
                v-model="props.specialDate.specialDate"
                type="Date"
                value-format="YYYY-MM-DD"
                placeholder="选择日期"
                style="width: 100%"
                :clearable="false"
                @change="onChangeDate"
              />
            </el-form-item>
            <el-form-item label="设置为">
              <el-radio-group v-model="props.specialDate.type">
                <el-radio :value="0">休息</el-radio>
                <el-radio :value="1">工作日</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="班次时间段" v-if="props.specialDate.type == 1">
              <el-time-picker
                v-model="props.specialDate.workTime"
                is-range
                start-placeholder="上班时间"
                end-placeholder="下班时间"
                format="HH:mm"
                value-format="HH:mm"
                :clearable="false"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-card>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive } from "vue";

const props = defineProps({
  index: {
    type: Number,
    default: 0,
  },
  specialDate: {
    type: Object,
    default: () => {
      return {
        specialDate: "",
        type: 0,
        workTime: ["09:00", "18:00"],
      };
    },
  },
});

const state = reactive({
  workStartTime: "",
  workEndTime: "",
});

const emit = defineEmits(["click-delete"]);

const onClickDelete = () => {
  emit("click-delete");
};
</script>
<style lang="scss" scoped>
.special-date {
  :deep(.el-card__body) {
    padding: 0;
  }
}
.content {
  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f0f0f0;
    padding: 10px 20px;
    &-title {
      font-weight: 600;
      color: #333333;
    }
  }
  &-body {
    padding: 20px;
  }
}
</style>
