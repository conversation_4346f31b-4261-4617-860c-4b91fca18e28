/*
 * @Descripttion: 
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-01-13 14:14:11
 * @LastEditors: z<PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-01-16 15:48:52
 * @Introduce: 
 */
import service from '@/utils/request'

export const useLegalHolidayRulesApi = (id: number) => {
  return service.get('/work/legalHolidayRules/' + id)
}
export const getHolidayRules = (year:string) => {
  return service.get('/work/legalHolidayRules/apiHoliday/'+year)
}

export const useLegalHolidayRulesSubmitApi = (dataForm: any) => {
  if (dataForm.id) {
    return service.put('/work/legalHolidayRules', dataForm)
  } else {
    return service.post('/work/legalHolidayRules', dataForm)
  }
}