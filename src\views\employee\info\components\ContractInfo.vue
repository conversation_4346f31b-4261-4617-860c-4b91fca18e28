<template>
  <el-card>
    <div class="info">
      <div class="info-title">
        <div class="info-title-text">
          <InfoTitle title="合同信息"></InfoTitle>
        </div>
        <div class="info-title-divider"></div>
        <div class="info-title-action">
          <el-button type="text" @click="onClickAddContract()" v-if="props.allowEdit"
            >新增</el-button
          >
        </div>
      </div>
      <div class="info-content">
        <el-table border :data="state.dataList">
          <fast-table-column
            prop="contractType"
            label="合同类型"
            header-align="center"
            align="center"
            dict-type="contract_type"
            width="150"
          >
          </fast-table-column>
          <el-table-column
            prop="contractNo"
            label="合同编号"
            header-align="center"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="signStartTime"
            label="合同开始时间"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              {{
                scope.row.signStartTime &&
                dayjs(scope.row.signStartTime).format("YYYY-MM-DD")
              }}
            </template>
          </el-table-column>
          <el-table-column
            prop="signEndTime"
            label="合同结束时间"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              {{
                scope.row.signEndTime && dayjs(scope.row.signEndTime).format("YYYY-MM-DD")
              }}
            </template>
          </el-table-column>
          <fast-table-column
            prop="contractStatus"
            label="合同状态"
            header-align="center"
            align="center"
            dict-type="contract_status"
            width="100"
          >
          </fast-table-column>
          <el-table-column
            prop="contractDeptName"
            label="合同公司"
            header-align="center"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="contractEffective"
            label="合同有效期"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.contractEffective }}{{ scope.row.timeType }}
            </template>
          </el-table-column>
          <el-table-column
            prop="signTime"
            label="合同签订时间"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.signTime && dayjs(scope.row.signTime).format("YYYY-MM-DD") }}
            </template>
          </el-table-column>
          <el-table-column
            prop="remark"
            label="备注"
            header-align="center"
            align="center"
          >
          </el-table-column>

          <el-table-column
            label="操作"
            header-align="center"
            align="center"
            width="150"
            v-if="props.allowEdit"
          >
            <template #default="scope">
              <el-button type="text" @click="onClickAddContract(scope.row.id)"
                >编辑</el-button
              >
              <el-button type="text" @click="deleteBatchHandle(scope.row.id)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <ActionContract ref="contractRef" @refreshDataList="getDataList()"></ActionContract>
  </el-card>
</template>
<script setup lang="ts">
import { ref, reactive } from "vue";
import dayjs from "dayjs";
import InfoTitle from "./InfoTitle.vue";
import ActionContract from "./ActionContract.vue";
import { IHooksOptions } from "@/hooks/interface";
import { useCrud } from "@/hooks";

const contractRef = ref();

const props = withDefaults(
  defineProps<{
    userId: number;
    allowEdit?: boolean;
  }>(),
  {
    userId: 0,
    allowEdit: false,
  }
);

const onClickAddContract = (id) => {
  contractRef.value.init(id);
};

const state: IHooksOptions = reactive({
  dataListUrl: "/work/userContract/info/list",
  deleteUrl: "/work/userContract/info",
  isPage: false,
  queryForm: {
    userId: props.userId,
  },
});

const { getDataList, deleteBatchHandle } = useCrud(state);
</script>
<style lang="scss" scoped>
.info {
  &-title {
    display: flex;
    align-items: center;
    &-divider {
      flex: 1;
      height: 1px;
      background-color: #f0f0f0;
      margin: 0 10px;
    }
  }
  &-content {
    margin-top: 20px;
  }
}
</style>
