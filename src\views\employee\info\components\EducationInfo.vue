<template>
  <el-card>
    <div class="info">
      <div class="info-title">
        <div class="info-title-text">
          <InfoTitle title="教育经历"></InfoTitle>
        </div>
        <div class="info-title-divider"></div>
        <div>
          <el-button type="text" @click="onHandleEducation()" v-if="props.allowEdit"
            >新增</el-button
          >
        </div>
      </div>
      <div class="info-content">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <fast-table-column
            prop="schoolType"
            label="学历"
            header-align="center"
            align="center"
            dict-type="highest_education"
            width="150"
          ></fast-table-column>
          <el-table-column
            prop="schoolName"
            label="院校"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="major"
            label="专业"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="startTime"
            label="开始时间"
            header-align="center"
            align="center"
            width="110"
          >
            <template #default="scope">
              {{ scope.row.startTime && dayjs(scope.row.startTime).format("YYYY-MM-DD") }}
            </template>
          </el-table-column>
          <el-table-column
            prop="endTime"
            label="结束时间"
            header-align="center"
            align="center"
            width="110"
          >
            <template #default="scope">
              {{ scope.row.endTime && dayjs(scope.row.endTime).format("YYYY-MM-DD") }}
            </template>
          </el-table-column>
          <fast-table-column
            prop="educationType"
            label="教育类型"
            header-align="center"
            align="center"
            dict-type="education_type"
          >
          </fast-table-column>
          <fast-table-column
            prop="isDegree"
            label="是否取得学位"
            header-align="center"
            align="center"
            dict-type="user_super_admin"
          ></fast-table-column>
          <el-table-column
            prop="remark"
            label="照片"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            header-align="center"
            align="center"
            width="150"
            v-if="props.allowEdit"
          >
            <template #default="scope">
              <el-button type="text" @click="onHandleEducation(scope.row.id)"
                >编辑</el-button
              >
              <el-button type="text" @click="deleteBatchHandle(scope.row.id)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <ActionEducation
          ref="educationRef"
          @refreshDataList="getDataList()"
        ></ActionEducation>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import dayjs from "dayjs";
import { reactive, ref } from "vue";
import InfoTitle from "./InfoTitle.vue";
import ActionEducation from "./ActionEducation.vue";
import { IHooksOptions } from "@/hooks/interface";
import { useCrud } from "@/hooks";

const props = withDefaults(
  defineProps<{
    userId: number;
    allowEdit?: boolean;
  }>(),
  {
    userId: 0,
    allowEdit: false,
  }
);

const educationRef = ref();

const state: IHooksOptions = reactive({
  dataListUrl: "/work/userEducation/info/list",
  deleteUrl: "/work/userEducation/info",
  isPage: false,
  queryForm: {
    userId: props.userId,
  },
});

const { getDataList, deleteBatchHandle, downloadHandle } = useCrud(state);

const onHandleEducation = (id) => {
  educationRef.value.init(id);
};
</script>
<style lang="scss" scoped>
.info {
  &-title {
    display: flex;
    align-items: center;
    &-divider {
      flex: 1;
      height: 1px;
      background-color: #f0f0f0;
      margin: 0 10px;
    }
  }
  &-content {
    margin-top: 20px;
  }
}
</style>
