<template>
  <el-dialog
    v-model="dialog.show"
    title="选择采购计划"
    :close-on-click-modal="false"
    draggable
    width="1000px"
    @close="onClose"
  >
    <div class="plan">
      <div class="plan-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="采购计划编号"
              clearable
              v-model="state.queryForm.packageNo"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="采购计划名称"
              clearable
              v-model="state.queryForm.packageName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onResetSearch()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="plan-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
          max-height="500px"
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-button type="primary" link @click="onClickSelect(scope.row)"
                >选择</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <!-- <div class="plan-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div> -->
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive, watch } from "vue";
interface IProps {
  show: boolean;
  from: string;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  from: "enter",
});

interface IDialog {
  show: boolean;
}

const dialog = reactive<IDialog>({
  show: false,
});

const state = reactive<IHooksOptions>({
  createdIsNeed: false,
  isPage: false,
  queryForm: {
    packageNo: "",
    packageName: "",
  },
  dataListUrl:
    props.from === "enter"
      ? "/purchase/package/reviewHasEnded"
      : props.from === "notice"
      ? "/purchase/winBidder/getToBeSentNotice"
      : "/purchase/winBulletin/packageList",
});
const { getDataList } = useCrud(state);

const onResetSearch = () => {
  state.queryForm.packageNo = "";
  state.queryForm.packageName = "";
  state.pageNo = 1;
  getDataList();
};

const emit = defineEmits<{
  (e: "on-close"): void;
  (e: "on-select", row: any): void;
}>();

const onClose = () => {
  dialog.show = false;
  emit("on-close");
};

const onClickSelect = (row: any) => {
  dialog.show = false;
  emit("on-select", row);
};

watch(
  () => props.show,
  () => {
    if (props.show) {
      dialog.show = true;
      getDataList();
    }
  }
);
</script>
<style lang="scss" scoped>
.plan {
  min-height: 450px;
  padding-bottom: 20px;
}
</style>
