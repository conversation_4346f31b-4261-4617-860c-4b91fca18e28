<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-06-18 15:21:32
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-07-04 15:31:10
-->
<template>
	<div>
		<WangEditor v-model="editorValue" placeholder="请输入..."></WangEditor>
		<!--		<WangEditor v-model="editorValue2" :disabled="true" placeholder=""></WangEditor>-->
	</div>


  <div class="outer">
    <div class="demo" id="demo" contenteditable="true" @keydown="keyIn"></div>
    <div class="select-person" id="selectPerson" v-show="showSelect" :style="sPersonPosi">
        <input type="text" id="searchPersonInput" v-model="personSearchText" @blur="missFocus()">
        <ul class="person-wrap">
            <li class="row" @click="sPersonDone({fullName:'所有人',id:'all'})">
                <div class="col-2">所有人</div>
            </li>
            <li class="row" @click="sPersonDone(item)" v-for="item in atList">
                <div class="col-2">{{item.fullName}}</div>
            </li>
        </ul>
    </div>
  </div>
</template>

<script lang="ts" setup name="DemoWangeditorIndex">
import WangEditor from '@/components/wang-editor/index.vue';
import { ref } from 'vue';

const editorValue = ref('<p>hjzt</p>')

// getSelection(window.getSelectio)：获取光标所在的区域（一个div或是一个textarea）；
// selection.getRangeAt：获取光标所在区域中光标选区的信息；
// range.setStart：设置光标选区的起始位置；
// range.setEnd：设置光标选区的结束位置；
// range.deleteContents：将光标选区选中的内容删除；
// range.insertNode：在光标选区中添加内容；
// selection.extend：将选区的焦点移动到一个特定的位置；
// selection.collapseToEnd：将当前的选区折叠到最末尾的一个点。
// range.startContainer ：返回 Range 开始的节点

const showSelect = ref(false)
const personSearchText = ref('')
const sPersonPosi = ref({})
const lastSelection = ref({})  // 保存选区和光标信息
const atList = [{
  fullName: '张三',
  id:1
},{
  fullName: '李四',
  id:2
}]
const keyIn = (e: any) => {
  // console.log(e)
  var selection = getSelection()
  var ele = $('#demo')
  if(e.code == 'Digit2' && e.shiftKey){  // @
    showSelect.value = true
    // 保存光标
    lastSelection.value = {
      range: selection.getRangeAt(0),
      offset: selection?.focusOffset,
      selection: selection
    }
    var offset = ele.caret('offset')
    sPersonPosi.value = {
      left: offset.left - 10 + 'px'
      // top: offset.top + 20 + 'px'
    }
    setTimeout(() => {
      $('#searchPersonInput')[0].focus() 
    });
  } else if (e.code == 'Backspace'){ // 删除
    // 1 ：由于在创建时默认会在 @xxx 后添加一个空格，
    // 所以当得知光标位于 @xxx 之后的一个第一个字符后并按下删除按钮时，
    // 应该将光标前的 @xxx 给删除
    // 2 ：当光标位于 @xxx 中间时，按下删除按钮时应该将整个 @xxx 给删除。
    var range = selection?.getRangeAt(0)
    var removeNode = null
    if(range?.startOffset <= 1 && range?.startContainer.parentElement?.className != 'at-text')
      removeNode = range?.startContainer.previousElementSibling
    if(range?.startContainer.parentElement?.className == 'at-text')
      removeNode = range.startContainer.parentElement
    if(removeNode)
      ele[0].removeChild(removeNode)
  }
}
const missFocus = (e: any) => {}
const sPersonDone = (person: any) => {
  showSelect.value = false
  var ele = $('#demo')[0]
  ele.focus()
  // 光标
  var selection = lastSelection.value.selection
  var range = lastSelection.value.range
  var textnode = range.startContainer
  // 删除@
  range.setStart(textnode, range.endOffset)
  range.setEnd(textnode, range.endOffset + 1)
  range.deleteContents()
  // 生成需要显示的内容，包括一个 span 和一个空格
  var spanNode1 = document.createElement('span')
  var spanNode2 = document.createElement('span')
  spanNode1.className = 'at-text'
  spanNode1.innerHTML = '@' + person.fullName
  spanNode2.innerHTML = '&nbsp;'
  // 将生成内容打包放在 Fragment 中，并获取生成内容的最后一个节点，也就是空格。
  var frag = document.createDocumentFragment(),node,lastNode
  frag.appendChild(spanNode1)
  while (node = spanNode2.firstChild) {
    lastNode = frag.appendChild(node)
  }
  // 将 Fragment 中的内容放入 range 中，并将光标放在空格之后。
  range.insertNode(frag)
  selection.extend(lastNode, 1)
  selection.collapseToEnd()
}



</script>
<style scoped>
.demo{
  border: 1px solid;
}
.outer{
  position: relative;
}
.select-person{
  position: absolute;
}
</style>