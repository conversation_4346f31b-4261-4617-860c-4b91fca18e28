import service from '@/utils/request'

//获取项目详情
export const getProjectInfo = (id: number) => {
  return service.get(`/purchase/project/${id}`)
}

interface IMaterialItem {
  materialId: number;
  materialNo: string;
  materialName: string;
  materialSpec: string;
  materialType: string;
  materialUnit: string;
  materialQuantity?: number;
  comment: string;
}

export interface IAttachmentItem {
  name: string;
  url: string;
  platform: string;
  size: number;
}

export interface IActionProject {
  id?: number
  projectName: string
  projectNo: string
  budget: number
  projectYear: string
  orgId:any
  projectContent: string
  attachmentList: IAttachmentItem[]
  tbProjectMaterialVOS: IMaterialItem[]
}

export const saveProject = (reqData: IActionProject) => {
  return service.post('/purchase/project', reqData)
}

export const updateProject = (reqData: IActionProject) => {
  return service.put('/purchase/project', reqData)
}

export const getProjectCode = (projectName: string) => {
  return service.get(`/purchase/project/getProjectCode?projectName=${projectName}`)
}

export const exportProjectMaterialList = (id: number) => {
  return service.get(`/purchase/projectMaterial/export/${id}`, {
    responseType: "blob"
  })
}

export const listOrgByCurrentUser = () => {
  return service.get(`/sys/org/listOrgByCurrentUser`)
}