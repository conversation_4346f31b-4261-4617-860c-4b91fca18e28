<template>
  <el-drawer
    v-model="state.visible"
    title="订单详情"
    :size="850"
    destroy-on-close
    @closed="onClosed"
  >
    <div class="detail">
      <el-form ref="refDetail" :model="state.dataForm" label-width="160px">
        <div class="detail-title">供应商信息</div>
        <div class="detail-content">
          <el-form-item prop="companyName" label="供应商名称">
            {{ state.dataForm.companyName }}
          </el-form-item>
          <el-form-item prop="contactName" label="联系人">
            {{ state.dataForm.contactName }}
          </el-form-item>
          <el-form-item prop="contactPhone" label="联系电话">
            {{ state.dataForm.contactPhone }}
          </el-form-item>
        </div>
        <template v-if="props.from !== 'purchase-invoice'">
          <div class="detail-title">订单信息</div>
          <div class="detail-content">
            <el-form-item prop="orderAmount" label="缴费金额（元）">
              {{ state.dataForm.orderAmount }}
            </el-form-item>
            <el-form-item prop="pay_type" label="支付方式">
              <span v-html="getDictLabelList('pay_type', state.dataForm.payType)"></span>
            </el-form-item>
            <el-form-item prop="payTime" label="缴费时间">
              {{ state.dataForm.payTime }}
            </el-form-item>
            <el-form-item prop="remarks" label="备注">
              {{ state.dataForm.remarks }}
            </el-form-item>
          </div>
        </template>

        <template v-if="(state.dataForm?.titleName ?? '') !== ''">
          <div class="detail-title">发票信息</div>
          <div class="detail-content">
            <el-form-item prop="titleType" label="抬头类型">
              <span
                v-html="getDictLabelList('title_type', state.dataForm.titleType + '')"
              ></span>
            </el-form-item>
            <el-form-item prop="titleName" label="发票抬头">
              {{ state.dataForm.titleName }}
            </el-form-item>
            <el-form-item prop="invoiceType" label="发票类型">
              <span
                v-html="getDictLabelList('invoice_type1', state.dataForm.invoiceType)"
              ></span>
            </el-form-item>
            <template v-if="state.dataForm.titleType == 0">
              <el-form-item prop="invoiceCode" label="统一纳税识别号">
                {{ state.dataForm.invoiceCode }}
              </el-form-item>
              <el-form-item prop="bankName" label="开户银行">
                {{ state.dataForm.bankName }}
              </el-form-item>
              <el-form-item prop="bankCode" label="银行账号">
                {{ state.dataForm.bankCode }}
              </el-form-item>
              <el-form-item prop="invoiceAddr" label="单位注册地址">
                {{ state.dataForm.invoiceAddr }}
              </el-form-item>
              <el-form-item prop="linkTel" label="电话">
                {{ state.dataForm.linkTel }}
              </el-form-item>
            </template>
            <el-form-item prop="landLineTel" label="手机号码">
              {{ state.dataForm.landLineTel }}
            </el-form-item>
            <el-form-item prop="emailCode" label="电子邮箱">
              {{ state.dataForm.emailCode }}
            </el-form-item>
          </div>
        </template>
      </el-form>
    </div>
  </el-drawer>
</template>
<script setup lang="ts">
import { getOrderDetail } from "@/api/supplier/order";
import { reactive, watch } from "vue";
import { getDictLabelList } from "@/utils/tool";

interface IProps {
  show: boolean;
  id: string;
  from: string;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  id: "",
  from: "supplier-order",
});

interface IDataForm {
  companyName: string;
  contactName: string;
  contactPhone: string;
  orderAmount: string;
  payType: string;
  payTime: string;
  remarks: string;
  titleType: number;
  titleName: string;
  invoiceType: string;
  invoiceCode: string;
  bankName: string;
  bankCode: string;
  invoiceAddr: string;
  linkTel: string;
  landLineTel: string;
  emailCode: string;
  isInvoice: number;
}

interface IState {
  visible: boolean;
  dataForm: IDataForm;
}

const state = reactive<IState>({
  visible: false,
  dataForm: {
    companyName: "",
    contactName: "",
    contactPhone: "",
    orderAmount: "",
    payType: "",
    payTime: "",
    remarks: "",
    titleType: 0,
    titleName: "",
    invoiceType: "",
    invoiceCode: "",
    bankName: "",
    bankCode: "",
    invoiceAddr: "",
    linkTel: "",
    landLineTel: "",
    emailCode: "",
    isInvoice: 1,
  },
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      state.visible = true;
      if (props.id !== "") {
        GetOrderDetail();
      }
    }
  }
);

const GetOrderDetail = () => {
  getOrderDetail(props.id).then((res) => {
    if (res.code === 0) {
      state.dataForm.companyName = res.data.companyName;
      state.dataForm.contactName = res.data.contactName;
      state.dataForm.contactPhone = res.data.contactPhone;
      state.dataForm.orderAmount = res.data.orderAmount;
      state.dataForm.payType = res.data.payType;
      state.dataForm.payTime = res.data.payTime;
      state.dataForm.remarks = res.data.remarks;
      state.dataForm.titleType = res.data.titleType;
      state.dataForm.titleName = res.data.titleName;
      state.dataForm.invoiceType = res.data.invoiceType;
      state.dataForm.invoiceCode = res.data.invoiceCode;
      state.dataForm.bankName = res.data.bankName;
      state.dataForm.bankCode = res.data.bankCode;
      state.dataForm.invoiceAddr = res.data.invoiceAddr;
      state.dataForm.linkTel = res.data.linkTel;
      state.dataForm.emailCode = res.data.emailCode;
      state.dataForm.landLineTel = res.data.landLineTel;
      state.dataForm.isInvoice = res.data.isInvoice;
    }
  });
};

const emit = defineEmits<{
  (e: "on-close"): void;
}>();

const onClosed = () => {
  emit("on-close");
};
</script>
<style lang="scss" scoped>
.detail {
  &-title {
    font-size: 16px;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 10px;
  }
  &-content {
    margin-top: 16px;
  }
}
</style>
