<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-06-24 11:22:30
 * @LastEditors: z<PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-03-19 14:41:44
-->
<template>
  <el-drawer
    v-model="state.show"
    :close-on-click-modal="false"
    title="审批详情"
    :size="850"
    class="detail_drawer"
  >
    <approvalcontent :id="state.id" :alldata="state.detailInfo" v-loading="state.loading">
      <template #center>
        <!-- 通用 -->
        <general :id="state.id" :alldata="state.detailInfo" v-if="state.type === 1" />
        <!-- 请假 -->
        <leave :id="state.id" :alldata="state.detailInfo" v-if="state.type === 2" />
        <!-- 费用报销 -->
        <expenseReim :id="state.id" :alldata="state.detailInfo" v-if="state.type === 3" />
        <!-- 付款申请 -->
        <payment :id="state.id" :alldata="state.detailInfo" v-if="state.type === 4" />
        <!-- 采购申请 -->
        <purchase :id="state.id" :alldata="state.detailInfo" v-if="state.type === 5" />
        <!-- 合同审批 -->
        <contract :id="state.id" :alldata="state.detailInfo" v-if="state.type === 6" />
      </template>
    </approvalcontent>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="openChangever(true)">更换审批人</el-button>
        <el-button type="primary" @click="openReason('审批同意')">同意</el-button>
        <el-button type="danger" @click="openReason('审批拒绝')">拒绝</el-button>
        <el-button @click="openChangever(false)">委托审批人</el-button>
        <el-button @click="onClickPrint" type="primary">打印</el-button>
        <!-- <el-button @click="openSelectUser">抄送</el-button> -->
      </div>
    </template>
  </el-drawer>

  <approvalreason
    v-if="state.approvalreasonShow"
    ref="approvalreasonRef"
    @refresh="refreshHd"
  ></approvalreason>
  <!-- 变更审批人 -->
  <changeapprover
    v-if="state.changeapproverShow"
    ref="changeapproverRef"
    @refresh="hadchange"
  ></changeapprover>
  <PrintDetail
    :show="print.show"
    :type="state.type"
    :alldata="state.detailInfo"
    @close="onClosePrint"
  ></PrintDetail>
</template>

<script setup lang="ts">
import { workGetStatusById } from "@/api/workbench";
import { nextTick, reactive, ref } from "vue";
import approvalcontent from "../components/approvalcontent.vue";
import approvalreason from "../components/approvalreason.vue";
import changeapprover from "../components/changeapprover.vue";
import contract from "../components/contract.vue";
import expenseReim from "../components/expenseReim.vue";
import general from "../components/general.vue";
import leave from "../components/leave.vue";
import payment from "../components/payment.vue";
import purchase from "../components/purchase.vue";
import PrintDetail from "../components/print-detail.vue";

interface IState {
  show: boolean;
  approvalreasonShow: boolean;
  changeapproverShow: boolean;
}
const approvalreasonRef = ref();
const changeapproverRef = ref();
const state = reactive<IState>({
  show: false,
  approvalreasonShow: false,
  changeapproverShow: false,
  loading: false,
  detailInfo: {},
  id: 0,
  type: 1,
});
const emit = defineEmits(["refresh"]);

const init = (obj) => {
  state.show = true;
  state.id = obj.id;
  state.type = obj.infoType;
  getDetailInfo();
};
const getDetailInfo = () => {
  state.loading = true;
  workGetStatusById(state.id).then((res) => {
    if (res.code == 0) {
      state.detailInfo = res.data;
      state.loading = false;
    } else {
      state.loading = false;
    }
  });
};
const openReason = (title) => {
  state.approvalreasonShow = true;
  nextTick(() => {
    approvalreasonRef.value.init(state.detailInfo, title);
  });
};
const openChangever = (bool) => {
  state.changeapproverShow = true;
  nextTick(() => {
    changeapproverRef.value.init(state.detailInfo, bool);
  });
};
const hadchange = () => {
  state.changeapproverShow = false;
  state.show = false;
  emit("refresh");
};
const refreshHd = () => {
  state.show = false;
  emit("refresh");
};

//#region 打印相关
const print = reactive({
  show: false,
});
const onClickPrint = () => {
  print.show = true;
};

const onClosePrint = () => {
  print.show = false;
};
//#endregion

defineExpose({
  init,
});
</script>

<style scoped></style>
