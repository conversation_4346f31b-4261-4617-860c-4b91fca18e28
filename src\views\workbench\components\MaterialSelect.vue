<template>
  <el-dialog
    v-model="state.show"
    title="选择采购清单"
    :close-on-click-modal="false"
    draggable
    width="1000px"
    @close="onClose"
  >
    <div class="project">
      <div class="project-search">
        <el-form :inline="true" :model="state">
          <el-form-item>
            <el-input
              placeholder="产品名称搜索"
              v-model="state.filterName"
              @keyup="searchEvent"
            >
            <template #suffix>
              <el-icon class="el-input__icon"><search /></el-icon>
            </template>
          </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="project-list">
        <vxe-table
          :data="state.list"
          border
          :column-config="{useKey:true}"
          :row-config="{keyField: 'id',useKey:true}"
          :scroll-y="{ enabled: true, gt: 20 }"
          @checkbox-all="selectChangeEvent"
          @checkbox-change="selectChangeEvent"
          :checkbox-config="{reserve: true}"
          max-height="500px"
          ref="vxeTableRef"
        >
          <vxe-column type="checkbox" width="60"></vxe-column>
          <vxe-column type="seq" width="60" title="序号"></vxe-column>
          <vxe-column field="materialName" title="产品名称" type="html"></vxe-column>
          <vxe-column field="materialType" title="产品型号（规格参数）"></vxe-column>
          <vxe-column field="surplusQuantity" title="剩余数量" width="80"></vxe-column>
          <vxe-column field="materialUnit" title="计量单位" width="80"></vxe-column>
          <vxe-column field="comment" title="备注"></vxe-column>
        </vxe-table>
      </div>
    </div>
    <template #footer>
      <el-button type="primary" @click="sureSelectEvent" :disabled="state.records.length<=0">
        确认选择
      </el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, watch, ref, nextTick,toRaw } from "vue";
import { useCrud } from "@/hooks";
import { Search } from '@element-plus/icons-vue'
import { IMaterialItem } from "../action.vue";
import { VXETable, VxeTableInstance, VxeTableEvents } from 'vxe-table'
interface IProps {
  show: boolean;
  list: IMaterialItem[],
  selectlist:IMaterialItem[],
}
interface IState {
  show: boolean;
  list: IMaterialItem[];
  tableList: IMaterialItem[];
  records: IMaterialItem[];
  filterName: string;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  list: [],
  selectlist: []
});
const state = reactive<IState>({
  show: false,
  list: [],   // 维护数据
  tableList: [],  // 原数据
  records:[], // 选中
  filterName: ''
});
const vxeTableRef = ref<VxeTableInstance<IMaterialItem>>()
const emit = defineEmits<{
  (e: "on-select", row: any): void;
}>();

watch(
  props,
  () => {
    if (props.show) {
      state.show = true;
      state.filterName = ''
      state.list = props.list
      state.tableList = props.list
      state.records = props.selectlist
      nextTick(()=>{
        if(vxeTableRef.value){
          vxeTableRef.value.clearCheckboxRow()
          vxeTableRef.value.setCheckboxRow(props.selectlist,true)
          if(props.selectlist.length<=0){
            vxeTableRef.value.clearCheckboxReserve()
          }
        }
      })
    }
  }
);

const selectChangeEvent = (row) => {
  // let records = vxeTableRef.value.getCheckboxRecords()  // 不包括保留的
  state.records = row.records.concat(row.reserves)
}

const sureSelectEvent = () => {
  state.show = false
  emit("on-select", state.records);
}
const onClose = ()=>{
  emit("on-close");
}

const searchEvent = () => {
  const filterVal = String(state.filterName).trim().toLowerCase()
  if (filterVal) {
    const filterRE = new RegExp(filterVal, 'gi')
    const searchProps = ['materialName']
    const rest = state.tableList.filter(item => searchProps.some(key => String(item[key]).toLowerCase().indexOf(filterVal) > -1))
    state.list = rest.map(row => {
      const item = Object.assign({}, row)
      // searchProps.forEach(key => {
      //   item[key] = String(item[key]).replace(filterRE, match => `<span class="keyword-lighten">${match}</span>`)
      // })
      return item
    })
  } else {
    state.list = state.tableList
  }
}
</script>
<style lang="scss" scoped>
.project {
  min-height: 450px;
}
.keyword-lighten {
  color: #000;
  background-color: #ff0;
}
</style>
