<template>
  <div class="notice">
    <div class="notice-search">
      <el-form
        ref="elFormRef"
        :inline="true"
        :model="state.queryForm"
        @keyup.enter="getDataList()"
      >
        <el-form-item prop="packageNo">
          <el-input
            v-model="state.queryForm.packageNo"
            placeholder="采购计划编号"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item prop="packageName">
          <el-input
            v-model="state.queryForm.packageName"
            placeholder="采购计划名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item prop="time">
          <el-date-picker
            v-model="state.queryForm.time"
            type="daterange"
            value-format="YYYY-MM-DD"
            range-separator="~"
            start-placeholder="报价开始时间"
            end-placeholder="报价截止时间"
            @change="queryTime"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()" type="primary">查询</el-button>
          <el-button @click="resetForm()">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="notice-list">
      <el-table v-loading="state.dataListLoading" :data="state.dataList" border>
        <el-table-column
          type="index"
          label="序号"
          header-align="center"
          align="center"
          width="70"
        ></el-table-column>
        <el-table-column
          prop="packageNo"
          label="采购计划编号"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="packageName"
          label="采购计划名称"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="title"
          label="公告名称"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="buyersName"
          label="采购单位"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="bidStartDate"
          label="报价开始时间"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="bidEndDate"
          label="报价截止时间"
          header-align="center"
          align="center"
        ></el-table-column>
        <fast-table-column
          prop="auditStatus"
          label="参与状态"
          header-align="center"
          align="center"
          dict-type="reg_audit_status"
        >
        </fast-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          header-align="center"
          align="center"
          width="180"
        >
          <template #default="scope">
            <el-button
              v-if="scope.row.auditStatus == 2"
              type="primary"
              link
              @click="participateHd(scope.row)"
            >
              修改参与信息
            </el-button>
            <el-button v-else type="primary" link @click="partviewHandle(scope.row.id)">
              查看参与信息
            </el-button>
            <el-button type="primary" link @click="viewHandle(scope.row.packageId)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="notice-page">
      <el-pagination
        :current-page="state.pageNo"
        :page-sizes="state.pageSizes"
        :page-size="state.pageSize"
        :total="state.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
      >
      </el-pagination>
    </div>
  </div>
  <!-- 参与 -->
  <participate-vue
    ref="participateVueRef"
    v-if="state.participateVueShow"
    @fresh-list="getDataList()"
  ></participate-vue>
  <participate-view
    ref="participateViewRef"
    v-if="state.participateViewShow"
  ></participate-view>
  <!-- 查看 -->
  <view-vue ref="viewVueRef" v-if="state.viewVueShow"></view-vue>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { nextTick, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import participateVue from "./components/participate.vue";
import participateView from "./components/participateview.vue";
import viewVue from "./view.vue";

const state: IHooksOptions = reactive({
  dataListUrl: "/purchase/registration/pageReg",
  queryForm: {
    regType: 1,
    packageNo: "",
    packageName: "",
    time: "",
    startTime: "",
    endTime: "",
  },
  viewVueShow: false,
  participateVueShow: false,
  participateViewShow: false,
});
const router = useRouter();
const viewVueRef = ref();
const participateVueRef = ref();
const participateViewRef = ref();
const elFormRef = ref();

const queryTime = (tt) => {
  if (tt instanceof Array) {
    state.queryForm.startTime = tt[0];
    state.queryForm.endTime = tt[1];
  } else {
    state.queryForm.startTime = state.queryForm.endTime = "";
  }
};
// 参与
const participateHd = (obj) => {
  state.participateVueShow = true;
  nextTick(() => {
    participateVueRef.value.init(obj, "edit");
  });
};
// 查看
const viewHandle = (id) => {
  state.viewVueShow = true;
  nextTick(() => {
    viewVueRef.value.init(id);
  });
};
// 查看参与
const partviewHandle = (id) => {
  state.participateViewShow = true;
  nextTick(() => {
    participateViewRef.value.init(id);
  });
};
const resetForm = () => {
  elFormRef.value.resetFields();
  state.queryForm.startTime = state.queryForm.endTime = "";
  getDataList(1);
};
const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

defineExpose({
  getDataList,
});
</script>
<style lang="scss" scoped>
.notice {
  &-list {
    margin-top: 16px;
  }
}
</style>
