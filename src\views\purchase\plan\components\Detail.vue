<template>
  <el-drawer
    v-model="state.show"
    :title="props.title"
    :size="950"
    class="plan_detail_drawer"
    @close="onClickClose"
  >
    <el-card>
      <div class="detail">
        <div class="detail_content">
          <el-tabs v-model="state.activeName" class="detail-tabs">
            <el-tab-pane label="基本信息" name="baseInfo">
              <DetailBaseInfo :baseInfo="state.baseInfo"></DetailBaseInfo>
            </el-tab-pane>
            <!-- <el-tab-pane label="审批记录" name="auditResult">
              <DetailAuditRecord :auditRecords="state.auditRecords"></DetailAuditRecord>
            </el-tab-pane> -->
          </el-tabs>
        </div>
      </div>
    </el-card>
    <el-card
      style="margin-top: 10px"
      v-if="state.activeName == 'baseInfo' && props.type == 'audit'"
    >
      <div class="audit">
        <DetailAudit :id="props.id" @audit-success="onAuditSuccess"></DetailAudit>
      </div>
    </el-card>
  </el-drawer>
</template>
<script setup lang="ts">
import {
  getAuditRecords,
  getProjectPlanInfo,
  IGetAuditRecords,
} from "@/api/purchase/plan";
import { reactive, watch, onMounted } from "vue";
import DetailBaseInfo from "./DetailBaseInfo.vue";
import DetailAuditRecord from "./DetailAuditRecord.vue";
import DetailAudit from "./DetailAudit.vue";
interface IProps {
  show: boolean;
  title: string;
  id?: number;
  type: string;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  title: "",
  type: "watch",
});

interface IMaterialItem {
  id: number;
  materialId: number;
  materialName: string;
  materialNo: string;
  materialSpec: string;
  materialType: string;
  materialUnit: string;
  materialQuantity: number;
  comment: string;
}

export interface IAttachmentItem {
  name: string;
  url: string;
  platform: string;
  size: number;
}

export interface IBaseInfo {
  id?: number;
  projectName: string;
  packageNo: string;
  packageName: string;
  packageBudget?: number;
  packageType: string;
  packageTypeLabel: string;
  packageMode: string;
  packageModeLabel: string;
  orgName: string;
  managerName: string;
  deliveryAddress: string;
  comment: string;
  attachmentList: IAttachmentItem[];
  materialVOS: IMaterialItem[];
}

export interface IAuditRecordsItem {
  id?: number;
  reviewedBy: string;
  auditResult: string;
  opinion: string;
  orgName: string;
  createTime: string;
  type: string;
}

interface IState {
  activeName: string;
  show: boolean;
  baseInfo: IBaseInfo;
  auditRecords: IAuditRecordsItem[];
}

const state = reactive<IState>({
  activeName: "baseInfo",
  show: false,
  baseInfo: {
    id: void 0,
    projectName: "",
    packageNo: "",
    packageName: "",
    packageBudget: void 0,
    packageType: "",
    packageTypeLabel: "",
    packageMode: "",
    packageModeLabel: "",
    orgName: "",
    managerName: "",
    deliveryAddress: "",
    comment: "",
    attachmentList: [],
    materialVOS: [],
  },
  auditRecords: [],
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      state.show = true;
      if (props.id) {
        GetProjectPlanInfo(props.id);
        GetAuditRecords(props.id);
      }
    }
  }
);

const GetProjectPlanInfo = (id: number) => {
  getProjectPlanInfo(id).then((res: any) => {
    if (res.code === 0) {
      let newBaseInfo = {
        id: res.data.id,
        projectName: res.data.projectName,
        packageNo: res.data.packageNo,
        packageName: res.data.packageName,
        packageBudget: res.data.packageBudget,
        packageType: res.data.packageType,
        packageTypeLabel: res.data.packageTypeLabel,
        packageMode: res.data.packageMode,
        packageModeLabel: res.data.packageModeLabel,
        orgName: res.data.orgName,
        deliveryAddress: res.data.deliveryAddress,
        managerName: res.data.managerName,
        comment: res.data.comment,
        attachmentList: (res.data?.attachmentList ?? []).map((item: any) => {
          return {
            name: item.name,
            url: item.url,
            platform: item.platform,
            size: item.size,
          };
        }),
        materialVOS: (res.data?.materialVOS ?? []).map((ele: any) => {
          return {
            id: ele?.id ?? void 0,
            materialId: ele?.materialId ?? void 0,
            materialName: ele?.materialName ?? "",
            materialNo: ele?.materialNo ?? "",
            materialSpec: ele?.materialSpec ?? "",
            materialType: ele?.materialType ?? "",
            materialUnit: ele?.materialUnit ?? "",
            materialQuantity: ele.materialQuantity,
            comment: ele?.comment ?? "",
          };
        }),
      };
      state.baseInfo = newBaseInfo;
    }
    console.log(res);
  });
};

const GetAuditRecords = (id: number) => {
  let reqData: IGetAuditRecords = {
    bizId: id,
    bizType: "net.yuan.purchase.vo.TbPackageVO",
  };
  getAuditRecords(reqData).then((res: any) => {
    if (res.code === 0) {
      let newAuditRecords = res.data.map((item: any) => {
        return {
          id: item.id,
          reviewedBy: item.reviewedBy,
          auditResult: item.auditResult,
          auditResultLabel: item.auditResultLabel,
          opinion: item.opinion,
          orgName: item.orgName,
          createTime: item.createTime,
          type: item.type,
        };
      });
      state.auditRecords = newAuditRecords;
    }
  });
};

const emit = defineEmits<{
  (e: "audit-success"): void;
  (e: "close"): void;
}>();

const onAuditSuccess = () => {
  state.show = false;
  emit("audit-success");
};

const onClickClose = () => {
  state.show = false;
  emit("close");
};
</script>
<style lang="scss">
.plan_detail_drawer {
  .el-drawer__body {
    padding: 14px !important;
    background: #f5f6fa !important;
  }
}
</style>
