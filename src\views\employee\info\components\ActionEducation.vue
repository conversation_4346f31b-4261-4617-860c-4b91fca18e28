<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    draggable
  >
    <div class="action-education">
      <el-form :model="dataForm" :rules="dataRule" ref="dataFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="schoolType" label="学历">
              <fast-select
                v-model="dataForm.schoolType"
                dict-type="highest_education"
                placeholder="学历"
              ></fast-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="schoolName" label="院校">
              <el-input v-model="dataForm.schoolName" placeholder="院校"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="major" label="专业">
              <el-input v-model="dataForm.major" placeholder="专业"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="startTime" label="开始时间">
              <el-date-picker
                v-model="dataForm.startTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="开始时间"
                style="width: 100%"
                :disabled-date="disabledStartTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="endTime" label="结束时间">
              <el-date-picker
                v-model="dataForm.endTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="结束时间"
                style="width: 100%"
                :disabled-date="disabledEndTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="educationType" label="教育类型">
              <fast-select
                v-model="dataForm.educationType"
                dict-type="education_type"
                placeholder="教育类型"
              ></fast-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="isDegree" label="是否取得学位">
              <fast-radio-group
                v-model="dataForm.isDegree"
                dict-type="user_super_admin"
              ></fast-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="epirationDate" label="照片附件">
              <div class="file-image">
                <div
                  class="image-item"
                  v-for="(item, index) in dataForm.attachmentList"
                  :key="index"
                >
                  <img :src="item.url" alt="" />
                  <div class="image-item-action">
                    <el-icon
                      class="action-icon"
                      style="margin-right: 10px"
                      @click="handleView(item.url)"
                      ><View
                    /></el-icon>
                    <el-icon class="action-icon" @click="handleRemove(index)"
                      ><Delete
                    /></el-icon>
                  </div>
                </div>
                <div class="image-upload">
                  <el-upload
                    :action="constant.uploadUrl"
                    :headers="{ Authorization: cache.getToken() }"
                    :before-upload="beforeUpload"
                    :on-success="handleSuccess"
                    :show-file-list="false"
                  >
                    <div class="upload-btn">
                      <svg-icon icon="icon-add" class="upload-icon"></svg-icon>
                    </div>
                  </el-upload>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="remark" label="备注">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-image-viewer
        v-if="viewer.show"
        @close="onCloseViewer"
        :url-list="[viewer.url]"
      />
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useEducationSubmitApi, getUserEducationInfo } from "@/api/employee";
import { ref, reactive } from "vue";
import { ElMessage, UploadProps } from "element-plus";
import cache from "@/utils/cache";
import constant from "@/utils/constant";
import { View, Delete } from "@element-plus/icons-vue";

const dataForm = reactive({
  id: "",
  schoolType: "",
  schoolName: "",
  major: "",
  startTime: "",
  endTime: "",
  educationType: "",
  isDegree: 0,
  attachmentList: [],
  remark: "",
});

const visible = ref(false);
const dataFormRef = ref();

const disabledStartTime = (time) => {
  if (time && dataForm.endTime) {
    return time.getTime() > new Date(dataForm.endTime).getTime();
  } else {
    return false;
  }
};

const disabledEndTime = (time) => {
  if (time && dataForm.startTime) {
    return time.getTime() < new Date(dataForm.startTime).getTime();
  } else {
    return false;
  }
};

const dataRule = ref({
  schoolType: [{ required: true, message: "学历不能为空", trigger: "blur" }],
  schoolName: [{ required: true, message: "院校不能为空", trigger: "blur" }],
});

const init = (id) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  // id 存在则为修改
  if (id) {
    GetUserEducationInfo(id);
  }
};

const GetUserEducationInfo = (id) => {
  getUserEducationInfo(id).then((response) => {
    Object.assign(dataForm, response.data);
  });
};

const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  let fileNameList = file.name.split(".");
  let fileType = fileNameList[fileNameList.length - 1];
  if (["jpg", "png", "jpeg"].indexOf(fileType) == -1) {
    ElMessage.error("请上传图片文件");
    return false;
  }
  return true;
};

const handleSuccess = (res: any) => {
  console.log(res);
  if (res.code === 0) {
    let newAttachmentList = JSON.parse(JSON.stringify(dataForm.attachmentList));
    newAttachmentList.push({
      platform: res.data.platform,
      url: res.data.url,
      name: res.data.name,
      size: res.data.size,
    });
    dataForm.attachmentList = newAttachmentList;
  }
};

const handleRemove = (index) => {
  dataForm.attachmentList.splice(index, 1);
};

const emit = defineEmits(["refreshDataList"]);

const submitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    useEducationSubmitApi(dataForm).then(() => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        },
      });
    });
  });
};

const viewer = reactive({
  show: false,
  url: "",
});

const handleView = (url) => {
  viewer.show = true;
  viewer.url = url;
};

const onCloseViewer = () => {
  viewer.show = false;
  viewer.url = "";
};

defineExpose({
  init,
});
</script>
<style lang="scss" scoped>
.file-image {
  display: flex;
  flex-wrap: wrap;
  .image-item {
    font-size: 0px;
    position: relative;
    line-height: 0px;
    img {
      width: 100px;
      height: 100px;
      border-radius: 5px;
      margin-right: 10px;
    }
    &-action {
      position: absolute;
      top: 0;
      left: 0;
      right: 10px;
      bottom: 0;
      z-index: 1;
      display: none;
      align-items: center;
      justify-content: center;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 5px;
      .action-icon {
        font-size: 16px;
        color: #fff;
        cursor: pointer;
      }
    }
  }
  .image-item:hover {
    .image-item-action {
      display: flex;
    }
  }

  .image-upload {
    .upload-btn {
      width: 98px;
      height: 98px;
      border: 1px dashed #d9d9d9;
      border-radius: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      .upload-icon {
        font-size: 20px;
        color: #8c939d;
      }
    }
  }
}
</style>
