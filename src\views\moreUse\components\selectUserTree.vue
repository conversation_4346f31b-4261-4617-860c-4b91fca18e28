<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-06-19 17:46:48
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-07-03 17:28:49
-->
<template>
  <el-dialog
    v-model="state.dialogVisible"
    title="选择成员"
    width="800"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <div class="layout" v-loading="state.loading">
      <div class="treebox">
        <el-input
          v-model="state.filterText"
          style="width: 240px"
          placeholder="输入成员名称"
        />
        <el-scrollbar max-height="400px">
          <el-tree
            ref="treeRef"
            :data="treeData"
            show-checkbox
            node-key="id"
            :check-strictly="true"
            :props="defaultProps"
            :filter-node-method="filterNode"
            @check-change="getCheckedNodesHd"
          />
        </el-scrollbar>
      </div>
      <div class="checked">
        <div class="checked-count">
          <span class="count-text">已选 {{ state.checkedLen }}</span>
          <el-link type="primary" @click="emptyChecked">清空</el-link>
        </div>
        <el-scrollbar max-height="400px">
          <div class="userline" v-for="(item, index) in state.checkedNodes">
            <svg-icon icon="icon-user" size="24" class="user-icon"></svg-icon>
            <span class="user-name">{{ item.name }}</span>
            <svg-icon
              icon="icon-close"
              class="user-del"
              size="18"
              @click="userDel(index)"
            ></svg-icon>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="sureSelect">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { getlistOrgUserTree } from "@/api/workbench";
import { ElMessage, ElTree } from "element-plus";
import { reactive, ref, toRaw, watch } from "vue";

interface Tree {
  [key: string]: any;
}

const state = reactive({
  dialogVisible: false,
  filterText: "",
  defaultTreeData: [],
  checkedNodes: [],
  checkedLen: 0,
  loading: false,
});
const treeData = ref<Tree[]>([]);
const treeRef = ref<InstanceType<typeof ElTree>>();

const defaultProps = {
  children: "children",
  label: "name",
  disabled: "status",
};
const emit = defineEmits(["selectUser"]);
watch(
  () => state.filterText,
  (val) => {
    treeRef.value!.filter(val);
  }
);

const init = (arr: any) => {
  state.filterText = "";
  state.dialogVisible = true;
  state.defaultTreeData = arr;
  state.checkedNodes = state.defaultTreeData;
  getUserTree();
};

const getUserTree = () => {
  state.loading = true;
  state.checkedLen = 0;
  getlistOrgUserTree().then((res) => {
    if (res.code == 0) {
      treeData.value = res.data;
      setTimeout(() => {
        state.loading = false;
        if (state.defaultTreeData.length > 0) {
          treeRef.value.setCheckedNodes(toRaw(state.defaultTreeData));
          state.checkedLen = state.defaultTreeData.length;
        }
      }, 0);
    } else {
      state.loading = false;
    }
  });
};

const filterNode = (value: string, data: Tree) => {
  if (!value) return true;
  return data.name.includes(value);
};
const getCheckedNodesHd = (now, check) => {
  // let nodes = treeRef.value!.getCheckedNodes()  // 无序的
  if (check) {
    if (!state.checkedNodes.some((item) => item.id === now.id)) {
      state.checkedNodes.push(now);
      state.checkedLen = state.checkedNodes.length;
    }
  } else {
    state.checkedNodes = state.checkedNodes.filter((item) => item.id !== now.id);
    state.checkedLen = state.checkedNodes.length;
  }
  // if(state.checkedNodes.length > 10){
  //   ElMessage.warning('超过可选上限10人')
  //   state.checkedNodes.pop()
  //   treeRef.value.setCheckedNodes(state.checkedNodes)
  // }
};
const userDel = (index) => {
  state.checkedNodes.splice(index, 1);
  state.checkedLen = state.checkedNodes.length;
  treeRef.value!.setCheckedNodes(state.checkedNodes);
};
const emptyChecked = () => {
  treeRef.value!.setCheckedNodes([]);
  state.checkedNodes = [];
  state.checkedLen = 0;
};
const sureSelect = () => {
  state.dialogVisible = false;
  emit("selectUser", state.checkedNodes);
  ElMessage.success("选择成功");
};

defineExpose({
  init,
});
</script>

<style scoped lang="scss">
.layout {
  display: flex;
  .treebox {
    width: 50%;
    border-right: 1px solid #ccc;
  }
  .checked {
    flex: 1;
    padding: 10px;
    &-count {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
    }
    .userline {
      padding: 8px;
      vertical-align: middle;
      background-color: #efefef;
      border-radius: 12px;
      margin-bottom: 8px;
      .user-icon {
        vertical-align: middle;
      }
      .user-del {
        vertical-align: middle;
        cursor: pointer;
        color: #409eff;
      }
      .user-name {
        width: 80%;
        display: inline-block;
        margin: 0 10px;
      }
    }
  }
}
:deep(.el-checkbox__input.is-disabled .el-checkbox__inner) {
  background-color: #dfdfdf;
}
</style>
