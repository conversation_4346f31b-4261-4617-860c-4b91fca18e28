<template>
  <div class="record">
    <template v-if="props.auditRecords.length > 0">
      <div class="record_item" v-for="(item, index) in props.auditRecords">
        <div class="left">
          <div class="left_circle">
            <div class="left_circle_content"></div>
          </div>
          <div class="left_divider" v-if="index !== props.auditRecords.length - 1">
            <div class="left_divider_content"></div>
          </div>
        </div>
        <div class="right">
          <div class="right_status" v-if="item.auditResult == '01'">
            <div class="right_status_icon">
              <el-icon class="icon"><Select /></el-icon>
            </div>
            <div class="right_status_text">审核通过</div>
          </div>
          <div class="right_status_no" v-else-if="item.type=='01'"></div>
          <div class="right_status_no" v-else>
            <div class="right_status_no_icon">
              <el-icon class="icon"><Close /></el-icon>
            </div>
            <div class="right_status_no_text">{{ item.auditResultLabel }}</div>
          </div>
          <div class="right_content">
            <div class="right_content_user">
              {{item.type=='00'?'审核人':'发起人'}}：{{ item.orgName }}-{{ item.reviewedBy }}
            </div>
            <div class="right_content_date">审核时间：{{ item.createTime }}</div>
            <div class="right_content_remark">审核意见：{{ item.opinion || "无" }}</div>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div class="record_no">
        <el-image :src="noAuditRecord" class="record_no_img"></el-image>
        <div class="record_no_text">暂无审核记录</div>
      </div>
    </template>
  </div>
</template>
<script setup lang="ts">
import { Select, Close } from "@element-plus/icons-vue";
import { IAuditRecordsItem } from "./Detail.vue";
import noAuditRecord from "@/assets/image/no_audit_record.png";

interface IProps {
  auditRecords: IAuditRecordsItem[];
}

const props = withDefaults(defineProps<IProps>(), {
  auditRecords: () => {
    return [];
  },
});
</script>
<style lang="scss" scoped>
.record {
  &_item {
    display: flex;
    .left {
      margin-right: 16px;
      &_circle {
        margin-top: 7px;
        &_content {
          width: 4px;
          height: 4px;
          border-radius: 50%;
          border: 2px solid #1890ff;
          background-color: #ffffff;
        }
      }
      &_divider {
        height: 100%;
        width: 6px;
        display: flex;
        flex-direction: column;
        align-items: center;
        &_content {
          width: 1px;
          height: 100%;
          background-color: #e4e4e4;
        }
      }
    }
    .right {
      &_status {
        display: flex;
        align-items: center;
        &_icon {
          width: 20px;
          height: 20px;
          background-color: #67c23a;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          .icon {
            color: #ffffff;
          }
        }
        &_text {
          margin-left: 6px;
        }
        &_no {
          display: flex;
          align-items: center;
          &_icon {
            width: 20px;
            height: 20px;
            background-color: #f56c6c;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            .icon {
              color: #ffffff;
            }
          }
          &_text {
            margin-left: 6px;
          }
        }
      }

      &_content {
        margin-top: 6px;
        margin-bottom: 10px;
        background-color: #f5f7fa;
        border-radius: 8px;
        padding: 10px;
        width: 350px;
        &_user {
          color: #030303;
        }
        &_date {
          margin-top: 6px;
          color: #767676;
        }
        &_remark {
          margin-top: 6px;
          color: #767676;
        }
      }
    }
  }
  &_no {
    display: flex;
    flex-direction: column;
    align-items: center;
    &_img {
      width: 160px;
      height: 160px;
    }
    &_text {
      margin-top: 6px;
      margin-bottom: 60px;
      font-size: 14px;
      color: #999999;
    }
  }
}
</style>
