<template>
  <el-card>
    <div class="track">
      <div class="track-info">
        <div class="info-left">
          <div class="info-left-title">项目信息</div>
          <div class="info-left-name">
            {{ state.packageInfo.packageName }}
          </div>
          <div class="info-left-base">
            <div class="info-left-base-item">
              <div class="info-left-base-item-label">采购方式：</div>
              <div class="info-left-base-item-value">
                <span
                  v-html="getDictLabelList('package_type', state.packageInfo.packageType)"
                ></span>
              </div>
            </div>
            <div class="info-left-base-divider"></div>
            <div class="info-left-base-item">
              <div class="info-left-base-item-label">报价开始时间：</div>
              <div class="info-left-base-item-value">
                {{ state.packageInfo.bidStartDate }}
              </div>
            </div>
            <div class="info-left-base-divider"></div>
            <div class="info-left-base-item">
              <div class="info-left-base-item-label">报价结束时间：</div>
              <div class="info-left-base-item-value">
                {{ state.packageInfo.bidEndDate }}
              </div>
            </div>
          </div>
        </div>
        <div class="info-right">
          <el-button type="primary" @click="onClickReturn">返回</el-button>
        </div>
      </div>
      <div class="track-divider"></div>
      <div class="track-content">
        <div class="track-content-menu">
          <template v-for="(item, index) in state.menuList">
            <div class="menu-item">
              <div class="menu-item-icon" v-if="item.isComplete">
                <el-icon><Check /></el-icon>
              </div>
              <div class="menu-item-icon-uncompltete" v-else>
                <svgIcon icon="icon-time-fill" class-name="svg-icon"></svgIcon>
              </div>
              <div
                :class="[
                  'menu-item-label',
                  state.checkedMenu == item.key ? 'menu-item-label-chekced' : '',
                ]"
                @click="onChangeMenu(item.key)"
              >
                {{ item.label }}
              </div>
            </div>
            <div class="menu-divider" v-if="index !== state.menuList.length - 1">
              <div class="menu-divider-content"></div>
            </div>
          </template>
        </div>
        <div class="track-content-main">
          <div
            v-if="
              state.checkedMenu === '01' ||
              state.checkedMenu === '06' ||
              state.checkedMenu === '07' ||
              state.checkedMenu === '08'
            "
            class="track-content-main-apply"
          >
            <TrackContent :id="state.id" :packageType="state.checkedMenu"></TrackContent>
          </div>
          <div class="track-content-main-apply" v-else-if="state.checkedMenu === '02'">
            <TrackChangeAnnouncement :id="state.id"></TrackChangeAnnouncement>
          </div>
          <div class="track-content-main-apply" v-else-if="state.checkedMenu === '03'">
            <TrackApplyInfo :id="state.id"></TrackApplyInfo>
          </div>
          <div class="track-content-main-apply" v-else-if="state.checkedMenu === '04'">
            <TrackQuotation :id="state.id"></TrackQuotation>
          </div>
          <div class="track-content-main-apply" v-else-if="state.checkedMenu === '05'">
            <TrackBidder :id="state.id"></TrackBidder>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { Check } from "@element-plus/icons-vue";
import { getDictLabelList } from "@/utils/tool";
import { getProjectPlanInfo, getTrackList } from "@/api/purchase/plan";
import { onMounted, reactive } from "vue";
import { useRoute, useRouter } from "vue-router";
import svgIcon from "@/components/svg-icon";
import TrackContent from "./components/TrackContent.vue";
import TrackApplyInfo from "./components/TrackApplyInfo.vue";
import TrackBidder from "./components/TrackBidder.vue";
import TrackQuotation from "./components/TrackQuotation.vue";
import TrackChangeAnnouncement from "./components/TrackChangeAnnouncement.vue";
import { closeTab } from "@/utils/tabs";

const route = useRoute();
const router = useRouter();

interface IPackageInfo {
  packageName: string;
  packageType: string;
  bidStartDate: string;
  bidEndDate: string;
}

interface IMenuItem {
  key: string;
  label: string;
  isComplete: boolean;
}

interface IState {
  id?: number;
  packageInfo: IPackageInfo;
  checkedMenu: string;
  menuList: IMenuItem[];
}

const state = reactive<IState>({
  id: void 0,
  packageInfo: {
    packageName: "",
    packageType: "",
    bidStartDate: "",
    bidEndDate: "",
  },
  checkedMenu: "01",
  menuList: [
    {
      key: "01",
      label: "采购公告",
      isComplete: false,
    },
    {
      key: "02",
      label: "变更公告",
      isComplete: false,
    },
    {
      key: "03",
      label: "报名参与信息",
      isComplete: false,
    },
    {
      key: "04",
      label: "报价信息",
      isComplete: false,
    },
    {
      key: "05",
      label: "确定中标人",
      isComplete: false,
    },
    {
      key: "06",
      label: "中标公告",
      isComplete: false,
    },
    {
      key: "07",
      label: "中标通知书",
      isComplete: false,
    },
    {
      key: "08",
      label: "采购合同",
      isComplete: false,
    },
  ],
});

onMounted(() => {
  state.id = route.query?.id ? ((route.query?.id as unknown) as number) : void 0;
  if (state.id) {
    getProjectPlanInfo(state.id).then((res: any) => {
      if (res.code === 0) {
        let newPackageInfo = {
          packageName: res.data.packageName,
          packageType: res.data.packageType,
          bidStartDate: res.data?.bidStartDate,
          bidEndDate: res.data?.bidEndDate,
        };
        state.packageInfo = newPackageInfo;
      }
    });
    getTrackList(state.id).then((res: any) => {
      if (res.code === 0) {
        let newMenuList: IMenuItem[] = JSON.parse(JSON.stringify(state.menuList));
        (res?.data ?? []).forEach((item: any) => {
          let menuIndex = newMenuList.findIndex((menu) => menu.key === item.trackingType);
          if (menuIndex !== -1) {
            newMenuList[menuIndex].isComplete = item.trackingStatus == 0 ? false : true;
          }
        });
        state.menuList = newMenuList;
      }
    });
  }
});

const onChangeMenu = (key: string) => {
  state.checkedMenu = key;
};

const onClickReturn = () => {
  closeTab(router, route);
};
</script>
<style lang="scss" scoped>
.track {
  &-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .info-left {
      &-title {
        font-weight: 650;
        font-size: 16px;
      }
      &-name {
        font-size: 16px;
        color: #333333;
        margin-top: 12px;
      }
      &-base {
        display: flex;
        align-items: center;
        margin-top: 12px;
        &-item {
          display: flex;
          align-items: center;
          &-label {
            font-size: 13px;
            color: #7f7f7f;
          }
          &-vale {
            font-size: 13px;
            color: #333333;
          }
        }
        &-divider {
          width: 1px;
          height: 20px;
          background-color: #d8d8d8;
          margin: 0 12px;
        }
      }
    }
  }
  &-divider {
    height: 1px;
    background-color: #d8d8d8;
    margin-top: 12px;
  }
  &-content {
    display: flex;
    &-menu {
      flex-shrink: 0;
      width: 180px;
      padding-top: 12px;
      border-right: 1px solid #d8d8d8;
      .menu-item {
        display: flex;
        align-items: center;
        &-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 16px;
          height: 16px;
          background-color: #409eff;
          color: #ffffff;
          border-radius: 50%;
          &-uncompltete {
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            :deep(.svg-icon) {
              font-size: 20px;
              color: #d7d7d7;
            }
          }
        }
        &-label {
          margin-left: 12px;
          display: flex;
          align-items: center;
          height: 36px;
          font-size: 13px;
          color: #333333;
          // background-color: #409eff;
          padding: 0 12px;
          cursor: pointer;
          &-chekced {
            background-color: #e9f1fd;
            color: #409eff;
          }
        }
        &-label:hover {
          color: #409eff;
        }
      }
      .menu-divider {
        width: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        &-content {
          width: 1px;
          height: 40px;
          background-color: #d8d8d8;
        }
      }
    }
    &-main {
      flex: 1;
      &-apply {
        margin: 20px;
      }
    }
  }
}
</style>
