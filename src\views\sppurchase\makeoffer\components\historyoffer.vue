<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-08 18:54:42
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-10 10:16:58
-->
<template>
  <el-dialog
    v-model="state.visible"
    title="查看历史报价"
    width="60%"
    top="40px"
    :close-on-click-modal="false"
  >
    <div class="notice-tabs">
      <el-tabs
        v-model="state.activeName"
        type="card"
        class="demo-tabs"
        @tab-click="handleClick"
      >
        <el-tab-pane
          :label="'第' + item + '轮'"
          :name="'name' + item"
          v-for="item in state.rowData.roundNo"
        ></el-tab-pane>
      </el-tabs>
    </div>
    <div class="viewbox">
      <view-vue
        ref="viewVueRef"
        :packId="state.rowData.id"
        :roundNum="state.rowData.roundNo"
        :="1"
      ></view-vue>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.visible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, ref, nextTick } from "vue";
import { usebulletinListApi } from "@/api/pnotice";
import { useCrud } from "@/hooks";
import viewVue from "./view.vue";

const state = reactive({
  visible: false,
  dataListLoading: false,
  dataForm: {},
  rowData: {},
  activeName: "name1",
});
const viewVueRef = ref();

const init = (row) => {
  state.visible = true;
  state.activeName = "name1";
  Object.assign(state.rowData, row);
  nextTick(() => {
    viewVueRef.value.init();
  });
};
// tab点击
const handleClick = (tab, event) => {
  let num = tab.paneName.slice(4);
  viewVueRef.value.getDetail(num);
};
defineExpose({
  init,
});
</script>
<style lang="scss" scoped></style>
