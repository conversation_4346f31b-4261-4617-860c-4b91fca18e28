<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-01 10:19:02
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-16 17:54:49
-->
<template>
  <el-card>
    <div class="action">
      <step1 v-if="state.stepNum==1"></step1>
      <step2 v-if="state.stepNum==2"></step2>
      <div class="action_btn">
        <Fragment v-if="state.stepNum==1">
          <el-button type="primary" plain @click="nextStep">下一步</el-button>
          <el-button type="primary" @click="saveTemp">暂存</el-button>
        </Fragment>
        <Fragment v-if="state.stepNum==2">
          <el-button type="primary" plain @click="preStep">上一步</el-button>
          <el-button type="primary" @click="saveTemp">提交</el-button>
        </Fragment>
        <el-button class="ml12" @click="closentab">返回</el-button>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { closeTab } from '@/utils/tabs'
import { useRoute, useRouter } from 'vue-router'
import { provide,reactive,ref,onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import step1 from './step1.vue'
import step2 from './step2.vue'

const route = useRoute()
const router = useRouter()
const state = reactive({
  nowId:'',
  stepNum:1, // 步骤
  saveFlag:false, // false 暂存 true 提交
  dataListLoading: false,
  materialList: [],
  fileList: [],
  uploadList:[]
})

onMounted(()=>{
  if(route.query.id){
    state.nowId = route.query.id
    getDetail()
  }
})
// 上一步
const preStep = ()=>{
  state.stepNum -- 
}
// 下一步
const nextStep = ()=>{
  state.stepNum ++ 
}
// 返回
const closentab = ()=>{
  closeTab(router, route)
}
// 暂存
const saveTemp = ()=>{
  state.saveFlag = false
  baseInfoRef.value.submitHandle()
}
// 提交
const saveSubmit = ()=>{
  state.saveFlag = true
  baseInfoRef.value.submitHandle()
}
// 值变化
const refreshProvide = (res)=>{
  if(res){
    if(state.saveFlag){
      useBulletinSaveSubmitApi(state.dataForm).then((res) => {
        if(res.code==0){
          ElMessage.success({
            message: '保存成功',
            duration: 500,
            onClose: () => {
              closentab()
            }
          })
        }
      })
    }else{
      useBulletinSaveTempApi(state.dataForm).then((res) => {
        if(res.code==0){
          ElMessage.success({
            message: '保存成功',
            duration: 500,
            onClose: () => {
              closentab()
            }
          })
        }
      })
    }
  }
}
// 查看
const getDetail = ()=>{
  usebulletinByIdApi(state.nowId).then((res) => {
    if(res.code == 0){
      Object.assign(state.dataForm, res.data)
      if(state.dataForm.bulletinAttachs){
        state.fileList = state.uploadList = state.dataForm.bulletinAttachs
      }
      getList(res.data.packageId)
    }
  })
}




</script>
<style lang="scss" scoped>
.action_title {
  margin: 20px 0;
}
.action_btn{
  text-align: center;
  margin: 20px auto;
}
.ml12{margin-left: 12px;}
</style>
