import service from "@/utils/request"

//获取成员树
export const getlistOrgUserTree = () => {
  return service.get('/sys/org/listOrgUserOa')
}
// 状态树
export const getlistOrgUserTreeWithStatus = (id, type) => {
  if (type) {
    return service.get('/sys/org/listOrgUserByFlowId?flowId=' + id + '&type=' + type)
  } else {
    return service.get('/sys/org/listOrgUserByFlowId?flowId=' + id)
  }
}
// 通用保存
export const workCommonFlowInfo = (data) => {
  return service.post('/work/common/flow/info', data)
}

// 审批查看
export const workGetStatusById = (id) => {
  return service.get('/work/flow/' + id)
}
// 评论
export const sendFlowComment = (data) => {
  return service.post('/work/flow/comment', data)
}
// 评论详情
export const getFlowComment = (id) => {
  return service.get('/work/flow/comment/' + id)
}
// 审批人
export const auditFlowPerson = (data) => {
  return service.post('/work/flow/Person/auditFlow', data)
}
// 更换审批人 （多）
export const changeFlowPersonBatch = (data) => {
  return service.post('/work/flow/Person', data)
}
// 撤销
export const cancelFlow = (data) => {
  return service.put('/work/flow', data)
}
// 根据流程id跟人员类型查询审批人
export const selectPersonListByFlowId = (id, type) => {
  return service.get('/work/flow/Person/selectPersonListByFlowId/' + id + '/' + type)
}
// 根据当前登录人查询请假类型
export const getOaHolidayRuleList = () => {
  return service.get('/work/holidayRule/getOaHolidayRuleList')
}
// 申请请假
export const workLeaveFlowInfo = (data) => {
  return service.post('/work/leave/flow/info', data)
}
// 计算时长
export const workLeaveDay = (data) => {
  return service.post('/work/leave/flow/info/getDay', data)
}
// 申请费用
export const workCostFlowInfo = (data) => {
  return service.post('/work/cost/flow/info', data)
}
// 申请付款
export const workPaymentFlowInfo = (data) => {
  return service.post('/work/payment/flow/info', data)
}
// 申请合同
export const workContractFlowInfo = (data) => {
  return service.post('/work/contract/flow/info', data)
}
// 申请合同
export const getHtCodeData = (name) => {
  return service.get('/work/flow/generateApprovalNumber/' + name)
}

export const workContractCheckCode = (data) => {
  return service.post('/work/contract/flow/info/checkCode', data)
}

//获取最近@用户
export const recentlyATUser = () => {
  return service.get('/work/flow/Person/selectPersonListByCurrentUser')
}