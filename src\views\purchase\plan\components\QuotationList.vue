<template>
  <div class="list">
    <div class="list-action">
      <div class="list-action-left">
        <el-button
          type="primary"
          @click="onStartQuotation()"
          v-if="props.roundStatus == 2 && props.from !== 'track'"
        >
          发起{{ props.roundNo + 1 }}轮报价
        </el-button>
        <!-- <el-button>导出报价数据</el-button> -->
        <el-button @click="onShowHistory()">查看历史报价</el-button>
      </div>
      <div class="list-action-right">
        <div class="right-max">
          本轮最高报价：
          <template v-if="props.maxOffer">
            <span class="right-max-price">{{ props.maxOffer }}</span>
            元
          </template>
          <template v-else>
            <span class="right-max-price">暂无报价</span>
          </template>
        </div>
        <div class="right-min">
          本轮最低报价：
          <template v-if="props.minOffer">
            <span class="right-min-price">{{ props.minOffer }}</span>
            元
          </template>
          <template v-else>
            <span class="right-min-price">暂无报价</span>
          </template>
        </div>
      </div>
    </div>
    <div class="list-table">
      <vxe-table
        :data="props.quotationList"
        border
        :scroll-y="{ enabled: true, gt: 20 }"
        max-height="500"
      >
        <vxe-column type="seq" width="60" title="序号"></vxe-column>
        <vxe-column field="bidderName" title="供应商"></vxe-column>
        <vxe-column field="quotationPrice" title="总报价（元）"></vxe-column>
        <vxe-column field="deliveryTime" title="交货时间"></vxe-column>
        <vxe-column field="createTime" title="报价时间"></vxe-column>
        <vxe-column field="attachmentList" title="附件">
          <template #default="{ row }">
            <div class="file-list">
              <div class="file-list-item" v-for="item in row?.attachmentList">
                <div class="file-list-item-text">
                  {{ item.name }}
                </div>
                <div class="file-list-item-action">
                  <el-icon class="action-icon" @click="onClickDownload(item)">
                    <Download />
                  </el-icon>
                </div>
              </div>
            </div>
          </template>
        </vxe-column>
        <vxe-column field="action" title="操作">
          <template #default="{ row }">
            <div>
              <el-button type="primary" link @click="onClickDetail(row)"
                >查看报价明细</el-button
              >
            </div>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <QuotationDetail
      :show="detail.show"
      :id="detail.id"
      :bidderName="detail.bidderName"
      :quotationPrice="detail.quotationPrice"
      @on-close="onCloseDetail"
    ></QuotationDetail>
    <MultipleOffer
      :show="start.show"
      :roundNo="props.roundNo"
      :packageId="props.packageId"
      @on-close="onCloseStart"
      @start-quotation="onSumbitStartQuotation"
    ></MultipleOffer>
    <HistoryQuotation
      :show="history.show"
      :roundNo="props.roundNo"
      :packageId="props.packageId"
      @on-close="onCloseHistory"
    ></HistoryQuotation>
  </div>
</template>
<script setup lang="ts">
import { reactive } from "vue";
import QuotationDetail from "./QuotationDetail.vue";
import HistoryQuotation from "./HistoryQuotation.vue";
import MultipleOffer from "./MultipleOffer.vue";
import service from "@/utils/request";
import { Download } from "@element-plus/icons-vue";

//#region props相关

interface IProps {
  from: string;
  packageId?: number;
  roundNo?: number;
  roundStatus?: number;
  maxOffer?: number;
  minOffer?: number;
  quotationList: IQuotationItem[];
}

const props = withDefaults(defineProps<IProps>(), {
  roundNo: 1,
  from: "quotation",
  quotationList: () => {
    return [];
  },
});
//#endregion

interface IAttachmentItem {
  name: string;
  url: string;
}

export interface IQuotationItem {
  id?: number;
  bidderName: string;
  quotationPrice?: number;
  deliveryTime: string;
  createTime: string;
  attachmentList: IAttachmentItem[];
}

const emit = defineEmits<{
  (e: "start-quotation"): void;
}>();

//#region 报价明细

interface IDetail {
  show: boolean;
  id?: number;
  bidderName: string;
  quotationPrice?: number;
}

const detail = reactive<IDetail>({
  show: false,
  id: void 0,
  bidderName: "",
  quotationPrice: 0,
});

const onClickDetail = (row: IQuotationItem) => {
  detail.id = row.id;
  detail.bidderName = row.bidderName;
  detail.quotationPrice = row.quotationPrice;
  detail.show = true;
};

const onCloseDetail = () => {
  detail.show = false;
  detail.id = void 0;
  detail.bidderName = "";
  detail.quotationPrice = 0;
};

//#endregion

//#region 发起多轮报价
interface IStart {
  show: boolean;
}

const start = reactive<IStart>({
  show: false,
});

const onStartQuotation = () => {
  start.show = true;
};

const onSumbitStartQuotation = () => {
  start.show = false;
  emit("start-quotation");
};

const onCloseStart = () => {
  start.show = false;
};

//#endregion

//#region 查看历史报价
interface IHistory {
  show: boolean;
}
const history = reactive<IHistory>({
  show: false,
});
const onShowHistory = () => {
  history.show = true;
};

const onCloseHistory = () => {
  history.show = false;
};

//#endregion

const onClickDownload = async (item: IAttachmentItem) => {
  const response = await service.get(item.url, {
    responseType: "blob", // important
  });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", item.name);
  document.body.appendChild(link);
  link.click();
};
</script>
<style lang="scss" scoped>
.list {
  &-action {
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-right {
      display: flex;
      align-items: center;
      .right-max {
        color: #333;
        font-size: 12px;
        margin-right: 16px;
        &-price {
          color: #f56c6c;
          font-size: 18px;
        }
      }
      .right-min {
        color: #333;
        font-size: 12px;
        &-price {
          color: #67c23a;
          font-size: 18px;
        }
      }
    }
  }
  &-table {
    margin-top: 16px;
  }
}

.file-list {
  &-item {
    display: flex;
    align-items: center;
    &-text {
      color: #409eff;
      cursor: pointer;
    }
    &-action {
      color: #545252;
      cursor: pointer;
      .action-icon {
        margin-left: 4px;
      }
    }
  }
}
</style>
