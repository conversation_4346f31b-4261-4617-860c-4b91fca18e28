<template>
  <div class="process">
    <div class="process_content">
      <el-form
        ref="dataFormRef"
        :model="state.dataForm"
        :rules="state.dataRules"
        label-width="100px"
      >
        <el-form-item prop="reviewedBy" label="添加审核人">
          <div style="display: flex; align-items: center; width: 100%">
            <el-input
              v-model="state.dataForm.reviewedBy"
              readonly
              placeholder="审核人"
              @click="onSelectUser"
              style="width: 250px"
            ></el-input>
            <el-button type="primary" style="margin-left: 10px" @click="onSelectUser">
              选择
            </el-button>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script setup lang="ts">
import { FormRules } from "element-plus";
import { nextTick, onMounted, reactive, watch, ref } from "vue";
import { IAuditInfo } from "../action.vue";

const dataFormRef = ref();

interface IProps {
  auditInfo: IAuditInfo;
}

const props = withDefaults(defineProps<IProps>(), {
  auditInfo: () => {
    return {
      reviewedById: void 0,
      reviewedBy: "",
    };
  },
});

interface IState {
  dataForm: IAuditInfo;
  dataRules: FormRules;
}

const state = reactive<IState>({
  dataForm: {
    reviewedById: void 0,
    reviewedBy: "",
  },
  dataRules: {
    reviewedBy: [{ required: true, message: "请选择审核人", trigger: "change" }],
  },
});

watch(
  () => props.auditInfo,
  () => {
    let newAuditInfo = JSON.parse(JSON.stringify(props.auditInfo));
    state.dataForm = newAuditInfo;
  }
);

const emit = defineEmits<{
  (e: "emit-ref", dataFormRef: any): void;
  (e: "on-select-user"): void;
}>();

const onSelectUser = () => {
  emit("on-select-user");
};

onMounted(() => {
  nextTick(() => {
    emit("emit-ref", dataFormRef.value);
  });
});
</script>
<style lang="scss" scoped>
.process {
  &_content {
    display: flex;
    align-items: center;
    margin-left: 20px;
    &_action {
      margin-left: 8px;
      &_btn {
        border-radius: 4px;
      }
    }
  }
}
</style>
