<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-02 16:25:21
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-16 16:11:46
-->
<template>
  <div class="action" v-loading="state.aloading" v-if="!state.tip">
    <div class="action_base">
      <el-descriptions class="margin-top" title="项目信息" :column="2" border>
        <el-descriptions-item label-align="right" width="150px" label="采购计划编号">{{
          state.dataForm.packageNo
        }}</el-descriptions-item>
        <el-descriptions-item label-align="right" width="150px" label="采购计划名称">{{
          state.dataForm.packageName
        }}</el-descriptions-item>
        <el-descriptions-item
          label-align="right"
          width="150px"
          label="本次预算金额（元）"
          >{{ state.dataForm.packageBudget }}</el-descriptions-item
        >
        <el-descriptions-item label-align="right" width="150px" label="报价开始时间">{{
          state.dataForm.offerStartTime
        }}</el-descriptions-item>
        <el-descriptions-item label-align="right" width="150px" label="项目状态">
          第 {{ state.dataForm.roundNum }} 轮报价
          <span v-if="state.dataForm.roundStatus == 1">进行中</span>
          <span v-if="state.dataForm.roundStatus == 2">结束</span></el-descriptions-item
        >
        <el-descriptions-item label-align="right" width="150px" label="报价截止时间">{{
          state.dataForm.offerEndTime
        }}</el-descriptions-item>
        <el-descriptions-item
          label-align="right"
          width="150px"
          label="报价说明"
          v-if="state.dataForm.roundNum > 1"
        >
          {{ state.dataForm.quotationDescription }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="action_inventory">
      <div class="action_inventory_title">
        <ContentTitle title="采购清单"></ContentTitle>
      </div>
      <div class="action_inventory_content">
        <div class="list_content">
          <el-table
            v-loading="state.dataListLoading"
            :data="state.dataForm.tbPackageMaterialVOS"
            :summary-method="getSummaries"
            show-summary
            max-height="250"
            border
            stripe
          >
            <el-table-column
              type="index"
              label="序号"
              header-align="center"
              align="center"
              width="70"
            ></el-table-column>
            <el-table-column
              prop="materialName"
              label="产品名称"
              header-align="center"
              align="center"
              width="180"
            ></el-table-column>
            <el-table-column
              prop="materialSpec"
              label="产品型号（规格参数）"
              header-align="center"
              width="180"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="materialUnit"
              label="计量单位"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="materialQuantity"
              label="采购数量"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              label="单价（元）"
              prop="unitPrice"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              label="总价（元）"
              prop="totalPrice"
              header-align="center"
              align="center"
              width="200"
            ></el-table-column>
            <el-table-column
              label="备注"
              prop="remark"
              header-align="center"
              align="center"
              width="200"
            ></el-table-column>
          </el-table>
        </div>
        <div class="form_content">
          <el-form
            ref="infoRef"
            :model="state.dataForm"
            label-width="140px"
            class="normalform"
          >
            <el-form-item label="优惠金额">
              <span>{{ state.dataForm.discountAmount }} 元</span>
            </el-form-item>
            <el-form-item label="报价金额">
              <span>{{ state.dataForm.quotationPrice }} 元</span>
            </el-form-item>
            <el-form-item label="交货期">
              <span
                >{{ state.dataForm.deliveryTime }} ~
                {{ state.dataForm.deliveryEndTime }}</span
              >
            </el-form-item>
            <el-form-item label="报价情况说明">
              <span>{{ state.dataForm.quotationComment }}</span>
            </el-form-item>
            <el-form-item label="附件">
              <Fragment v-for="i in state.dataForm.attachmentList">
                <el-link
                  type="primary"
                  class="block"
                  @click="downloadFile(i.url, i.name)"
                  >{{ i.name }}</el-link
                >
              </Fragment>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
  <div class="action_tip" v-else>
    {{ state.tip }}
  </div>
</template>
<script setup lang="ts">
import { reactive, onMounted } from "vue";
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import { useGetQuotationDetailByPackIdAndRoundNumApi } from "@/api/pnotice";
import { ElMessage } from "element-plus";
import { downloadFile } from "@/utils/tool";

const state = reactive({
  visible: false,
  opinion: "",
  nowId: "",
  aloading: false,
  dataForm: {},
  tip: "",
});

const props = defineProps({
  packId: "",
  roundNum: "",
  curRound: {
    default: 1,
    type: Number,
  },
});

onMounted(() => {
  getDetail(props.curRound);
});
const init = () => {
  getDetail(props.curRound);
};
const getDetail = (round = 1) => {
  state.aloading = true;
  useGetQuotationDetailByPackIdAndRoundNumApi(props.packId, round).then((res) => {
    if (res.code == 0) {
      if (res.data) {
        Object.assign(state.dataForm, res.data);
        state.tip = "";
      } else {
        state.tip = "未查询您本轮的报价信息!";
      }
      state.aloading = false;
    }
  });
};
// 合计
const getSummaries = (param) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 6) {
      const values = data.map((item) => Number(item[column.property]));
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `总报价金额（元）： ${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0)}`;
      } else {
        sums[index] = "";
      }
    } else {
      sums[index] = "";
    }
  });

  return sums;
};

defineExpose({
  getDetail,
  init,
});
</script>
<style lang="scss" scoped>
.action_title {
  margin-bottom: 15px;
}
::v-deep(.el-form-item--default) {
  margin-bottom: 6px;
}
.block {
  display: block;
}
.files {
  margin-top: 20px;
  display: flex;
  align-items: baseline;
  .label {
    margin-right: 5px;
  }
}
.btn {
  margin-left: 12px;
}
.tip {
  color: red;
  font-size: 12px;
  display: inline-block;
  width: 62%;
  text-align: left;
}
.action_base {
  margin-bottom: 20px;
}
.list_content {
  margin-bottom: 20px;
}
.form_content {
  width: 500px;
}
.action_tip {
  min-height: 500px;
  text-align: center;
}
</style>
