<template>

  <div class="detail-ctr" v-if="props.alldata.oaPaymentFlowInfoVO">
   <div class="detail-ctr-label">项目</div>
   <div class="detail-ctr-txt">{{props.alldata.oaPaymentFlowInfoVO.projectName}}</div>
   <div class="detail-ctr-label">付款事由</div>
   <div class="detail-ctr-txt">{{props.alldata.oaPaymentFlowInfoVO.flowRemark}}</div>
   <div class="detail-ctr-label">付款金额（元）</div>
   <div class="detail-ctr-txt">{{props.alldata.oaPaymentFlowInfoVO.amount}}</div>
   <div class="detail-ctr-label">收款账户名称</div>
   <div class="detail-ctr-txt">{{props.alldata.oaPaymentFlowInfoVO.receivingCompany}}</div>
   <div class="detail-ctr-label">银行账号</div>
   <div class="detail-ctr-txt">{{props.alldata.oaPaymentFlowInfoVO.bankNo}}</div>
   <div class="detail-ctr-label">开户行</div>
   <div class="detail-ctr-txt">{{props.alldata.oaPaymentFlowInfoVO.openingBank}}</div>
   <div class="detail-ctr-label">附件</div>
   <div class="detail-ctr-txt">
    <el-link class="file" :underline="false" type="primary" v-for="item in props.alldata.oaPaymentFlowInfoVO.attachmentList"
    @click="downloadFile(item.url,item.name)">{{item.name}}</el-link>
   </div>
  </div>
 
 </template>
 
 <script setup lang='ts'>
 
 import { downloadFile } from "@/utils/tool";
import { IView } from '../types';

 interface IProps {
  id: number;
  alldata: IView;
}

const props = withDefaults(defineProps<IProps>(), {
  id: 0,
  alldata: {
    oaPaymentFlowInfoVO: {
      projectId:'',
      projectName:'',
      amount:'',
      payee:'',
      receivingCompany:'',
      bankNo:'',
      openingBank:'',
      flowRemark: '',
      attachmentList: [],
    }
  }
})
 
 </script>
 
 <style scoped lang='scss'>
 .detail-ctr{
   &-label{
     color: #a2a2a2;
     padding: 0 12px 0 0;
     margin: 0 0 8px 0;
     line-height: 22px;
   }
   &-txt{
     margin-bottom: 15px;
     .file{display: block;line-height: 1.8;}
   }
 }
 </style>