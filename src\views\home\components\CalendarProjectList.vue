<template>
  <el-drawer v-model="state.show" title="查看" width="800px" @close="onClickClose">
    <div class="detail">
      <div class="detail-title">
        {{ dayjs(props.selectDate).format("MM月DD日") }}
      </div>
      <div class="detail-content">
        <el-timeline>
          <template v-for="item in state.detailList">
            <el-timeline-item :timestamp="item.key" placement="top" color="#1A90FE">
              <div class="detail-content-item-list">
                <div class="detail-content-item-list-item" v-for="listItem in item.list">
                  <div class="list-item-title">
                    {{ listItem.packageName }}
                  </div>
                  <div class="list-item-divider"></div>
                  <div class="list-item-time">
                    报价截止时间：{{ listItem.bidEndDate }}
                  </div>
                </div>
              </div>
            </el-timeline-item>
          </template>
        </el-timeline>
      </div>
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { getBuyerDetail } from "@/api/home";
import dayjs from "dayjs";
import { reactive, watch } from "vue";

interface IProps {
  selectDate?: Date;
  show: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      if (props.selectDate) {
        GetBuyerDetail();
      }
      state.show = props.show;
    }
  }
);

const GetBuyerDetail = () => {
  getBuyerDetail(
    dayjs(props.selectDate).format("YYYY"),
    dayjs(props.selectDate).format("MM"),
    dayjs(props.selectDate).format("DD")
  ).then((res: any) => {
    if (res.code === 0) {
      let detailList: any[] = [];
      let key = "";
      let list: any[] = [];
      res.data.forEach((item: any) => {
        if (key !== dayjs(item.bidEndDate).format("HH:mm:ss")) {
          if (key !== "") {
            detailList.push({
              key: key,
              list: list,
            });
          }
          key = dayjs(item.bidEndDate).format("HH:mm:ss");
          list = [];
          list.push(item);
        } else {
          list.push(item);
        }
      });
      if (key !== "") {
        detailList.push({
          key: key,
          list: list,
        });
      }
      console.log(detailList);
      state.detailList = detailList;
    }
  });
};

interface IState {
  show: boolean;
  detailList: any[];
}

const state = reactive<IState>({
  show: false,
  detailList: [],
});

const emit = defineEmits<{
  (e: "on-close"): void;
}>();

const onClickClose = () => {
  state.show = false;
  emit("on-close");
};
</script>

<style lang="scss" scoped>
.detail {
  &-title {
    font-size: 16px;
  }
  &-content {
    margin-top: 16px;
    &-item-list {
      &-item {
        border: 1px solid #d8d8d8;
        margin-bottom: 16px;
        .list-item-title {
          padding: 16px;
          font-size: 14px;
          color: #333333;
          font-weight: 700;
        }
        .list-item-divider {
          height: 1px;
          width: 100%;
          background-color: #d8d8d8;
        }
        .list-item-time {
          padding: 16px;
          font-size: 14px;
          color: #666666;
        }
      }
    }
  }
}
</style>
