.print {
  &-title {
    font-size: 20px;
    display: flex;
    justify-content: center;
  }

  &-people {
    font-size: 12px;
    color: rgb(153, 153, 153);
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
  }

  .content {
    margin-top: 20px;
    position: relative;

    &-item {
      font-size: 12px;
      position: relative;

      &-top {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        border-top: 1px solid #000000;
        z-index: 2000;
      }

      &-left {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        border-left: 1px solid #000000;
        z-index: 2000;
      }

      &-right {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        border-right: 1px solid #000000;
        z-index: 2000;
      }

      &-bottom {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-bottom: 1px solid #000000;
        z-index: 2000;
      }
    }

    &-two {
      display: flex;

      &-item {
        min-width: 0;
        flex: 1;
        flex-shrink: 0;
        display: flex;

        &-title {
          width: 140px;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 6px 10px;
          position: relative;
          overflow: hidden;

          &-left {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            border-left: 1px solid #000000;
            z-index: 2000;
          }

          &-image {
            position: absolute;
            top: -5px;
            left: -5px;
            right: -5px;
            bottom: -5px;
            z-index: 1;
          }

          &-text {
            z-index: 2;
          }
        }

        &-value {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 6px 10px;
          position: relative;

          &-left {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            border-left: 1px solid #000000;
            z-index: 2000;
          }

          &-main {
            word-break: break-all;
          }
        }
      }
    }

    &-title {
      padding: 6px 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 700;
    }

    &-titleleft {
      padding: 6px 10px;
      display: flex;
      align-items: center;
      justify-content: flex-start;
    }

    &-one {
      display: flex;

      &-title {
        width: 140px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 6px 10px;
        position: relative;
        overflow: hidden;

        &-left {
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          border-left: 1px solid #000000;
          z-index: 2000;
        }

        &-image {
          position: absolute;
          top: -5px;
          left: -5px;
          right: -5px;
          bottom: -5px;
          z-index: 1;
        }

        &-text {
          position: relative;
          z-index: 2;
        }
      }

      &-value {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 6px 10px;
        position: relative;

        &-main {
          &-html {
            :deep(img) {
              max-width: 100%;
            }

            :deep(table) {
              width: 100%;
              border-collapse: collapse;
              border: 1px solid #000;

              th {
                border: 1px solid #000;
              }

              td {
                border: 1px solid #000;
              }
            }
          }

          &-more {
            &-item {
              margin-bottom: 6px;
            }

            &-item:last-child {
              margin-bottom: 0;
            }
          }
        }

        &-left {
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          border-left: 1px solid #000000;
          z-index: 2000;
        }
      }
    }

    &-empty {
      padding: 6px 10px;
    }

    &-emptytwo {
      display: flex;

      &-item {
        flex: 1;
        flex-shrink: 0;
        padding: 6px 10px;
        position: relative;
        overflow: hidden;

        &-left {
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          border-left: 1px solid #000000;
          z-index: 2000;
        }
      }
    }

    &-table {
      display: flex;

      &-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 6px 10px;
        position: relative;
        overflow: hidden;

        &-left {
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          border-left: 1px solid #000000;
          z-index: 2000;
        }

        &-image {
          position: absolute;
          top: -5px;
          left: -5px;
          right: -5px;
          bottom: -5px;
          z-index: 1;
        }

        &-text {
          z-index: 2;
        }
      }
    }

    &-more {
      display: flex;

      &-title {
        width: 140px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 6px 10px;
        position: relative;
        overflow: hidden;

        &-left {
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          border-left: 1px solid #000000;
          z-index: 2000;
        }

        &-image {
          position: absolute;
          top: -5px;
          left: -5px;
          right: -5px;
          bottom: -5px;
          z-index: 1;
        }

        &-text {
          position: relative;
          z-index: 2;
        }
      }

      &-value {
        flex: 1;
        position: relative;

        &-left {
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          border-left: 1px solid #000000;
          z-index: 2000;
        }

        &-item {
          padding: 6px 10px;
          position: relative;

          &-top {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            border-top: 1px solid #000000;
            z-index: 2000;
          }
        }
      }
    }

  }

  &-id {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;

    &-main {
      font-size: 12px;
      color: rgb(153, 153, 153);
    }

    &-time {
      font-size: 12px;
      color: rgb(153, 153, 153);
    }
  }
}