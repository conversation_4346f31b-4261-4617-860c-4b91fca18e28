<template>
  <el-dialog
    v-model="state.show"
    :title="props.id ? '修改' : '新增'"
    :close-on-click-modal="false"
    draggable
    @close="onClose"
  >
    <el-form
      ref="dataFormRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="120px"
      @keyup.enter="onSubmit()"
    >
      <el-form-item prop="name" label="名称">
        <el-input v-model="state.dataForm.name" placeholder="名称"></el-input>
      </el-form-item>
      <el-form-item prop="pid" label="上级分类">
        <el-tree-select
          v-model="state.dataForm.pid"
          :data="state.categoryList"
          value-key="id"
          check-strictly
          :render-after-expand="false"
          :props="{ label: 'name', children: 'children' }"
          style="width: 100%"
          clearable
          @node-click="onChangeParentCategory"
        />
      </el-form-item>
      <el-form-item prop="sort" label="排序">
        <el-input-number
          v-model="state.dataForm.sort"
          controls-position="right"
          :min="0"
          label="排序"
        ></el-input-number>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onClose">取消</el-button>
      <el-button type="primary" @click="onSubmit()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from "vue";
import { ElMessage, FormRules } from "element-plus";
import {
  getCategoryList,
  addCategory,
  updateCategory,
  IActionCategory,
} from "@/api/material/category";

const dataFormRef = ref();

interface IDataForm {
  name: string;
  pid?: number;
  parentName?: string;
  sort: number;
}

interface IProps {
  show: boolean;
  id?: number;
  info?: IDataForm;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  info: () => {
    return {
      name: "",
      sort: 0,
    };
  },
});

interface ICategoryItem {
  id: number;
  name: string;
  pid?: number;
  parentName?: string;
  children: ICategoryItem[];
}

interface IState {
  show: boolean;
  dataForm: IDataForm;
  dataRules: FormRules;
  categoryList: ICategoryItem[];
}

const state = reactive<IState>({
  show: false,
  dataForm: {
    name: "",
    pid: void 0,
    parentName: void 0,
    sort: 0,
  },
  dataRules: {
    name: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  },
  categoryList: [],
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      state.show = true;
      GetCategoryList();
      let newDataForm = JSON.parse(JSON.stringify(props.info));
      state.dataForm = newDataForm;
    }
  }
);

const GetCategoryList = () => {
  getCategoryList().then((res) => {
    console.log(res);
    state.categoryList = res.data.map((item: ICategoryItem) => {
      return {
        id: item.id,
        name: item.name,
        children: [],
      };
    });
  });
};

const emit = defineEmits<{
  (e: "on-close"): void;
  (e: "on-submit"): void;
}>();

const onClose = () => {
  state.show = false;
  emit("on-close");
};

// 表单提交
const onSubmit = () => {
  dataFormRef.value.validate((valid: Boolean) => {
    if (valid) {
      let reqData: IActionCategory = {
        id: props.id,
        name: state.dataForm.name,
        pid: state.dataForm.pid,
        parentName: state.dataForm.parentName,
        sort: state.dataForm.sort,
      };
      console.log(props.id);
      (props.id ? updateCategory(reqData) : addCategory(reqData)).then((res: any) => {
        if (res.code === 0) {
          ElMessage.success("操作成功!");
          state.show = false;
          emit("on-submit");
        } else {
          ElMessage.error(res.msg);
        }
      });
    }
  });
};

const onChangeParentCategory = (value: ICategoryItem) => {
  state.dataForm.pid = value.id;
  state.dataForm.parentName = value.name;
};
</script>

<style lang="scss" scoped>
.org-list {
  ::v-deep(.el-input__inner) {
    cursor: pointer;
  }
  ::v-deep(.el-input__suffix) {
    cursor: pointer;
  }
}
</style>
