<template>
  <div class="action-address">
    <el-dialog
      v-model="visible"
      :title="!dataForm.id ? '新增' : '修改'"
      :close-on-click-modal="false"
      draggable
      width="550px"
      destroy-on-close
    >
      <div class="action-address-content">
        <!-- <div class="content-search">
          <el-input placeholder="请输入地址关键字"></el-input>
        </div> -->
        <div class="content-map" id="content-map"></div>
        <div class="content-form">
          <el-form
            ref="dataFormRef"
            :model="dataForm"
            :rules="dataRule"
            label-width="120px"
          >
            <el-form-item label="考勤地点名称" prop="name">
              <el-input v-model="dataForm.name" placeholder="考勤地点"></el-input>
            </el-form-item>
            <el-form-item label="详细地址" prop="attendanceLocation">
              {{ dataForm.attendanceLocation }}
            </el-form-item>
            <el-form-item label="打卡范围" prop="checkInScope">
              <fast-select
                v-model="dataForm.checkInScope"
                dict-type="check_in_scope"
              ></fast-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <template #footer>
        <el-button @click="visible = false">取消</el-button>
        <el-button type="primary" @click="submitHandle()">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, nextTick, shallowReactive } from "vue";
import { onMounted } from "vue";

import { jsonp } from "vue-jsonp";

const visible = ref(false);
const map = shallowReactive({
  map: undefined,
  marker: undefined,
  circle: undefined,
});

const operationType=ref('add')
const editIndex=ref(0)
const dataForm = reactive({
  id: "",
  name: "",
  attendanceLocation: "",
  longitude: undefined,
  latitude: undefined,
  checkInScope: 100,
});

const dataRule = {
  name: [{ required: true, message: "考勤地点名称不能为空", trigger: "blur" }],
  attendanceLocation: [{ required: true, message: "详细地址不能为空", trigger: "blur" }],
  checkInScope: [{ required: true, message: "打卡范围不能为空", trigger: "blur" }],
};

const dataFormRef = ref();

const emit = defineEmits(["submitData"]);

const submitHandle = () => {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      visible.value = false;
      emit("submitData", dataForm,operationType.value,editIndex.value);
    }
  });
};

const initMap = (type:string,row:any) => {
  nextTick(() => {
    let center = new TMap.LatLng(type==='edit'?row.latitude:38.047872, type==='edit'?row.longitude:114.456076);
    map.map = new TMap.Map(document.getElementById("content-map"), {
      center: center, //设置地图中心点坐标
      zoom: 16, //设置地图缩放级别
    });
    map.marker = new TMap.MultiMarker({
      map: map.map,
      styles: {
        myStyle: new TMap.MarkerStyle({
          width: 25, // 点标记样式宽度（像素）
          height: 35, // 点标记样式高度（像素）
          src: "./icon.png", //图片路径
          anchor: { x: 16, y: 32 }, //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
        }),
      },
      geometries: [
        {
          id: "marker-1",
          style: "myStyle",
          position: center,
          properties: {
            title: "我的位置",
          },
        },
      ],
    });

    map.circle = new TMap.MultiCircle({
      map: map.map,
      styles: {
        circle: new TMap.CircleStyle({
          color: "rgba(41,91,255,0.16)",
          showBorder: true,
          borderColor: "rgba(41,91,255,1)",
          borderWidth: 2,
        }),
      },
      geometries: [
        {
          id: "circle-1",
          rank: 1,
          styleId: "circle",
          center: center, //圆形中心点坐标
          radius: Number(dataForm.checkInScope), //半径（单位：米）
        },
      ],
    });
    map.map.on("click", listenClickMap);
    if(type==='add'){
      dataForm.longitude = 114.456076;
      dataForm.latitude = 38.047872;
    }
    computeMap();
  });
};

const listenClickMap = (value) => {
  dataForm.longitude = value.latLng.lng.toFixed(6);
  dataForm.latitude = value.latLng.lat.toFixed(6);
  computeMap();
};

const computeMap = () => {
  let newPoint = new TMap.LatLng(dataForm.latitude, dataForm.longitude);
  map.marker.updateGeometries([
    {
      id: "marker-1",
      position: newPoint,
    },
  ]);
  map.circle.updateGeometries([
    {
      id: "circle-1",
      rank: 1,
      styleId: "circle",
      radius: Number(dataForm.checkInScope),
      center: newPoint,
    },
  ]);
  jsonp("https://apis.map.qq.com/ws/geocoder/v1/?", {
    location: dataForm.latitude + "," + dataForm.longitude,
    key: "TTVBZ-Y5CLX-SCL4I-T36PC-MOODS-W5FT7",
    output: "jsonp",
  }).then((data) => {
    console.log(data);
    dataForm.attendanceLocation = data.result.address;
  });
};

const init = (type:string,row:any,index:number) => {
  visible.value = true;
  dataForm.id = "";
  initMap(type,row);

  let data= {
    id: "",
    name: "",
    attendanceLocation: "",
    longitude: undefined,
    latitude: undefined,
    checkInScope: 100,
  }
  Object.assign(dataForm, data);

  // 重置表单数据
  // if (dataFormRef.value) {
  //   dataFormRef.value.resetFields();
  // }
  operationType.value=type
  editIndex.value=index
  // id 存在则为修改
  if (type==='edit') {
    Object.assign(dataForm, row);
    // GetContractWorkInfo(id);
  }
};

defineExpose({
  init,
});
</script>
<style lang="scss" scoped>
.action-address {
  &-content {
    .content-map {
      height: 400px;
    }
    .content-form {
      margin-top: 20px;
    }
  }
}
</style>
