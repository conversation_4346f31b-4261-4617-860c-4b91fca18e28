<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-03-19 14:34:29
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-06-11 14:19:38
-->
<template>
  <div v-if="appStore.sidebarOpened" class="sidebar-logo">
    <!-- <el-avatar src="./favicon.ico"></el-avatar>
		<span class="logo-title"> {{ $t('app.logoText') }}</span> -->
    <a :href="webHref" target="_blank" class="alink"
      ><el-image :src="logoPic" style="width: 120px; margin-top: 20px"></el-image
    ></a>
  </div>
  <div v-else class="sidebar-logo sidebar-logo-expend">
    <!-- <span>{{ $t('app.miniLogoText') }}</span> -->
    <a :href="webHref" target="_blank" class="alink"
      ><el-image :src="logoPic" style="width: 120px; margin-top: 20px"></el-image
    ></a>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import constant from "@/utils/constant";
import logoPic from "@/assets/logo1.png";
import { useAppStore } from "@/store/modules/app";
const appStore = useAppStore();

const webHref = ref("");

const webHrefHd = () => {
  webHref.value = constant.env.DEV
    ? "http://test-jczl.hebhzjt.com:7898/yuan-cms/webfile/"
    : "https://www.eczl.com.cn";
};

webHrefHd();
</script>

<style lang="scss" scoped>
.sidebar-logo {
  width: 230px !important;
  height: var(--theme-header-height);
  line-height: var(--theme-header-height);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: rgb(0 21 41 / 2%) 0 1px 4px;
  color: var(--theme-logo-text-color);
  font-size: 18px;

  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;

  .alink {
    margin-top: 12px;
    display: block;
  }
  .el-avatar {
    width: 25px;
    height: 25px;
  }
  .logo-title {
    margin-left: 10px;
  }
}
</style>
