<!--
 * @Descripttion: 
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-01-13 14:14:11
 * @LastEditors: z<PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-01-13 15:16:15
 * @Introduce: 
-->
<template>
  <div class="audit">
    <div class="audit-table">
      <el-table show-overflow-tooltip :data="state.dataList" border :max-height="350">
        <el-table-column
          type="index"
          label="序号"
          header-align="center"
          align="center"
          width="70"
        ></el-table-column>
        <el-table-column
          prop="reviewedBy"
          label="审核人（用户名）"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="createTime"
          label="审核时间"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="opinion"
          label="审核意见"
          header-align="center"
          align="center"
        ></el-table-column>
        <fast-table-column
          prop="auditResult"
          label="审核状态"
          dict-type="audit_result"
        ></fast-table-column>
      </el-table>
    </div>
  </div>
</template>
<script setup lang="ts">
import { getAuditRecords, IGetAuditRecords } from "@/api/purchase/plan";
import { watch, reactive } from "vue";

interface IProps {
  bizId?: number;
}

const props = withDefaults(defineProps<IProps>(), {
  bizId: void 0,
});

interface IState {
  queryForm: {
    bizId?: number;
    bizType: string;
  };
  dataList: any[];
}

const state = reactive<IState>({
  queryForm: {
    bizId: void 0,
    bizType: "net.yuan.system.vo.SysPurchaserBidderVO",
  },
  dataList: [],
});

watch(
  () => props.bizId,
  () => {
    if (props.bizId) {
      state.queryForm.bizId = props.bizId;
      GetAuditRecords();
    }
  },
  { immediate: true }
);

// onMounted(() => {
//   GetAuditRecords();
// });

const GetAuditRecords = () => {
  let reqData: IGetAuditRecords = {
    bizId: state.queryForm.bizId,
    bizType: state.queryForm.bizType,
  };
  getAuditRecords(reqData).then((res: any) => {
    if (res.code == 0) {
      state.dataList = res.data;
    }
  });
};
</script>
<style lang="scss" scoped></style>
