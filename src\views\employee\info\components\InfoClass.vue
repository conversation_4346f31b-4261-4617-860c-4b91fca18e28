<template>
  <el-card>
    <div class="info">
      <div class="info-title">
        <InfoTitle title="员工信息"></InfoTitle>
      </div>
      <div class="info-list">
        <div
          :class="['item', activeClass === item.key && 'active']"
          v-for="item in state.actionList"
          :key="item.key"
          @click="onClickClass(item.key)"
        >
          <div class="item-point"></div>
          <div class="item-text">{{ item.text }}</div>
          <div class="item-right" v-if="activeClass === item.key">
            <svg-icon icon="icon-xiangyou" color="#1890ff"></svg-icon>
          </div>
        </div>
      </div>
    </div>
  </el-card>
</template>
<script setup>
import { reactive, defineEmits } from "vue";
import InfoTitle from "./InfoTitle.vue";

const props = defineProps({
  activeClass: {
    type: String,
    default: "company",
  },
});

const emit = defineEmits(["click-class"]);

const state = reactive({
  actionList: [
    {
      key: "company",
      text: "工作信息",
    },
    {
      key: "base",
      text: "个人信息",
    },
    {
      key: "education",
      text: "教育经历",
    },
    {
      key: "work",
      text: "工作经历",
    },
    {
      key: "contract",
      text: "合同信息",
    },
    {
      key: "file",
      text: "附件存档",
    },
  ],
});

const onClickClass = (key) => {
  console.log(key);
  emit("click-class", key);
};
</script>
<style lang="scss" scoped>
.info {
  &-list {
    margin-top: 16px;
    .item {
      display: flex;
      align-items: center;
      line-height: 40px;
      cursor: pointer;
      &-point {
        width: 4px;
        height: 4px;
        background-color: #333;
        border-radius: 50%;
        margin-right: 6px;
      }
      &-text {
        flex: 1;
      }
    }
    .active {
      color: #1890ff;
      .item-point {
        background-color: #1890ff;
      }
    }
  }
}
</style>
