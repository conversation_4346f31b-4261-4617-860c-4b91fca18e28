<template>
  <div class="base">
    <el-card>
      <div class="base-info">
        <div class="base-info-title">
          <div class="base-info-title-text">
            <InfoTitle title="基础信息"></InfoTitle>
          </div>
          <div class="base-info-title-divider"></div>
          <div class="base-info-title-action" v-if="props.allowEdit">
            <el-button type="text" @click="onClickEdit">编辑</el-button>
          </div>
        </div>
        <div class="base-info-content">
          <el-form
            :model="dataForm"
            :rules="dataRule"
            ref="dataFormRef"
            label-width="120px"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item prop="realName" label="姓名">
                  <el-input
                    v-model="dataForm.realName"
                    placeholder="姓名"
                    disabled
                    v-if="props.allowEdit && isEdit"
                  ></el-input>
                  <span v-else>{{ dataForm.realName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="gender" label="性别">
                  <fast-radio-group
                    v-model="dataForm.gender"
                    dict-type="user_gender"
                    v-if="props.allowEdit && isEdit"
                  ></fast-radio-group>
                  <span
                    v-html="getDictLabelList('user_gender', dataForm.gender)"
                    v-else
                  ></span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="mobile" label="手机号">
                  <el-input
                    v-model="dataForm.mobile"
                    placeholder="手机号"
                    v-if="props.allowEdit && isEdit"
                  ></el-input>
                  <div v-else>{{ dataForm.mobile }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="birthDate" label="出生日期">
                  <el-date-picker
                    v-model="dataForm.birthDate"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="出生日期"
                    style="width: 100%"
                    v-if="props.allowEdit && isEdit"
                  />
                  <div v-else>{{ dataForm.birthDate }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="idCard" label="身份证号码">
                  <el-input
                    v-model="dataForm.idCard"
                    placeholder="身份证号码"
                    v-if="props.allowEdit && isEdit"
                  ></el-input>
                  <div v-else>{{ dataForm.idCard }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="nation" label="民族">
                  <el-input
                    v-model="dataForm.nation"
                    placeholder="民族"
                    v-if="props.allowEdit && isEdit"
                  ></el-input>
                  <div v-else>{{ dataForm.nation }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="hometown" label="籍贯">
                  <el-input
                    v-model="dataForm.hometown"
                    placeholder="籍贯"
                    v-if="props.allowEdit && isEdit"
                  ></el-input>
                  <div v-else>{{ dataForm.hometown }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="address" label="居住地址">
                  <el-input
                    v-model="dataForm.address"
                    placeholder="居住地址"
                    v-if="props.allowEdit && isEdit"
                  ></el-input>
                  <div v-else>{{ dataForm.address }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="highestEducation" label="最高学历">
                  <fast-select
                    v-model="dataForm.highestEducation"
                    dict-type="highest_education"
                    placeholder="最高学历"
                    v-if="props.allowEdit && isEdit"
                  ></fast-select>
                  <span
                    v-html="
                      getDictLabelList('highest_education', dataForm.highestEducation)
                    "
                    v-else
                  ></span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="schoolName" label="毕业院校">
                  <el-input
                    v-model="dataForm.schoolName"
                    placeholder="毕业院校"
                    v-if="props.allowEdit && isEdit"
                  ></el-input>
                  <div v-else>{{ dataForm.schoolName }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="major" label="专业">
                  <el-input
                    v-model="dataForm.major"
                    placeholder="专业"
                    v-if="props.allowEdit && isEdit"
                  ></el-input>
                  <div v-else>{{ dataForm.major }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="graduationTime" label="毕业时间">
                  <el-date-picker
                    v-model="dataForm.graduationTime"
                    type="date"
                    value-format="YYYY-MM-DD"
                    placeholder="毕业时间"
                    style="width: 100%"
                    v-if="props.allowEdit && isEdit"
                  />
                  <div v-else>{{ dataForm.graduationTime }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="isMarry" label="婚姻状况">
                  <fast-radio-group
                    v-model="dataForm.isMarry"
                    dict-type="is_marry"
                    v-if="props.allowEdit && isEdit"
                  ></fast-radio-group>
                  <span
                    v-html="getDictLabelList('is_marry', dataForm.isMarry)"
                    v-else
                  ></span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="politicsFace" label="政治面貌">
                  <el-input
                    v-model="dataForm.politicsFace"
                    placeholder="政治面貌"
                    v-if="props.allowEdit && isEdit"
                  ></el-input>
                  <div v-else>{{ dataForm.politicsFace }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="email" label="邮箱">
                  <el-input
                    v-model="dataForm.email"
                    placeholder="邮箱"
                    v-if="props.allowEdit && isEdit"
                  ></el-input>
                  <div v-else>{{ dataForm.email }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="linkName" label="紧急联系人">
                  <el-input
                    v-model="dataForm.linkName"
                    placeholder="紧急联系人"
                    v-if="props.allowEdit && isEdit"
                  ></el-input>
                  <div v-else>{{ dataForm.linkName }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="linkTel" label="紧急联系人电话">
                  <el-input
                    v-model="dataForm.linkTel"
                    placeholder="紧急联系人电话"
                    v-if="props.allowEdit && isEdit"
                  ></el-input>
                  <div v-else>{{ dataForm.linkTel }}</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="base-info-action" v-if="props.allowEdit && isEdit">
          <el-button type="primary" @click="onSubmit">保存</el-button>
          <el-button @click="onCancel">取消</el-button>
        </div>
      </div>
    </el-card>
    <el-card style="margin-top: 20px">
      <div class="base-certificate">
        <div class="base-certificate-title">
          <div class="base-certificate-title-text">
            <InfoTitle title="证书"></InfoTitle>
          </div>
          <div class="base-certificate-title-divider"></div>
          <div>
            <el-button type="text" @click="onAddCertificate()" v-if="props.allowEdit"
              >新增</el-button
            >
          </div>
        </div>
        <div class="base-certificate-content">
          <el-table border :data="certificate.dataList">
            <el-table-column
              prop="certificateName"
              label="证书名称"
              header-align="center"
              align="center"
            ></el-table-column>
            <fast-table-column
              prop="certificateType"
              label="证书类型"
              header-align="center"
              align="center"
              dict-type="certificate_type"
              width="150"
            ></fast-table-column>
            <el-table-column
              prop="issuingUnit"
              label="发证机构"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="certificateCode"
              label="证书编号"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="issuingDate"
              label="发证日期"
              header-align="center"
              align="center"
              width="110"
            >
              <template #default="scope">
                {{
                  scope.row.issuingDate &&
                  dayjs(scope.row.issuingDate).format("YYYY-MM-DD")
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="expirationDate"
              label="有效日期"
              header-align="center"
              align="center"
              width="110"
            >
              <template #default="scope">
                {{
                  scope.row.expirationDate &&
                  dayjs(scope.row.expirationDate).format("YYYY-MM-DD")
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              label="操作"
              header-align="center"
              align="center"
              width="150"
              v-if="props.allowEdit"
            >
              <template #default="scope">
                <el-button type="text" @click="onAddCertificate(scope.row.id)"
                  >编辑</el-button
                >
                <el-button type="text" @click="deleteBatchHandleCertificate(scope.row.id)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
    <el-card style="margin-top: 20px">
      <div class="base-skill">
        <div class="base-skill-title">
          <div class="base-skill-title-text">
            <InfoTitle title="技能"></InfoTitle>
          </div>
          <div class="base-skill-title-divider"></div>
          <div>
            <el-button type="text" @click="onAddSkill()" v-if="props.allowEdit"
              >新增</el-button
            >
          </div>
        </div>
        <div class="base-skill-content">
          <el-table border :data="skill.dataList">
            <el-table-column
              prop="skillName"
              label="技能名称"
              header-align="center"
              align="center"
            ></el-table-column>
            <fast-table-column
              prop="skillType"
              label="技能类型"
              header-align="center"
              align="center"
              dict-type="skill_type"
              width="150"
            ></fast-table-column>
            <fast-table-column
              prop="masteryLevel"
              label="掌握程度"
              header-align="center"
              align="center"
              dict-type="mastery_level"
              width="150"
            ></fast-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              label="操作"
              header-align="center"
              align="center"
              width="150"
              v-if="props.allowEdit"
            >
              <template #default="scope">
                <el-button type="text" @click="onAddSkill(scope.row.id)">编辑</el-button>
                <el-button type="text" @click="deleteBatchHandleSkill(scope.row.id)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>

    <ActionCertificate
      ref="certificateRef"
      @refreshDataList="getDataListCertificate()"
    ></ActionCertificate>

    <ActionSkill ref="skillRef" @refreshDataList="getDataListSkill()"></ActionSkill>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { ref, onMounted, reactive, watch } from "vue";
import InfoTitle from "./InfoTitle.vue";
import {
  getUserInfo,
  useUserSubmitApi,
  getUserCertificateList,
  getUserSkillList,
  useUserInfoSubmitApi,
} from "@/api/employee/index";
import ActionCertificate from "./ActionCertificate.vue";
import ActionSkill from "./ActionSkill.vue";
import { useUserStore } from "@/store/modules/user";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { getDictLabelList } from "@/utils/tool";
import { validateMobile } from "@/utils/validate";

const props = withDefaults(
  defineProps<{
    userId: number;
    allowEdit: boolean;
  }>(),
  {
    userId: 0,
    allowEdit: false,
  }
);

const isEdit = ref(false);

const dataFormRef = ref();

const dataForm = ref({
  id: "",
  userId: "",
  realName: "",
  gender: 0,
  mobile: "",
  birthDate: "",
  entryDate: "",
  formalStart: "",
  workingYears: "",
  workingDate: "",
  workLength: "",
  idCard: "",
  nation: "",
  hometown: "",
  address: "",
  highestEducation: "",
  schoolName: "",
  major: "",
  graduationTime: "",
  isMarry: "",
  politicsFace: "",
  email: "",
  linkName: "",
  linkTel: "",
});

const dataRule = ref({
  realName: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
  gender: [{ required: true, message: "性别不能为空", trigger: "blur" }],
  mobile: [
    { required: true, message: "手机号不能为空", trigger: "blur" },
    { validator: validateMobile, trigger: "blur" },
  ],
  birthDate: [{ required: true, message: "出生日期不能为空", trigger: "blur" }],
  idCard: [{ required: true, message: "身份证号不能为空", trigger: "blur" }],
  nation: [{ required: true, message: "民族不能为空", trigger: "blur" }],
  hometown: [{ required: true, message: "籍贯不能为空", trigger: "blur" }],
  address: [{ required: true, message: "地址不能为空", trigger: "blur" }],
  highestEducation: [{ required: true, message: "最高学历不能为空", trigger: "blur" }],
  schoolName: [{ required: true, message: "毕业学校不能为空", trigger: "blur" }],
  major: [{ required: true, message: "专业不能为空", trigger: "blur" }],
  graduationTime: [{ required: true, message: "毕业时间不能为空", trigger: "blur" }],
  isMarry: [{ required: true, message: "婚姻状况不能为空", trigger: "blur" }],
  politicsFace: [{ required: true, message: "政治面貌不能为空", trigger: "blur" }],
  linkName: [{ required: true, message: "紧急联系人不能为空", trigger: "blur" }],
  linkTel: [{ required: true, message: "紧急联系人电话不能为空", trigger: "blur" }],
});

const GetUserInfo = () => {
  console.log(props.userId);
  getUserInfo(props.userId).then((res) => {
    console.log(res);
    if (res.code === 0) {
      Object.assign(dataForm.value, res.data);
    }
  });
};

const onClickEdit = () => {
  isEdit.value = true;
};

const onSubmit = () => {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      console.log(dataForm.value);
      useUserInfoSubmitApi(dataForm.value).then((res) => {
        if (res.code === 0) {
          isEdit.value = false;
          ElMessage.success("保存成功");
        }
      });
    }
  });
};

const onCancel = () => {
  isEdit.value = false;
};

const certificate = reactive<IHooksOptions>({
  isPage: false,
  dataListUrl: "/work/userCertificate/list",
  deleteUrl: "/work/userCertificate",
  queryForm: {
    userId: props.userId,
  },
});

const {
  getDataList: getDataListCertificate,
  deleteBatchHandle: deleteBatchHandleCertificate,
} = useCrud(certificate);

const skill = reactive<IHooksOptions>({
  isPage: false,
  dataListUrl: "/work/userSkills/list",
  deleteUrl: "/work/userSkills",
  queryForm: {
    userId: props.userId,
  },
});

const {
  getDataList: getDataListSkill,
  deleteBatchHandle: deleteBatchHandleSkill,
} = useCrud(skill);

onMounted(() => {
  GetUserInfo();
});

const certificateRef = ref();

const onAddCertificate = (id) => {
  certificateRef.value.init(id);
};

const skillRef = ref();

const onAddSkill = (id) => {
  skillRef.value.init(id);
};

const skillList = ref([]);

const GetUserSkillList = () => {
  getUserSkillList().then((res) => {
    if (res.code === 0) {
      skillList.value = res.data;
    }
  });
};
</script>
<style lang="scss" scoped>
.base {
  &-info {
    &-title {
      display: flex;
      align-items: center;
      &-divider {
        flex: 1;
        height: 1px;
        background-color: #f0f0f0;
        margin: 0 10px;
      }
    }
    &-content {
      margin-top: 20px;
    }
    &-action {
      display: flex;
      justify-content: center;
      margin-top: 20px;
    }
  }
  &-certificate {
    &-title {
      display: flex;
      align-items: center;
      &-divider {
        flex: 1;
        height: 1px;
        background-color: #f0f0f0;
        margin: 0 10px;
      }
    }
    &-content {
      margin-top: 20px;
    }
  }
  &-skill {
    &-title {
      display: flex;
      align-items: center;
      &-divider {
        flex: 1;
        height: 1px;
        background-color: #f0f0f0;
        margin: 0 10px;
      }
    }
    &-content {
      margin-top: 20px;
    }
  }
}
</style>
