<template>
  <el-dialog
    v-model="state.show"
    :title="state.title"
    width="500"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <el-input
      type="textarea"
      v-model="state.reason"
      placeholder="请输入理由"
      :rows="10"
    ></el-input>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.show = false">取消</el-button>
        <el-button type="primary" :loading="state.loading" @click="sureSend"
          >确定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { auditFlowPerson } from "@/api/workbench";
import { ElMessage } from "element-plus";
import { reactive } from "vue";

interface IState {
  show: boolean;
  loading: boolean;
  id: number;
  title: string;
  reason: string;
  putdata?: Object;
}

const state = reactive<IState>({
  show: false,
  id: 0,
  reason: "",
  title: "",
  putdata: {},
  loading: false,
});
const emit = defineEmits(["refresh"]);
const init = (row, title) => {
  state.show = true;
  state.title = title;
  state.putdata = row;
  state.reason = title === "审批同意" ? "同意" : "";
};

const sureSend = () => {
  if ((state.reason ?? "") === "") {
    ElMessage.error("请输入审批理由");
  } else {
    state.loading = true;
    let params = {
      flowId: state.putdata.id,
      infoId: state.putdata.infoId,
      infoType: state.putdata.infoType,
      status: state.title == "审批同意" ? 4 : 5,
      type: 1,
      flowRemark: state.reason,
    };
    auditFlowPerson(params).then((res) => {
      if (res.code == 0) {
        ElMessage.success("提交成功");
        state.loading = false;
        state.show = false;
        emit("refresh");
      } else {
        state.loading = false;
      }
    });
  }
};

defineExpose({
  init,
});
</script>

<style scoped></style>
