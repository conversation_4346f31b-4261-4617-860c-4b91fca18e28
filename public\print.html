<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8" />
  <title></title>
  <style>
    .print-title {
      font-size: 20px;
      display: flex;
      justify-content: center;
    }

    .print-people {
      font-size: 12px;
      color: rgb(153, 153, 153);
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;
    }

    .print-id {
      display: flex;
      justify-content: space-between;
      margin-top: 10px;
    }

    .print-id-main {
      font-size: 12px;
      color: rgb(153, 153, 153);
    }

    .print-id-time {
      font-size: 12px;
      color: rgb(153, 153, 153);
    }

    .content {
      margin-top: 20px;
    }

    .content-item {
      border-top: 1px solid #f1f1f1;
    }

    .content-item:last-child {
      border-bottom: 1px solid #f1f1f1;
    }

    .content-two {
      display: flex;
    }

    .content-two-item {
      flex: 1;
      display: flex;
    }

    .content-two-item-title {
      width: 140px;
      flex-shrink: 0;
      border-left: 1px solid #f1f1f1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px 10px;
      position: relative;
      overflow: hidden;
    }

    .content-two-item-title-image {
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
    }

    .content-two-item-title-text {
      z-index: 2;
    }

    .content-two-item-value {
      border-left: 1px solid #f1f1f1;
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px 10px;
    }

    .content-two-item:last-child {
      border-right: 1px solid #f1f1f1;
    }

    .content-title {
      padding: 6px 10px;
      border-left: 1px solid #f1f1f1;
      border-right: 1px solid #f1f1f1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .content-one {
      display: flex;
      border-right: 1px solid #f1f1f1;
    }

    .content-one-title {
      width: 140px;
      flex-shrink: 0;
      border-left: 1px solid #f1f1f1;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 6px 10px;
      position: relative;
      overflow: hidden;
    }

    .content-one-title-image {
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
    }

    .content-one-title-text {
      z-index: 2;
    }

    .content-one-value {
      border-left: 1px solid #f1f1f1;
      flex: 1;
      display: flex;
      align-items: center;
      padding: 6px 10px;
    }

    .content-one-value-more-item {
      margin-bottom: 6px;
    }

    .content-one-value-more-item:last-child {
      margin-bottom: 0px;
    }

    .content-empty {
      padding: 6px 10px;
      border-left: 1px solid #f1f1f1;
      border-right: 1px solid #f1f1f1;
    }

    @media print {
      .next-page {
        page-break-after: always;
      }

      @page {
        margin-top: 0;
        margin-right: 5mm;
        margin-left: 5mm;
        size: auto;
      }
    }
  </style>
</head>

<body>

</body>

</html>