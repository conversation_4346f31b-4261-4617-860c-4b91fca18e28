<template>
  <div class="statistics">
    <div class="statistics-title">
      <HomeTitle icon="icon-tongji" title="中标情况统计">
        <template #right>
          <el-select v-model="state.year" style="width: 120px" @change="onChangeYear">
            <el-option
              v-for="item in state.yearList"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
        </template>
      </HomeTitle>
    </div>
    <div class="statistics-content">
      <div class="statistics-content-chart" id="chart"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import HomeTitle from "./HomeTitle.vue";
import * as echarts from "echarts/core";
import {
  ToolboxComponent,
  ToolboxComponentOption,
  TooltipComponent,
  TooltipComponentOption,
  GridComponent,
  GridComponentOption,
  LegendComponent,
  LegendComponentOption,
} from "echarts/components";
import { Bar<PERSON>hart, BarSeriesOption, LineChart, LineSeriesOption } from "echarts/charts";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
import { onMounted, onUnmounted, reactive, shallowRef } from "vue";
import dayjs from "dayjs";
import { getPlanSuppelierStatistics } from "@/api/home";
echarts.use([
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
]);

type EChartsOption = echarts.ComposeOption<
  | ToolboxComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | LegendComponentOption
  | BarSeriesOption
  | LineSeriesOption
>;

interface IState {
  year: string;
  yearList: string[];
}

const state = reactive<IState>({
  year: dayjs().format("YYYY"),
  yearList: [dayjs().format("YYYY")],
});

const barEcharts = shallowRef(null);

onMounted(() => {
  barEcharts.value = echarts.init(document.getElementById("chart"));
  let currentYear = Number(dayjs().format("YYYY"));
  let newYearList = [];
  for (let i = 10; i >= 0; i--) {
    newYearList.push(currentYear - i + "");
  }
  state.yearList = newYearList;
  getPlanSuppelierStatistics(state.year).then((res: any) => {
    if (res.code === 0) {
      initEcharts(res.data.numYearMonthResList, res.data.amountYearMonthResList);
    } else {
      initEcharts(
        [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
      );
    }
  });
});

const onChangeYear = () => {
  getPlanSuppelierStatistics(state.year).then((res: any) => {
    if (res.code === 0) {
      initEcharts(res.data.numYearMonthResList, res.data.amountYearMonthResList);
    } else {
      initEcharts(
        [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
        [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
      );
    }
  });
};

const initEcharts = (numYearMonthResList: number[], amountYearMonthResList: number[]) => {
  let option: EChartsOption = {
    type: "category",
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      bottom: 10,
    },
    xAxis: [
      {
        type: "category",
        data: [
          "1月",
          "2月",
          "3月",
          "4月",
          "5月",
          "6月",
          "7月",
          "8月",
          "9月",
          "10月",
          "11月",
          "12月",
        ],
        axisPointer: {
          type: "shadow",
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "中标项目",
      },
      {
        type: "value",
        name: "中标金额",
      },
    ],
    series: [
      {
        name: "中标项目",
        type: "bar",
        // tooltip: {
        //   valueFormatter: function (value) {
        //     return (value as number) + "";
        //   },
        // },
        itemStyle: {
          color: "#399FFD",
        },
        data: numYearMonthResList,
      },
      {
        name: "中标金额",
        type: "line",
        yAxisIndex: 1,
        // tooltip: {
        //   valueFormatter: function (value) {
        //     return (value as number) + "";
        //   },
        // },
        itemStyle: {
          color: "#00C29A",
        },
        data: amountYearMonthResList,
      },
    ],
  };
  option && barEcharts.value.setOption(option);
};
onUnmounted(() => {
  if (barEcharts.value) {
    barEcharts.value.dispose();
  }
});
</script>
<style lang="scss" scoped>
.statistics {
  background-color: #ffffff;
  padding: 16px 10px;
  border-radius: 4px;
  &-content {
    &-chart {
      height: 400px;
    }
  }
}
</style>
