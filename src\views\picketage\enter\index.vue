<template>
  <el-card>
    <div class="enter">
      <div class="enter-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="采购编号"
              clearable
              v-model="state.queryForm.packageNo"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="采购名称"
              clearable
              v-model="state.queryForm.packageName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onResetSearch()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="enter-action">
        <el-button
          type="primary"
          @click="onClickEnter()"
          v-auth="'purchase:winBidder:save'"
        >
          录入
        </el-button>
      </div>
      <div class="enter-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="winBidder"
            label="中标人"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="winPrice"
            label="中标金额（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="serviceCharge"
            label="合同额"
            header-align="center"
            align="center"
          ></el-table-column>

          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-button type="primary" link @click="onClickDetail(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="enter-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <Detail :show="detail.show" :id="detail.id" @on-close="onCloseDetail"></Detail>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive } from "vue";
import { useRouter } from "vue-router";
import Detail from "./components/Detail.vue";

const router = useRouter();

const state = reactive<IHooksOptions>({
  queryForm: {
    packageNo: "",
    packageName: "",
  },
  dataListUrl: "/purchase/winBidder/page",
});

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

const onResetSearch = () => {
  state.queryForm.packageNo = "";
  state.queryForm.packageName = "";
  state.pageNo = 1;
  getDataList();
};

const onClickEnter = () => {
  router.push("/picketage/enter/action");
};

interface IDetail {
  show: boolean;
  id?: number;
}

const detail = reactive<IDetail>({
  show: false,
  id: void 0,
});

const onClickDetail = (row: any) => {
  detail.id = row.id;
  detail.show = true;
};

const onCloseDetail = () => {
  detail.show = false;
  detail.id = void 0;
};
</script>
<style lang="scss" scoped>
.enter {
  &-list {
    margin-top: 16px;
  }
}
</style>
