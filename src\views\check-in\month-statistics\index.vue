<template>
  <el-card>
    <div class="flex-box">
      <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList(1)">
        <el-form-item>
          <el-date-picker
            v-model="selectDate"
            type="month"
            value-format="YYYY-MM"
            style="width: 250px"
            placeholder="选择月份"
            @change="onChangeDate"
            :clearable="false"
          />
        </el-form-item>
        <el-form-item>
          <el-cascader
            :options="userData"
            :props="userProps"
            collapse-tags
            collapse-tags-tooltip
            clearable
            @change="userChange"
            placeholder="请选择员工"
          />
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList(1)">查询</el-button>
        </el-form-item>
      </el-form>
      <div>
        <el-button type="primary" @click="onClickExport()">导出</el-button>
      </div>
    </div>

    <el-table
      v-loading="state.dataListLoading"
      :data="state.dataList"
      border
      style="width: 100%"
    >
      <el-table-column
        fixed="left"
        prop="realName"
        label="员工"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="orgName"
        label="部门"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="attendanceMonth"
        label="职位"
        header-align="center"
        align="center"
      >
        <template #default="scope">
          {{
            scope.row.postNames && scope.row.postNames.length > 0
              ? scope.row.postNames.toString()
              : "-"
          }}
        </template>
      </el-table-column>
      <el-table-column
        prop="requiredAttendanceDays"
        header-align="center"
        align="center"
        width="100"
      >
        <template #header>
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
            "
          >
            <div>应出勤天数</div>
            <div>（天）</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="actualAttendanceDays"
        label="实际出勤天数"
        header-align="center"
        align="center"
        width="110"
      >
        <template #header>
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
            "
          >
            <div>实际出勤天数</div>
            <div>（天）</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="beLateFrequency"
        label="迟到次数"
        header-align="center"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="earlyDepartureFrequency"
        label="早退次数"
        header-align="center"
        align="center"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="workStartMissClockFrequency"
        label="上班缺卡次数"
        header-align="center"
        align="center"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="workEndMissClockFrequency"
        label="下班缺卡次数"
        header-align="center"
        align="center"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="absenteeismDays"
        label="旷工天数"
        header-align="center"
        align="center"
        width="90"
      >
        <template #header>
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
            "
          >
            <div>旷工天数</div>
            <div>（天）</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="请假" header-align="center" align="center">
        <el-table-column prop="sickLeaveDays" header-align="center" align="center">
          <template #header>
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              "
            >
              <div>病假</div>
              <div>（天）</div>
            </div>
          </template>
          <template #default="scope">
            <span
              v-if="scope.row.sickLeaveDays && scope.row.sickLeaveDays > 0"
              class="text-link"
              @click="viewDetail(scope.row, 'leave', '病假')"
              >{{ scope.row.sickLeaveDays }}</span
            >
            <span v-else>{{ scope.row.sickLeaveDays }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="personalLeaveDays" header-align="center" align="center">
          <template #header>
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              "
            >
              <div>事假</div>
              <div>（天）</div>
            </div>
          </template>
          <template #default="scope">
            <span
              v-if="scope.row.personalLeaveDays && scope.row.personalLeaveDays > 0"
              class="text-link"
              @click="viewDetail(scope.row, 'leave', '事假')"
              >{{ scope.row.personalLeaveDays }}</span
            >
            <span v-else>{{ scope.row.personalLeaveDays }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="annualLeaveDays" header-align="center" align="center">
          <template #header>
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              "
            >
              <div>年假</div>
              <div>（天）</div>
            </div>
          </template>
          <template #default="scope">
            <span
              v-if="scope.row.sickLeaveDays && scope.row.sickLeaveDays > 0"
              class="text-link"
              @click="viewDetail(scope.row, 'leave', '年假')"
              >{{ scope.row.sickLeaveDays }}</span
            >
            <span v-else>{{ scope.row.sickLeaveDays }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="maternityLeaveDays" header-align="center" align="center">
          <template #header>
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              "
            >
              <div>产假</div>
              <div>（天）</div>
            </div>
          </template>
          <template #default="scope">
            <span
              v-if="scope.row.sickLeaveDays && scope.row.sickLeaveDays > 0"
              class="text-link"
              @click="viewDetail(scope.row, 'leave', '产假')"
              >{{ scope.row.sickLeaveDays }}</span
            >
            <span v-else>{{ scope.row.sickLeaveDays }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="paternityLeaveDays" header-align="center" align="center">
          <template #header>
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              "
            >
              <div>陪产假</div>
              <div>（天）</div>
            </div>
          </template>
          <template #default="scope">
            <span
              v-if="scope.row.sickLeaveDays && scope.row.sickLeaveDays > 0"
              class="text-link"
              @click="viewDetail(scope.row, 'leave', '陪产假')"
              >{{ scope.row.sickLeaveDays }}</span
            >
            <span v-else>{{ scope.row.sickLeaveDays }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="marriageLeaveDays" header-align="center" align="center">
          <template #header>
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              "
            >
              <div>婚假</div>
              <div>（天）</div>
            </div>
          </template>
          <template #default="scope">
            <span
              v-if="scope.row.sickLeaveDays && scope.row.sickLeaveDays > 0"
              class="text-link"
              @click="viewDetail(scope.row, 'leave', '婚假')"
              >{{ scope.row.sickLeaveDays }}</span
            >
            <span v-else>{{ scope.row.sickLeaveDays }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="funeralLeaveDays" header-align="center" align="center">
          <template #header>
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
              "
            >
              <div>丧假</div>
              <div>（天）</div>
            </div>
          </template>
          <template #default="scope">
            <span
              v-if="scope.row.sickLeaveDays && scope.row.sickLeaveDays > 0"
              class="text-link"
              @click="viewDetail(scope.row, 'leave', '丧假')"
              >{{ scope.row.sickLeaveDays }}</span
            >
            <span v-else>{{ scope.row.sickLeaveDays }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column prop="one" label="1" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.one"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 1)"
            >{{ scope.row.one }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="two" label="2" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.two"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 2)"
            >{{ scope.row.two }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="three" label="3" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.three"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 3)"
            >{{ scope.row.three }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="four" label="4" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.four"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 4)"
            >{{ scope.row.four }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="five" label="5" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.five"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 5)"
            >{{ scope.row.five }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="six" label="6" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.six"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 6)"
            >{{ scope.row.six }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="seven" label="7" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.seven"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 7)"
            >{{ scope.row.seven }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="eight" label="8" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.eight"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 8)"
            >{{ scope.row.eight }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="nine" label="9" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.nine"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 9)"
            >{{ scope.row.nine }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="ten" label="10" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.ten"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 10)"
            >{{ scope.row.ten }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="eleven" label="11" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.eleven"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 11)"
            >{{ scope.row.eleven }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="twelve" label="12" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.twelve"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 12)"
            >{{ scope.row.twelve }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="thirteen" label="13" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.thirteen"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 13)"
            >{{ scope.row.thirteen }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="fourteen" label="14" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.fourteen"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 14)"
            >{{ scope.row.fourteen }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="fifteen" label="15" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.fifteen"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 15)"
            >{{ scope.row.fifteen }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="sixteen" label="16" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.sixteen"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 16)"
            >{{ scope.row.sixteen }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="seventeen" label="17" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.seventeen"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 17)"
            >{{ scope.row.seventeen }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="eighteen" label="18" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.eighteen"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 18)"
            >{{ scope.row.eighteen }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="nineteen" label="19" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.nineteen"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 19)"
            >{{ scope.row.nineteen }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="twenty" label="20" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.twenty"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 20)"
            >{{ scope.row.twenty }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="twentyOne" label="21" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.twentyOne"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 21)"
            >{{ scope.row.twentyOne }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="twentyTwo" label="22" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.twentyTwo"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 22)"
            >{{ scope.row.twentyTwo }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="twentyThree" label="23" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.twentyThree"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 23)"
            >{{ scope.row.twentyThree }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="twentyFour" label="24" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.twentyFour"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 24)"
            >{{ scope.row.twentyFour }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="twentyFive" label="25" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.twentyFive"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 25)"
            >{{ scope.row.twentyFive }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="twentySix" label="26" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.twentySix"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 26)"
            >{{ scope.row.twentySix }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="twentySeven" label="27" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.twentySeven"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 27)"
            >{{ scope.row.twentySeven }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="twentyEight" label="28" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.twentyEight"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 28)"
            >{{ scope.row.twentyEight }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="twentyNine" label="29" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.twentyNine"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 29)"
            >{{ scope.row.twentyNine }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="thirty" label="30" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.thirty"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 30)"
            >{{ scope.row.thirty }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="thirtyOne" label="31" header-align="center" align="center">
        <template #default="scope">
          <span
            v-if="scope.row.thirtyOne"
            class="text-link"
            @click="viewDetail(scope.row, 'clock', null, 31)"
            >{{ scope.row.thirtyOne }}</span
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="state.pageNo"
      :page-sizes="state.pageSizes"
      :page-size="state.pageSize"
      :total="state.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    >
    </el-pagination>
    <el-dialog
      v-model="state.visibleDetail"
      :title="state.titleDetail"
      width="800px"
      :before-close="visibleClose"
    >
      <el-table
        v-if="state.infoType === 'leave'"
        :data="state.dataListLeave"
        border
        style="width: 100%"
      >
        <el-table-column
          prop="startTime"
          label="开始时间"
          header-align="center"
          align="center"
        >
          <template #default="scope">
            {{ scope.row.leaveTime }}
            <template v-if="scope.row.leaveType === 0 || scope.row.leaveType === 2"
              >上午</template
            >
            <template v-if="scope.row.leaveType === 1">下午</template>
          </template>
        </el-table-column>
        <el-table-column
          prop="endTime"
          label="结束时间"
          header-align="center"
          align="center"
        >
          <template #default="scope">
            {{ scope.row.leaveTime }}
            <template v-if="scope.row.leaveType === 0">上午</template>
            <template v-if="scope.row.leaveType === 1 || scope.row.leaveType === 2"
              >下午</template
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="leaveType"
          label="请假类型"
          header-align="center"
          align="center"
        >
          <template #default="scope">
            {{ state.holidayName }}
          </template>
        </el-table-column>
        <el-table-column
          prop="days"
          label="请假时长（天）"
          header-align="center"
          align="center"
        ></el-table-column>
      </el-table>
      <el-table
        v-if="state.infoType === 'clock'"
        :data="state.dataListClock"
        border
        style="width: 100%"
      >
        <el-table-column
          prop="attendanceTime"
          label="考勤时间"
          header-align="center"
          align="center"
        >
          <template #default="scope">
            {{ scope.row.attendanceTime ? scope.row.attendanceTime : "-" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="clockTime"
          label="打卡时间"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="clockResultLabel"
          label="打卡结果"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="clockLocation"
          label="打卡地址"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="clockImg"
          label="打卡照片"
          header-align="center"
          align="center"
        >
          <template #default="scope">
            <!-- https://test-jczl.hebhzjt.com:8443/yuan-oa/upload/20240819/lkTtajMlaBTq1b4da557ad1f741a32b9a81f049a7a37_1724054505.jpg -->
            <img
              v-if="scope.row.clockImg"
              @click="imageClick(scope.row.clockImg)"
              class="clock-img"
              :src="scope.row.clockImg"
              alt=""
            />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="clockRemark"
          label="打卡备注"
          header-align="center"
          align="center"
        >
          <template #default="scope">
            {{ scope.row.clockRemark ? scope.row.clockRemark : "-" }}
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="visibleClose">关闭</el-button>
        </div>
      </template>
    </el-dialog>
    <el-image-viewer
      v-if="state.imageVisible"
      :url-list="state.imageList"
      @close="imageClose"
    ></el-image-viewer>
  </el-card>
</template>

<script setup lang="ts">
import { useCrud } from "@/hooks";
import { reactive, ref, onMounted } from "vue";
import { IHooksOptions } from "@/hooks/interface";
import {
  exportMonthAttendanceStatistics,
  findHolidayByDate,
  findClockRecordByDate,
} from "@/api/check-in/month-statistics";
import { jsonp } from "vue-jsonp";
import moment from "moment-mini";
import { getlistOrgUserTree } from "@/api/workbench";
import { getDayOfWeek } from "@/utils/tool";

const selectDate = ref(moment(new Date().getTime()).format("YYYY-MM"));
const state: IHooksOptions = reactive({
  dataListUrl: "/work/monthAttendanceStatistics/page",
  queryForm: {
    attendanceYear: null,
    attendanceMonth: null,
    userId: "",
  },
  infoType: "",
  rowInfoNow: {},
  titleDetail: "",
  visibleDetail: false,
  dayToday: null,
  dataListLeave: [],
  dataListClock: [],
  holidayName: "",
  imageVisible: false,
  imageList: [],
});

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};
const userProps = { value: "id", label: "name", children: "children", multiple: true };
const userData = ref([]);

const onClickExport = () => {
  let params = {
    attendanceYear: state.queryForm.attendanceYear,
    attendanceMonth: state.queryForm.attendanceMonth,
    userId: state.queryForm.userId,
  };

  exportMonthAttendanceStatistics(params).then((res) => {
    const url = window.URL.createObjectURL(new Blob([res.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "月考勤表.xlsx");
    document.body.appendChild(link);
    link.click();
  });
};
const onChangeDate = (value: any) => {
  console.log(value);
  if (value) {
    let arr = value.split("-");
    state.queryForm.attendanceYear = parseInt(arr[0]);
    state.queryForm.attendanceMonth = parseInt(arr[1]);
  }
};
const visibleClose = () => {
  state.visibleDetail = false;
};
const viewDetail = (row, type, holidayName, day) => {
  state.rowInfoNow = row;
  state.infoType = type;
  state.holidayName = holidayName;
  if (type === "leave") {
    getFindHolidayByDate(row.realName, holidayName);
  } else if (type === "clock") {
    getfindClockRecordByDate(row.realName, day);
  }
};

const getFindHolidayByDate = (realName, holidayName) => {
  let attendanceMonth = state.queryForm.attendanceMonth;
  if (attendanceMonth < 10) {
    attendanceMonth = "0" + attendanceMonth;
  }
  let attendanceYearMonth = state.queryForm.attendanceYear + "-" + attendanceMonth;
  state.titleDetail = realName + " " + attendanceYearMonth;
  let params = {
    attendanceYearMonth: attendanceYearMonth,
    userId: state.rowInfoNow.userId,
    holidayName: holidayName,
  };
  findHolidayByDate(params).then((res) => {
    if (res.code == 0) {
      state.dataListLeave = res.data;
      state.visibleDetail = true;
    }
  });
};

const getfindClockRecordByDate = (realName, day) => {
  let attendanceMonth = state.queryForm.attendanceMonth;
  let attendanceDay = day;
  if (attendanceMonth < 10) {
    attendanceMonth = "0" + attendanceMonth;
  }
  if (attendanceDay < 10) {
    attendanceDay = "0" + attendanceDay;
  }
  let attendanceDate =
    state.queryForm.attendanceYear + "-" + attendanceMonth + "-" + attendanceDay;
  state.titleDetail =
    realName + " " + attendanceDate + " " + getDayOfWeek(attendanceDate);
  let week = getDayOfWeek(attendanceDate);
  console.log("week", week);
  let params = {
    attendanceDate: attendanceDate,
    userId: state.rowInfoNow.userId,
  };
  findClockRecordByDate(params).then((res) => {
    if (res.code == 0) {
      state.dataListClock = res.data;
      state.visibleDetail = true;
    }
  });
};
const getUserList = () => {
  getlistOrgUserTree().then((res) => {
    if (res.code == 0) {
      userData.value = res.data;
    }
  });
};
const userChange = (event) => {
  console.log(event);
  if (event && event.length > 0) {
    let arr = [];
    event.map((item) => {
      arr.push(item[item.length - 1]);
    });
    state.queryForm.userId = arr.toString();
  } else {
    state.queryForm.userId = "";
  }
};
const imageClick = (src) => {
  state.imageVisible = true;
  state.imageList = [];
  state.imageList.push(src);
};
const imageClose = () => {
  state.imageVisible = false;
  state.imageList = [];
};
onMounted(() => {
  let nowTime = new Date();
  let year = nowTime.getFullYear();
  let month = nowTime.getMonth() + 1;
  let day = nowTime.getDate();
  state.queryForm.attendanceYear = parseInt(year);
  state.queryForm.attendanceMonth = parseInt(month);
  state.dayToday = parseInt(day);
  getUserList();
});
const {
  getDataList,
  selectionChangeHandle,
  sizeChangeHandle,
  currentChangeHandle,
  deleteBatchHandle,
} = useCrud(state);
</script>
<style scoped>
.flex-box {
  display: flex;
  justify-content: space-between;
}
.text-link {
  color: blue;
  cursor: pointer;
}
.clock-img {
  max-width: 50px;
  max-height: 50px;
  cursor: pointer;
}
</style>
