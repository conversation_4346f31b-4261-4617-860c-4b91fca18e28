import service from '@/utils/request'

export const usebulletinListApi = () => {
	return service.get('/purchase/bulletin/packageList')
}

export const usebulletinChangeListApi = () => {
	return service.get('/purchase/bulletinChange/packageList')
}

export const useGetMaterialByPackIdApi = (id) => {
	return service.get('/purchase/packageMaterial/getMaterialByPackId/'+id)
}

export const usebulletinChangeByPackIdApi = (pid) => {
	return service.get('/purchase/bulletinChange/getBulletinByPackageId/'+pid)
}

export const useBulletinSaveTempApi = (data) => {
	return service.post('/purchase/bulletin/saveTemp',data)
}

export const useBulletinChangeSaveTempApi = (data) => {
	return service.post('/purchase/bulletinChange/saveTemp',data)
}

export const usebulletinByIdApi = (id) => {
	return service.get('/purchase/bulletin/getOne/'+id)
}

export const usebulletinChangeByIdApi = (id) => {
	return service.get('/purchase/bulletinChange/getOne/'+id)
}

export const useBulletinSaveSubmitApi = (data) => {
	return service.post('/purchase/bulletin/saveSubmit',data)
}

export const useBulletinChangeSaveSubmitApi = (data) => {
	return service.post('/purchase/bulletinChange/saveSubmit',data)
}

export const useBulletinSaveAuditApi = (data) => {
	return service.put('/purchase/bulletin/saveAudit',data)
}

export const useBulletinChangeSaveAuditApi = (data) => {
	return service.put('/purchase/bulletinChange/saveAudit',data)
}

export const useInvitationListApi = () => {
	return service.get('/purchase/invitation/packageList')
}

export const useInvitationSaveTempApi = (data) => {
	return service.post('/purchase/invitation/saveTemp',data)
}

export const useInvitationSaveSubmitApi = (data) => {
	return service.post('/purchase/invitation/saveSubmit',data)
}

export const useInvitationByIdApi = (id) => {
	return service.get('/purchase/invitation/getOne/'+id)
}

export const useInvitationSaveAuditApi = (data) => {
	return service.put('/purchase/invitation/saveAudit',data)
}

export const useGetListByPackIdApi = (id) => {
	return service.get('/purchase/invitationBidder/getListByPackId/'+id)
}

export const useInvitationBidderBatchApi = (data) => {
	return service.post('/purchase/invitationBidder/saveBatch',data)
}

export const usebulletinByPackageIdApi = (id) => {
	return service.get('/purchase/bulletin/getOneByPackageId/'+id)
}

export const useInvitationByPackageIdApi = (id) => {
	return service.get('/purchase/invitation/getOneByPackageId/'+id)
}

export const usebulletinRegistrationApi = (data) => {
	return service.post('/purchase/registration',data)
}

export const usebulletinRegistrationPutApi = (data) => {
	return service.put('/purchase/registration',data)
}

export const useRegistrationByIdApi = (id) => {
	return service.get('/purchase/registration/'+id)
}

export const useRegistrationCheckApi = (id) => {
	return service.get('/purchase/registration/check/'+id)
}

export const useGetSysUserDetailApi = () => {
	return service.get('/purchase/registration/getSysUserDtail')
}

export const usebulletinAcceptInvitationApi = (data) => {
	return service.post('/purchase/registration/acceptInvitation',data)
}

export const useRefuseInvitationApi = (id) => {
	return service.get('/purchase/registration/refuseInvitation/'+id)
}

export const useGetQuotationDetailByPackIdAndRoundNumApi = (packId,roundNum) => {
	return service.get(`/purchase/quotation/getQuotationDetailByPackIdAndRoundNum/${packId}/${roundNum}`)
}

export const useGetQuotationBasicInfoApi = (packId,roundNum) => {
	return service.get(`/purchase/quotation/getQuotationBasicInfo/${packId}/${roundNum}`)
}

export const useQuotationSaveApi = (data) => {
	return service.post('/purchase/quotation',data)
}

export const useQuotationSavePutApi = (data) => {
	return service.put('/purchase/quotation',data)
}

export const useCancelQuotationByIdApi = (id) => {
	return service.put('/purchase/quotation/cancelQuotation/'+id)
}

export const useGetRoundByPackIdApi = (packId) => {
	return service.get(`/purchase/round/getRoundByPackId/${packId}`)
}

export const useGetBidderListApi = () => {
	return service.get(`/purchase/winBidder/bidderList`)
}

export const useOrderScanCodeBuyApi = (data) => {
	return service.post('/purchase/order/scanCodeBuy',data)
}

export const useOrderGetPayStatusApi = (id) => {
	return service.get('/pay/goodsOrder/getPayStatus/'+id)
}

export const useGetWinBidderInfoApi = (id) => {
	return service.get('/purchase/winBidder/'+id)
}

export const useOrderGetPayGoodsApi = (id) => {
	return service.get('/pay/goodsOrder/getPayGoods/'+id)
}

export const useDeleteInvoiceHeaderApi = (data) => {
	return service.delete('/sys/invoiceHeader',data)
}

export const useInvoiceHeaderByIdApi = (id) => {
	return service.get('/sys/invoiceHeader/'+id)
}

export const useInvoiceHeaderSaveApi = (data) => {
	return service.post('/sys/invoiceHeader',data)
}

export const useInvoiceHeaderSavePutApi = (data) => {
	return service.put('/sys/invoiceHeader',data)
}

export const useGetAreaTreeApi = () => {
	return service.get('/sys/area/tree')
}

export const useGetMailingAddressApi = (id) => {
	return service.get('/sys/mailingAddress/'+id)
}

export const useMailingAddressSaveApi = (data) => {
	return service.post('/sys/mailingAddress',data)
}
export const useMailingAddressSavePutApi = (data) => {
	return service.put('/sys/mailingAddress',data)
}

export const useGetDefaultAddressApi = () => {
	return service.get('/sys/mailingAddress/defaultAddress')
}
export const useGetInvoiceHeaderApi = (invoiceType) => {
	return service.get('/sys/invoiceHeader/list/'+invoiceType)
}

export const useGetAuditRecordsByIdApi = (id) => {
	return service.get('/purchase/bulletin/auditRecords/'+id)
}

export const useGetInvitationAuditRecordsByIdApi = (id) => {
	return service.get('/purchase/invitation/auditRecords/'+id)
}
export const moreUseSave = (data) => {
	return service.post('/work/bulletin/info',data)
}
export const moreUseUpdate = (data) => {
	return service.put('/work/bulletin/info',data)
}
export const moreUseGetInfo = (id) => {
	return service.get('/work/bulletin/info/'+id)
}
export const morePersonGetInfo = (id) => {
	return service.get('/work/bulletin/person/'+id)
}
export const getWorkFlowPage = (params) => {
	return service.get('/work/flow/page',{params})
}
export const getWorkInfoPage = (params) => {
	return service.get('/work/bulletin/info/page',{params})
}
export const getContractDetailInfo = (id) => {
	return service.get('/work/contract/flow/info/'+id)
}
export const contractSignRecordPage = (data) => {
	return service.get('/work/contractSignRecord/page',data)
}
export const contractSignCancel = (data) => {
	return service.put('/work/contract/flow/info/signCancel',data)
}
export const contractSignReject = (data) => {
	return service.put('/work/contract/flow/info/signReject',data)
}
export const contractSignConfirmation = (data) => {
	return service.put('/work/contract/flow/info/signConfirmation',data)
}
/*
  上传文件类型

  // 音视频
.3gpp  audio/3gpp， video/3gpp  
.ac3   audio/ac3   
.asf   allpication/vnd.ms-asf  
.au    audio/basic 
.ogg   application/ogg， audio/ogg  
.mp2   audio/mpeg， video/mpeg  
.mp3   audio/mpeg  
.mp4   audio/mp4， video/mp4   
//图片
.jp2   image/jp2   
.jpe   image/jpeg  
.jpeg  image/jpeg  
.jpg   image/jpeg  
.mpeg  video/mpeg  
.mpg   video/mpeg 
.dwg   image/vnd.dwg   
.dxf   image/vnd.dxf   
.gif   image/gif   
//文件
.doc   application/msword  
.docx	application/vnd.openxmlformats-officedocument.wordprocessingml.document
.dot   application/msword  
.dotx	application/vnd.openxmlformats-officedocument.wordprocessingml.template
.docm	application/vnd.ms-word.document.macroEnabled.12
.dotm	application/vnd.ms-word.template.macroEnabled.12
.dtd   application/xml-dtd 
.mpp   application/vnd.ms-project  
.pdf   application/pdf 
.png   image/png   
.pot   application/vnd.ms-powerpoint   
.pps   application/vnd.ms-powerpoint   
.ppt   application/vnd.ms-powerpoint 
.pptx	application/vnd.openxmlformats-officedocument.presentationml.presentation
.potx	application/vnd.openxmlformats-officedocument.presentationml.template
.ppsx	application/vnd.openxmlformats-officedocument.presentationml.slideshow
.rtf   application/rtf， text/rtf  
.svf   image/vnd.svf  
.tif   image/tiff  
.tiff  image/tiff  
.txt   text/plain  
.wdb   application/vnd.ms-works    
.wps   application/vnd.ms-works    
.xhtml application/xhtml+xml   
.xlc   application/vnd.ms-excel    
.xlm   application/vnd.ms-excel    
.xls   application/vnd.ms-excel    
.xlsx	application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
.xltx	application/vnd.openxmlformats-officedocument.spreadsheetml.template
.xlsm	application/vnd.ms-excel.sheet.macroEnabled.12
.xltm	application/vnd.ms-excel.template.macroEnabled.12
.xlam	application/vnd.ms-excel.addin.macroEnabled.12
.xlsb	application/vnd.ms-excel.sheet.binary.macroEnabled.12
.xlt   application/vnd.ms-excel    
.xlw   application/vnd.ms-excel    
.xml   text/xml， application/xml   
.css   text/css    
.csv   text/csv    
.htm   text/html   
.html  text/html   
.js    text/javascript， 
.json  application/json    
.zip   aplication/zip  

*/ 