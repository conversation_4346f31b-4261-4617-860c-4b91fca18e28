<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-06-24 16:06:12
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-07-01 09:42:20
-->
<template>
  <div class="detail-ctr">
    <div class="detail-ctr-label">审批事由</div>
    <div class="detail-ctr-txt" v-if="props.alldata.oaCommonFlowInfoVO">
      {{ props.alldata.oaCommonFlowInfoVO.title }}
    </div>
    <div class="detail-ctr-label">审批详情</div>
    <div class="detail-ctr-txt" v-if="props.alldata.oaCommonFlowInfoVO">
      <WangEditor
        v-model="props.alldata.oaCommonFlowInfoVO.flowRemark"
        :disabled="true"
      ></WangEditor>
      <!-- {{ props.alldata.oaCommonFlowInfoVO.flowRemark }} -->
    </div>
    <div class="detail-ctr-label">附件</div>
    <div class="detail-ctr-txt" v-if="props.alldata.oaCommonFlowInfoVO">
      <el-link
        class="file"
        :underline="false"
        type="primary"
        v-for="item in props.alldata.oaCommonFlowInfoVO.attachmentList"
        @click="downloadFile(item.url, item.name)"
        >{{ item.name }}</el-link
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import WangEditor from "@/components/wang-editor/index.vue";
import { downloadFile } from "@/utils/tool";
import { IView } from "../types";

interface IProps {
  id: number;
  alldata: IView;
}

const props = withDefaults(defineProps<IProps>(), {
  id: 0,
  alldata: {
    oaCommonFlowInfoVO: {
      flowRemark: "",
      attachmentList: [],
    },
  },
});
</script>

<style scoped lang="scss">
.detail-ctr {
  &-label {
    color: #a2a2a2;
    padding: 0 12px 0 0;
    margin: 0 0 8px 0;
    line-height: 22px;
  }
  &-txt {
    margin-bottom: 15px;
    .file {
      display: block;
      line-height: 1.8;
    }
  }
}
</style>
