<template>
  <div class="info">
    <el-card>
      <div class="info-base">
        <div class="info-base-title">
          <div class="info-base-title-left">
            <InfoTitle title="员工详情"></InfoTitle>
          </div>
        </div>
        <div class="info-base-divider"></div>
        <div class="info-base-content">
          <!-- <div class="content-avatar">
            <el-image
              src="https://picsum.photos/200/200"
              class="content-avatar-img"
            ></el-image>
          </div> -->
          <div class="content-avatar-empty"></div>
          <div class="content-info">
            <div class="content-info-name">
              <div class="content-info-name-text">{{ state.realName }}</div>
              <div class="content-info-name-status">
                <el-tag type="success" v-if="state.status == 1">启用</el-tag>
                <el-tag type="error" v-else>停用</el-tag>
              </div>
            </div>
            <div class="content-info-main">
              <div class="item">
                <div class="item-text">
                  {{ state.userStatus == 1 ? "正式" : "试用" }}
                </div>
                <div class="item-divider"></div>
              </div>
              <div class="item">
                <div class="item-text">
                  {{ state.companyName }}
                </div>
                <div class="item-divider"></div>
              </div>
              <div class="item">
                <div class="item-text">
                  {{ state.name }}
                </div>
                <div class="item-divider"></div>
              </div>
              <div class="item">
                <div class="item-text">
                  {{ (state.postName || []).join("、") }}
                </div>
              </div>
            </div>
            <div class="content-info-link">
              <div class="link-item">
                <svg-icon icon="icon-phone"></svg-icon>
                <div class="link-item-text">
                  {{ state.mobile }}
                </div>
              </div>
              <div class="link-item">
                <svg-icon icon="icon-email"></svg-icon>
                <div class="link-item-text">
                  {{ state.email }}
                </div>
              </div>
            </div>
          </div>
          <div class="content-action">
            <!-- <el-button type="primary">停用</el-button> -->
            <!-- <el-button @click="onClickDelete">删除</el-button> -->
          </div>
        </div>
      </div>
    </el-card>
    <div class="info-content">
      <div class="info-content-class">
        <InfoClass
          :active-class="state.activeClass"
          @click-class="onClickClass"
        ></InfoClass>
      </div>
      <div class="info-content-main">
        <div v-if="state.activeClass === 'company'">
          <CompanyInfo :userId="state.userId" :allowEdit="true"></CompanyInfo>
        </div>
        <div v-else-if="state.activeClass === 'base'">
          <BaseInfo :userId="state.userId" :allowEdit="allowEdit"></BaseInfo>
        </div>
        <div v-else-if="state.activeClass === 'education'">
          <EducationInfo :userId="state.userId" :allowEdit="allowEdit"></EducationInfo>
        </div>
        <div v-else-if="state.activeClass === 'work'">
          <WorkInfo :userId="state.userId" :allowEdit="allowEdit"></WorkInfo>
        </div>
        <div v-else-if="state.activeClass === 'contract'">
          <ContractInfo :userId="state.userId" :allowEdit="allowEdit"></ContractInfo>
        </div>
        <div v-else-if="state.activeClass === 'file'">
          <FileInfo :userId="state.userId" :allowEdit="allowEdit"></FileInfo>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { onMounted, reactive, ref } from "vue";
import InfoTitle from "./components/InfoTitle.vue";
import InfoClass from "./components/InfoClass.vue";
import CompanyInfo from "./components/CompanyInfo.vue";
import BaseInfo from "./components/BaseInfo.vue";
import EducationInfo from "./components/EducationInfo.vue";
import WorkInfo from "./components/WorkInfo.vue";
import ContractInfo from "./components/ContractInfo.vue";
import FileInfo from "./components/FileInfo.vue";
import { getUserInfo } from "@/api/employee/index";
import { useUserStore } from "@/store/modules/user";
import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";

const userStore = useUserStore();
const route = useRoute();

const allowEdit = ref(false);

const state = reactive({
  userId: "",
  activeClass: "company",
  realName: "",
  userStatus: "",
  status: "",
  companyName: "",
  name: "",
  postName: "",
  mobile: "",
  email: "",
  from: "list",
});

const GetUserInfo = () => {
  getUserInfo(state.userId).then((res) => {
    console.log(res);
    if (res.code === 0) {
      state.realName = res.data.realName;
      state.userStatus = res.data.userStatus;
      state.status = res.data.status;
      state.companyName = res.data.companyName;
      state.name = res.data.name;
      state.postName = res.data.postName;
      state.mobile = res.data.mobile;
      state.email = res.data.email;
    }
  });
};

onMounted(() => {
  if ((route.query.userId || 0) !== 0) {
    state.userId = route.query.userId;
  } else {
    state.userId = userStore.user.id;
  }
  allowEdit.value = false;
  GetUserInfo();
});

const onClickClass = (key) => {
  state.activeClass = key;
};

const onClickDelete = () => {
  let ids = [state.userId];
  deleteUserApi(ids)
    .then((res) => {
      if (res.code === 0) {
        ElMessage.success("删除成功");
      }
    })
    .catch((err) => {
      ElMessage.error(err);
    });
};
</script>
<style lang="scss" scoped>
.info {
  &-base {
    &-divider {
      height: 1px;
      background-color: #f0f0f0;
      margin: 10px 0;
    }
    &-content {
      display: flex;
      .content-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        &-img {
          width: 120px;
          height: 120px;
          border-radius: 50%;
        }
        &-empty {
          width: 120px;
          height: 120px;
          border-radius: 50%;
          background-color: #f0f0f0;
        }
      }
      .content-info {
        flex: 1;
        margin-left: 20px;
        &-name {
          display: flex;
          align-items: center;
          margin-top: 10px;
          &-text {
            font-size: 20px;
            color: #333;
            font-weight: 600;
            margin-right: 10px;
          }
        }
        &-main {
          display: flex;
          align-items: center;
          margin-top: 10px;
          .item {
            display: flex;
            align-items: center;
            &-text {
              font-size: 14px;
              color: rgb(153, 153, 153);
            }
            &-divider {
              width: 1px;
              height: 14px;
              background-color: #f0f0f0;
              margin: 0 10px;
            }
          }
        }
        &-link {
          display: flex;
          align-items: center;
          margin-top: 10px;
          .link-item {
            font-size: 14px;
            color: rgb(153, 153, 153);
            display: flex;
            align-items: center;
            margin-right: 20px;
          }
        }
      }
      .content-action {
        display: flex;
        align-items: center;
      }
    }
  }
  &-content {
    display: flex;
    margin-top: 20px;
    .info-content-class {
      width: 250px;
      margin-right: 20px;
      flex-shrink: 0;
    }
    .info-content-main {
      flex: 1;
      flex-shrink: 0;
      width: 0;
    }
  }
}
</style>
