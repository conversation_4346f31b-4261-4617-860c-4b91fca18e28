<template>
  <div class="statistics">
    <div class="statistics-title">
      <HomeTitle icon="icon-tongji1" title="供应商中标统计"> </HomeTitle>
    </div>
    <div class="statistics-content">
      <div class="statistics-content-title">
        <div class="statistics-content-title-sort">排名</div>
        <div class="statistics-content-title-name">供应商名称</div>
        <div class="statistics-content-title-num">中标项目数</div>
      </div>
      <div
        class="statistics-content-list"
        ref="refList"
        @mouseover="onMouseOverList"
        @mouseout="onMouseOutList"
      >
        <div class="list-item" v-for="item in state.dataList">
          <div class="list-item-sort">
            <div v-if="item.key < 4" class="list-item-sort-one">
              <div class="list-item-sort-one-img"></div>
              <div class="list-item-sort-one-num">
                {{ item.key }}
              </div>
            </div>
            <div v-else class="list-item-sort-two">
              <div class="list-item-sort-two-img"></div>
              <div class="list-item-sort-two-num">
                {{ item.key }}
              </div>
            </div>
          </div>
          <div class="list-item-name">
            {{ item.winBidder }}
          </div>
          <div class="list-item-num">{{ item.bidCount }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { getSuppelierSortList } from "@/api/home";
import { onMounted, reactive, ref } from "vue";
import HomeTitle from "./HomeTitle.vue";

const refList = ref();

interface IDataItem {
  key: number;
  winBidder: string;
  bidCount: number;
}

interface IState {
  dataList: IDataItem[];
  listInterval: any;
  listTimeout: any;
}

const state = reactive<IState>({
  dataList: [],
  listInterval: void 0,
  listTimeout: void 0,
});

const ClearListInterval = () => {
  state.listInterval && clearInterval(state.listInterval);
  state.listTimeout && clearTimeout(state.listTimeout);
};

const AutoScrollList = () => {
  let divData = refList.value;
  ClearListInterval();
  state.listInterval = setInterval(() => {
    state.listTimeout = setTimeout(() => {
      divData.scrollTop = divData.scrollTop + 1;
      if (divData.clientHeight + divData.scrollTop >= divData.scrollHeight) {
        divData.scrollTop = 0;
      }
    }, 1000);
  }, 100);
};

const onMouseOverList = () => {
  ClearListInterval();
};

const onMouseOutList = () => {
  AutoScrollList();
};

onMounted(() => {
  getSuppelierSortList().then((res: any) => {
    if (res.code === 0) {
      state.dataList = (res?.data ?? []).map((item: any, index: number) => {
        return {
          key: index + 1,
          winBidder: item.winBidder,
          bidCount: item.bidCount,
        };
      });
    }
  });
  AutoScrollList();
});
</script>
<style lang="scss" scoped>
.statistics {
  background-color: #ffffff;
  padding: 16px 10px;
  border-radius: 4px;
  &-content {
    margin-top: 12px;
    &-title {
      height: 48px;
      display: flex;
      align-items: center;
      background-color: #f5f7fa;
      &-sort {
        width: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      &-name {
        flex: 1;
      }
      &-num {
        width: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
    &-list {
      overflow-y: hidden;
      height: 220px;
      .list-item {
        height: 48px;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #d8d8d8;
        &-sort {
          width: 80px;
          display: flex;
          align-items: center;
          justify-content: center;

          &-one {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            &-img {
              width: 30px;
              height: 30px;
              background-image: url("@/assets/image/home/<USER>");
              background-size: 100% 100%;
            }
            &-num {
              position: absolute;
              bottom: 5px;
              color: #ffffff;
            }
          }
          &-two {
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            &-img {
              width: 26px;
              height: 26px;
              background-image: url("@/assets/image/home/<USER>");
              background-size: 100% 100%;
            }
            &-num {
              position: absolute;
              bottom: 5px;
              color: #ffffff;
            }
          }
        }
        &-name {
          flex: 1;
        }
        &-num {
          width: 120px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
</style>
