<template>
  <el-card>
    <div class="order">
      <div class="order-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="供应商名称"
              clearable
              v-model="state.queryForm.companyName"
              style="width: 250px"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item>
            <fast-select
              v-model="state.queryForm.titleType"
              dict-type="title_type"
              placeholder="抬头类型"
              clearable
              style="width: 250px"
            >
            </fast-select>
          </el-form-item> -->
          <el-form-item>
            <fast-select
              v-model="state.queryForm.payType"
              dict-type="pay_type"
              placeholder="缴费方式"
              clearable
              style="width: 250px"
            >
            </fast-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="payTime"
              type="daterange"
              clearable
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="onChangeDate"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onClickReset()">重置</el-button>
            <!-- <el-button @click="onClickExport()" type="primary">导出</el-button> -->
          </el-form-item>
        </el-form>
      </div>
      <div class="order-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="orderNo"
            label="订单号"
            width="220"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            width="220"
            prop="tradeNo"
            label="交易订单"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              {{ scope.row.tradeNo?scope.row.tradeNo:"-" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="companyName"
            label="供应商名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="contactName"
            label="联系人"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="contactPhone"
            label="联系电话"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="orderAmount"
            label="缴费金额（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="payType"
            label="缴费方式"
            header-align="center"
            align="center"
            dict-type="pay_type"
          ></fast-table-column>
          <el-table-column
            prop="payTime"
            label="缴费时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="200"
          >
            <template #default="scope">
              <el-button type="primary" link @click="onClickDetail(scope.row.id)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="order-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <Detail
        :show="detail.show"
        :id="detail.id"
        from="purchase-order"
        @on-close="onCloseDetail"
      ></Detail>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { reactive, ref } from "vue";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import dayjs from "dayjs";
import Detail from "../../supplier/order/detail.vue";

const payTime = ref([]);

const state = reactive<IHooksOptions>({
  queryForm: {
    queryType: "1",
    companyName: "",
    titleType: "",
    payType: "",
    payTimeStart: "",
    payTimeEnd: "",
  },
  dataListUrl: "/work/record/page",
});

const { getDataList, sizeChangeHandle, currentChangeHandle, deleteHandle } = useCrud(
  state
);

const onClickReset = () => {
  state.queryForm.companyName = "";
  state.queryForm.titleType = "";
  state.queryForm.payType = "";
  state.queryForm.payTimeStart = "";
  state.queryForm.payTimeEnd = "";
  payTime.value = [];
  getDataList();
};

const onChangeDate = (value: any) => {
  if (value && value.length === 2) {
    state.queryForm.payTimeStart = dayjs(value[0]).format("YYYY-MM-DD");
    state.queryForm.payTimeEnd = dayjs(value[1]).format("YYYY-MM-DD");
  } else {
    state.queryForm.payTimeStart = "";
    state.queryForm.payTimeEnd = "";
  }
};

const onClickExport = () => {};

//#region 查看详情
interface IDetail {
  show: boolean;
  id: string;
}
const detail = reactive<IDetail>({
  show: false,
  id: "",
});

const onClickDetail = (id: string) => {
  detail.id = id;
  detail.show = true;
};

const onCloseDetail = () => {
  detail.show = false;
  detail.id = "";
};

//#endregion
</script>
