<template>
  <div class="title">
    <div class="title-left">
      <div class="title-left-icon">
        <svg-icon :icon="props.icon" class-name="svg-size" />
      </div>
      <div class="title-left-title">
        {{ props.title }}
      </div>
    </div>
    <div class="title-right">
      <slot name="right"></slot>
    </div>
  </div>
</template>
<script setup lang="ts">
interface IProps {
  icon: string;
  title: string;
}

const props = withDefaults(defineProps<IProps>(), {
  icon: "",
  title: "标题",
});
</script>
<style lang="scss" scoped>
.title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  &-left {
    display: flex;
    align-items: center;
    &-icon {
      :deep(.svg-size) {
        color: #ffffff;
        font-size: 18px;
      }
    }
    &-title {
      margin-left: 8px;
      font-size: 16px;
      font-weight: bold;
    }
  }
}
</style>
