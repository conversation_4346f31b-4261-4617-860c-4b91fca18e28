<template>
  <div class="work">
    <el-card>
      <div class="work-info">
        <div class="work-info-title">
          <div class="work-info-title-text">
            <InfoTitle title="工作经历"></InfoTitle>
          </div>
          <div class="work-info-title-divider"></div>
          <div class="info-title-action">
            <el-button type="text" @click="onClickAddWork()" v-if="allowEdit"
              >新增</el-button
            >
          </div>
        </div>
        <div class="work-info-content">
          <el-table border :data="state.dataList">
            <el-table-column
              prop="companyName"
              label="公司名称"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="position"
              label="职位"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="rzTime"
              label="入职日期"
              header-align="center"
              align="center"
            >
              <template #default="scope">
                {{ scope.row.rzTime && dayjs(scope.row.rzTime).format("YYYY-MM-DD") }}
              </template>
            </el-table-column>
            <el-table-column
              prop="certificateCode"
              label="离职日期"
              header-align="center"
              align="center"
            >
              <template #default="scope">
                {{ scope.row.lzTime && dayjs(scope.row.lzTime).format("YYYY-MM-DD") }}
              </template>
            </el-table-column>
            <el-table-column
              prop="workYear"
              label="工作时长"
              header-align="center"
              align="center"
              width="110"
            >
            </el-table-column>
            <el-table-column
              prop="lzSalary"
              label="离职工资"
              header-align="center"
              align="center"
              width="110"
            >
            </el-table-column>
            <el-table-column
              prop="userPost"
              label="岗位职责"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="lzWhy"
              label="离职原因"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="remark"
              label="备注"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              label="操作"
              header-align="center"
              align="center"
              width="150"
              v-if="allowEdit"
            >
              <template #default="scope">
                <el-button type="text" @click="onClickAddWork(scope.row.id)"
                  >编辑</el-button
                >
                <el-button type="text" @click="deleteBatchHandleWork(scope.row.id)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>
    <el-card style="margin-top: 20px">
      <div class="work-project">
        <div class="work-project-title">
          <div class="work-project-title-text">
            <InfoTitle title="项目经历"></InfoTitle>
          </div>
          <div class="work-project-title-divider"></div>
          <div class="info-title-action">
            <el-button type="text" @click="onClickAddProject()" v-if="allowEdit"
              >新增</el-button
            >
          </div>
        </div>
        <div class="work-project-content">
          <el-table border :data="project.dataList">
            <el-table-column
              prop="startTime"
              label="开始时间"
              header-align="center"
              align="center"
            >
              <template #default="scope">
                {{
                  scope.row.startTime && dayjs(scope.row.startTime).format("YYYY-MM-DD")
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="endTime"
              label="结束时间"
              header-align="center"
              align="center"
            >
              <template #default="scope">
                {{ scope.row.endTime && dayjs(scope.row.endTime).format("YYYY-MM-DD") }}
              </template>
            </el-table-column>
            <el-table-column
              prop="projectName"
              label="项目名称"
              header-align="center"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="projectRole"
              label="项目角色"
              header-align="center"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="projectRemark"
              label="项目描述"
              header-align="center"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="projectAchievement"
              label="项目业绩"
              header-align="center"
              align="center"
            >
            </el-table-column>

            <el-table-column
              label="操作"
              header-align="center"
              align="center"
              width="150"
              v-if="allowEdit"
            >
              <template #default="scope">
                <el-button type="text" @click="onClickAddProject(scope.row.id)"
                  >编辑</el-button
                >
                <el-button type="text" @click="deleteBatchHandleProject(scope.row.id)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-card>

    <ActionWork ref="workRef" @refreshDataList="getWorkList()"></ActionWork>

    <ActionProject ref="projectRef" @refreshDataList="getProjectList()"></ActionProject>
  </div>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { reactive, ref } from "vue";
import InfoTitle from "./InfoTitle.vue";
import ActionWork from "./ActionWork.vue";
import ActionProject from "./ActionProject.vue";
import { IHooksOptions } from "@/hooks/interface";
import { useCrud } from "@/hooks";

const props = withDefaults(
  defineProps<{
    userId: number;
    allowEdit?: boolean;
  }>(),
  {
    userId: 0,
    allowEdit: false,
  }
);

const dataForm = ref({});

const state: IHooksOptions = reactive({
  dataListUrl: "/work/userWork/info/list",
  deleteUrl: "/work/user/info",
  isPage: false,
  queryForm: {
    userId: props.userId,
  },
});

const { getDataList: getWorkList, deleteBatchHandle: deleteBatchHandleWork } = useCrud(
  state
);

const workRef = ref();

const onClickAddWork = (id) => {
  workRef.value.init(id);
};

const project: IHooksOptions = reactive({
  dataListUrl: "/work/userProject/info/list",
  deleteUrl: "/work/userProject/info",
  isPage: false,
  queryForm: {
    userId: props.userId,
  },
});
const {
  getDataList: getProjectList,
  deleteBatchHandle: deleteBatchHandleProject,
} = useCrud(project);

const projectRef = ref();

const onClickAddProject = (id) => {
  projectRef.value.init(id);
};
</script>
<style lang="scss" scoped>
.work {
  &-info {
    &-title {
      display: flex;
      align-items: center;
      &-divider {
        flex: 1;
        height: 1px;
        background-color: #f0f0f0;
        margin: 0 10px;
      }
    }
    &-content {
      margin-top: 20px;
    }
  }
  &-project {
    &-title {
      display: flex;
      align-items: center;
      &-divider {
        flex: 1;
        height: 1px;
        background-color: #f0f0f0;
        margin: 0 10px;
      }
    }
    &-content {
      margin-top: 20px;
    }
  }
}
</style>
