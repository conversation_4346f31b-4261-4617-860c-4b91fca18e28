<template>
  <el-dialog
    v-model="state.visible"
    title="参与信息"
    width="60%"
    :close-on-click-modal="false"
  >
    <div class="notice-search">
      <el-form
        :model="state.dataForm"
        :rules="state.dataRules"
        label-width="140px"
        class="elform"
        ref="elformRef"
        v-loading="state.floading"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="bidderName" label="供应商名称">
              <el-input
                v-model="state.dataForm.bidderName"
                readonly
                placeholder="供应商名称"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="creditCode" label="统一社会信用代码">
              <el-input
                v-model="state.dataForm.creditCode"
                readonly
                placeholder="统一社会信用代码"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="contactName" label="联系人">
              <el-input
                v-model="state.dataForm.contactName"
                placeholder="联系人"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="contactPhone" label="联系电话">
              <el-input
                v-model="state.dataForm.contactPhone"
                placeholder="联系电话"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="postalCode" label="邮编">
              <el-input
                v-model="state.dataForm.postalCode"
                placeholder="邮编"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="businessAddress" label="联系地址">
              <el-input
                v-model="state.dataForm.businessAddress"
                placeholder="联系地址"
                clearable
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="保证金附件" prop="attachmentList">
          <el-upload
            v-model:file-list="state.fileList"
            :headers="{ Authorization: cache.getToken() }"
            :action="constant.uploadUrl"
            :before-upload="beforeUpload"
            :on-success="handleSuccess"
            :on-remove="handleRemove"
            accept=".jpeg,.jpg,.png,.bmp,.gif"
            multiple
          >
            <el-button type="primary">上传</el-button>
            <template #tip>
              <div class="el-upload__tip">支持jpg/png/bmp/gif格式文件，大小不超过10M</div>
            </template>
          </el-upload>
        </el-form-item>
        <p class="ctr">
          <el-checkbox v-model="state.dataForm.check">
            <template #default>
              我已阅读并同意 <el-link type="primary">《供应商承诺函》</el-link>
            </template>
          </el-checkbox>
        </p>
      </el-form>
      <el-alert title="参与说明：" type="warning" :closable="false" />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.visible = false">取消</el-button>
        <el-button type="primary" @click="submitHandle" :loading="sloading"
          >确定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, ref, nextTick } from "vue";
import {
  usebulletinRegistrationApi,
  usebulletinRegistrationPutApi,
  useRegistrationByIdApi,
  useGetSysUserDetailApi,
} from "@/api/pnotice";
import constant from "@/utils/constant";
import { ElMessage } from "element-plus";
import cache from "@/utils/cache";

const state = reactive({
  visible: false,
  sloading: false,
  uploadList: [],
  fileList: [],
  floading: false,
  nowId: "",
  dataRules: {
    contactName: [{ required: true, message: "必填项", trigger: "blur" }],
    contactPhone: [{ required: true, message: "必填项", trigger: "blur" }],
    bidderName: [{ required: true, message: "必填项", trigger: "blur" }],
    creditCode: [{ required: true, message: "必填项", trigger: "blur" }],
    attachmentList: [{ required: true, message: "必填项", trigger: "change" }],
  },
  dataForm: {
    bidderName: "",
    contactName: "",
    contactPhone: "",
    creditCode: "",
    businessAddress: "",
    postalCode: "",
    attachmentList: [],
    check: false,
  },
});
const elformRef = ref();
const emit = defineEmits(["freshList"]);

const init = (obj, type = "new") => {
  state.visible = true;
  state.floading = true;
  if (type == "edit") {
    state.nowId = obj.id;
    getDetail();
  } else {
    Object.assign(state.dataForm, obj);
    state.dataForm.attachmentList = state.fileList = state.uploadList = [];
    state.dataForm.check = false;
    nextTick(() => {
      elformRef.value.resetFields();
      getSysUserDetail();
    });
  }
};

const getSysUserDetail = () => {
  useGetSysUserDetailApi().then((res) => {
    if (res.code == 0) {
      state.dataForm.creditCode = res.data?.creditCode;
      state.dataForm.bidderName = res.data?.companyName;
      state.dataForm.contactName = res.data?.contactName;
      state.dataForm.contactPhone = res.data?.contactPhone;
    }
    state.floading = false;
  });
};
// 详情
const getDetail = () => {
  useRegistrationByIdApi(state.nowId).then((res) => {
    if (res.code == 0) {
      Object.assign(state.dataForm, res.data);
      if (state.dataForm.attachmentList) {
        state.fileList = state.uploadList = state.dataForm.attachmentList;
      }
    }
    state.floading = false;
  });
};

const handleSuccess = (res, file, files) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  state.uploadList = [];
  for (let i of state.fileList) {
    if ("response" in i) {
      state.uploadList.push(i.response.data);
    } else {
      state.uploadList.push(i);
    }
  }
  Object.assign(state.dataForm.attachmentList, state.uploadList);
  // console.log(state.uploadList)
};
const handleRemove = (file, files) => {
  state.uploadList = [];
  for (let i of files) {
    if ("response" in i) {
      state.uploadList.push(i.response.data);
    } else {
      state.uploadList.push(i);
    }
  }
  state.dataForm.attachmentList = state.uploadList;
  // Object.assign(state.dataForm.attachmentList, state.uploadList);
};
const beforeUpload = (file) => {
  let types = ["image/jpeg", "image/png", "image/bmp", "image/gif"];
  if (!types.includes(file.type)) {
    ElMessage.error("格式不支持");
    return false;
  }
  return true;
};
// 表单提交
const submitHandle = () => {
  if (!state.dataForm.check) {
    ElMessage.error("请阅读并同意《供应商承诺函》");
    return;
  }
  elformRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    } else {
      state.sloading = true;
      if (state.nowId) {
        usebulletinRegistrationPutApi(state.dataForm).then((res) => {
          if (res.code == 0) {
            ElMessage.success("参与成功");
            state.visible = false;
            state.sloading = false;
            emit("freshList");
          }
        });
      } else {
        usebulletinRegistrationApi(state.dataForm).then((res) => {
          if (res.code == 0) {
            ElMessage.success("参与成功");
            state.visible = false;
            state.sloading = false;
            emit("freshList");
          }
        });
      }
    }
  });
};

defineExpose({
  init,
});
</script>
<style lang="scss" scoped>
.elform {
  width: 95%;
}
.ctr {
  text-align: center;
  margin: 5px 0;
}
</style>
