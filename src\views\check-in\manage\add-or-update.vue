<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.id ? '新增考勤组' : '修改考勤组'"
    :close-on-click-modal="false"
    fullscreen
    @close="close"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="100px"
      @keyup.enter="submitHandle()"
    >
      <div class="content">
        <el-form-item label="考勤组名称" prop="name">
          <el-input v-model="dataForm.name" placeholder="考勤组名称"></el-input>
        </el-form-item>
        <el-form-item label="适用人员" prop="userList">
          <div class="content-user">
            <el-tag
              v-for="user in dataForm.userList"
              :key="user.userId"
              closable
              :disable-transitions="false"
              @close="onClickCloseUser(user.userId)"
              style="margin-right: 10px"
            >
              {{ user.username }}
            </el-tag>
            <el-button @click="onClickSelectUser" size="small" style="margin-right: 10px"
              >新增</el-button
            >
          </div>
        </el-form-item>
        <!-- <el-form-item label="考勤组负责人" prop="shiftType">
          <el-input v-model="dataForm.shiftType" placeholder="考勤组负责人"></el-input>
        </el-form-item> -->
        <div class="content-title">
          <div class="content-title-before"></div>
          <div class="content-title-label">考勤班次</div>
          <div class="content-title-divider"></div>
        </div>
        <el-form-item label="班次类型" prop="shiftName">
          <el-radio-group v-model="dataForm.shiftType">
            <el-radio :value="0">固定班制</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="班次时间段" prop="workTime">
          <div style="width: 200px">
            <el-time-picker
              v-model="dataForm.workTime"
              is-range
              start-placeholder="上班时间"
              end-placeholder="下班时间"
              format="HH:mm"
              value-format="HH:mm"
              :clearable="false"
              @change="onChangeWorkTime"
            />
          </div>
        </el-form-item>
        <el-form-item label="工作日设置" prop="workSetting">
          <el-table
            border
            ref="workSettingRef"
            :data="state.weekdayList"
            @selection-change="onSelectionChange"
            row-key="weekday"
          >
            <el-table-column type="selection" width="55" reserve-selection />
            <el-table-column property="weekday" label="工作日" />
            <el-table-column property="workTime" label="班次时间段">
              <template #default="scope">
                <div>
                  <div v-if="!scope.row.checked">休息</div>
                  <div v-else>
                    {{ scope.row.workStartTime }} - {{ scope.row.workEndTime }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column property="action" label="操作">
              <template #default="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="onClickSetWorkTime(scope.row)"
                  v-if="scope.row.checked"
                  >设置</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
        <el-form-item label=" " prop="legalHolidays">
          <div class="content-setting">
            <div class="content-setting-legal">
              <el-checkbox
                v-model="dataForm.legalHolidays"
                :true-value="1"
                :false-value="0"
              >
                法定节假日自动排休
              </el-checkbox>
            </div>
            <div class="content-setting-more">
              <div class="content-setting-more-action">
                <el-button type="text" @click="onClickSetMore">
                  <svg-icon
                    icon="icon-setting1"
                    color="var(--el-color-primary)"
                    style="margin-right: 6px"
                  ></svg-icon>
                  更多设置
                </el-button>
              </div>
              <div class="content-setting-more-desc"></div>
            </div>
          </div>
        </el-form-item>
        <div class="content-title">
          <div class="content-title-before"></div>
          <div class="content-title-label">打卡方式</div>
          <div class="content-title-divider"></div>
        </div>
        <el-form-item label="办公地址考勤" prop="workStartTime">
          <div class="content-address" style="width: 100%">
            <div class="content-address-action">
              <el-button type="text" @click="onClickAddAddress('add')">
                <svg-icon
                  icon="icon-add"
                  color="var(--el-color-primary)"
                  style="margin-right: 6px"
                ></svg-icon>
                添加地址
              </el-button>
            </div>
            <div class="content-address-list">
              <el-table border :data="dataForm.locationList">
                <el-table-column prop="name" label="考勤地点" />
                <fast-table-column
                  prop="checkInScope"
                  label="有效范围"
                  dict-type="check_in_scope"
                ></fast-table-column>
                <el-table-column prop="action" label="操作">
                  <template #default="scope">
                    <el-button type="text" size="small" @click="onClickAddAddress('edit',scope.row,scope.$index)">编辑</el-button>
                    <el-button type="text" size="small" @click="onClickDeleteAddress(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-form-item>
        <div class="content-title">
          <div class="content-title-before"></div>
          <div class="content-title-label">更多设置</div>
          <div class="content-title-divider"></div>
        </div>
        <el-form-item label="允许外勤打卡" prop="outdoorWork">
          <div class="content-outside">
            <div class="content-outside-action">
              <el-switch
                :active-value="1"
                :inactive-value="0"
                v-model="dataForm.allowOutdoorWorkCheckIn"
              />
            </div>
            <div class="content-outside-remark">
              <el-checkbox
                v-model="dataForm.outdoorWorkRemark"
                :true-value="1"
                :false-value="0"
              >
                外勤打卡需要填写备注
              </el-checkbox>
            </div>
            <div class="content-outside-image">
              <el-checkbox
                v-model="dataForm.outdoorWorkPhotograph"
                :true-value="1"
                :false-value="0"
              >
                外勤打卡需要拍照
              </el-checkbox>
            </div>
            <div class="content-outside-desc">超出考勤有效范围视为外勤签到。</div>
          </div>
        </el-form-item>
      </div>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
  <el-dialog v-model="state.workTimeSet.visible" title="设置班次时间段" width="450px">
    <el-form :model="state.workTimeSet" ref="workTimeSetRef" label-width="120px">
      <el-form-item label-width="0px" prop="workTime">
        <el-time-picker
          v-model="state.workTimeSet.workTime"
          is-range
          start-placeholder="上班时间"
          end-placeholder="下班时间"
          format="HH:mm"
          value-format="HH:mm"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="onCancelWorkTimeSet()">取消</el-button>
      <el-button type="primary" @click="onConfirmWorkTimeSet()">确定</el-button>
    </template>
  </el-dialog>
  <el-dialog v-model="state.moreSet.visible" title="更多设置" width="650px">
    <div class="content-setting-more">
      <el-form :model="state.moreSet" ref="moreSetRef" label-width="120px">
        <div class="content-setting-more-title">打卡时间范围限制</div>
        <el-form-item label="上班" prop="outdoorWork">
          <div style="display: flex; align-items: center">
            <el-time-picker
              v-model="state.moreSet.startClockInAt"
              is-range
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="HH:mm"
              value-format="HH:mm"
              :clearable="false"
            />
            <div style="margin-left: 10px">可打上班卡</div>
          </div>
        </el-form-item>
        <el-form-item label="下班" prop="outdoorWork">
          <div style="display: flex; align-items: center">
            <el-time-picker
              v-model="state.moreSet.startClockInAfter"
              is-range
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="HH:mm"
              value-format="HH:mm"
              :clearable="false"
            />
            <div style="margin-left: 10px">可打下班卡</div>
          </div>
        </el-form-item>
        <div class="content-setting-more-desc">在选择时间的范围以外不可以打卡</div>
        <div class="content-setting-more-title">
          <div>设定特殊日期</div>
          <div>
            <el-button type="primary" @click="onActionSpecialDate" size="small"
              >添加特殊日期</el-button
            >
          </div>
        </div>
        <div style="height: 350px; overflow-y: auto">
          <div
            v-for="(item, index) in state.moreSet.specialList"
            style="margin-bottom: 10px"
          >
            <SpecialDate
              :specialDate="item"
              :index="index"
              @click-delete="onClickDeleteSpecialList(index)"
            ></SpecialDate>
          </div>
        </div>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="onCancelMoreSet()">取消</el-button>
      <el-button type="primary" @click="onConfirmMoreSet()">确定</el-button>
    </template>
  </el-dialog>
  <el-dialog
    v-model="state.sameGroup.visible"
    title="以下人员已在其他考勤组"
    width="450px"
    @close="onCloseSameGroup"
  >
    <div>
      <div style="color: red">以下人员已在其他考勤组,请先移除</div>
      <div style="margin-top: 10px">
        <el-table
          :data="state.sameGroup.list"
          border
          style="width: 100%"
          :max-height="450"
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="realName"
            label="员工"
            header-align="center"
            align="center"
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </el-dialog>

  <!-- 选择成员 -->
  <select-user-tree
    ref="selectUserTreeRef"
    :maxSelectNumber="0"
    @select-user="getSelectUser"
  ></select-user-tree>

  <ActionAddress ref="actionAddressRef" @submitData="onSubmitAddress"></ActionAddress>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { ElMessage } from "element-plus/es";
import {
  useAttendanceGroupApi,
  useAttendanceGroupSubmitApi,
} from "@/api/check-in/manage";
import dayjs from "dayjs";

import SpecialDate from "./special-date.vue";
import SelectUserTree from "@/views/workbench/components/selectUserTree.vue";
import ActionAddress from "./action-address.vue";

const emit = defineEmits(["refreshDataList", "close"]);

const visible = ref(false);
const dataFormRef = ref();

const workTimeSetRef = ref();

const state = reactive({
  weekdayList: [
    {
      weekday: "星期一",
      workStartTime: "",
      workEndTime: "",
      checked: false,
    },
    {
      weekday: "星期二",
      workStartTime: "",
      workEndTime: "",
      checked: false,
    },
    {
      weekday: "星期三",
      workStartTime: "",
      workEndTime: "",
      checked: false,
    },
    {
      weekday: "星期四",
      workStartTime: "",
      workEndTime: "",
      checked: false,
    },
    {
      weekday: "星期五",
      workStartTime: "",
      workEndTime: "",
      checked: false,
    },
    {
      weekday: "星期六",
      workStartTime: "",
      workEndTime: "",
      checked: false,
    },
    {
      weekday: "星期日",
      workStartTime: "",
      workEndTime: "",
      checked: false,
    },
  ],
  workTimeSet: {
    visible: false,
    weekday: "",
    workTime: ["09:00", "18:00"],
  },
  moreSet: {
    visible: false,
    startClockInAt: ["09:00", "12:00"],
    startClockInAfter: ["12:00", "18:00"],
    specialList: [],
  },
  optionUser: [],
  sameGroup: {
    visible: false,
    list: [],
  },
});

const dataForm = reactive({
  id: "",
  name: "",
  userList: [],
  shiftType: 0,
  workTime: ["09:00", "18:00"],
  workStartTime: "09:00",
  workEndTime: "18:00",
  weekdayList: [],
  legalHolidays: 0,
  startClockInAtWork: "00:00",
  endClockInAtWork: "12:00",
  startClockInAfterWork: "12:00",
  endClockInAfterWork: "23:00",
  specialList: [],
  locationList: [],
  allowOutdoorWorkCheckIn: 0,
  outdoorWorkRemark: 0,
  outdoorWorkPhotograph: 0,
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getAttendanceGroup(id);
  }
};

const setASpecialDate=()=>{
  state.moreSet.specialList=dataForm.specialList;
  if(state.moreSet.specialList.length>0){
    for (const item of state.moreSet.specialList) {
      item.workTime=[]
      item.workTime[0]=item.workStartTime
      item.workTime[1]=item.workEndTime
    }
  }
}

const getAttendanceGroup = (id: number) => {
  useAttendanceGroupApi(id).then((res) => {
    Object.assign(dataForm, res.data);
    dataForm.workTime[0]=res.data.workStartTime
    dataForm.workTime[1]=res.data.workEndTime 
    state.moreSet.startClockInAt[0] = dataForm.startClockInAtWork;
    state.moreSet.startClockInAt[1] = dataForm.endClockInAtWork;
    state.moreSet.startClockInAfter[0]=dataForm.startClockInAfterWork;
    state.moreSet.startClockInAfter[1]=dataForm.endClockInAfterWork;
    setASpecialDate()
    dataForm.userList = res.data.userList.map((item) => {
      return {
        userId: item.userId,
        username: item.realName,
      };
    });
    let newWeekdayList = JSON.parse(JSON.stringify(state.weekdayList));
    newWeekdayList.forEach((item) => {
      let index = res.data.weekdayList.findIndex((ele) => ele.weekday === item.weekday);
      if (index !== -1) {
        item.checked = true;
        item.workStartTime = res.data.weekdayList[index].workStartTime;
        item.workEndTime = res.data.weekdayList[index].workEndTime;
      }
    });
    state.weekdayList = newWeekdayList;
    state.weekdayList.forEach((item) => {
      if (item.checked) {
        workSettingRef.value.toggleRowSelection(item, true);
      } else {
        workSettingRef.value.toggleRowSelection(item, false);
      }
    });
  });
};

const dataRules = ref({
  name: [{ required: true, message: "名称不能为空", trigger: "blur" }],
  userList: [{ required: true, message: "适用人员不能为空", trigger: "blur" }],
});

//#region 选择地址

const actionAddressRef = ref();

const onClickAddAddress = (type:string,row:any,index:number) => {
  actionAddressRef.value.init(type,row,index);
};

const onSubmitAddress = (data:any,type:string,index:number) => {
  let newLocationList = JSON.parse(JSON.stringify(dataForm.locationList));

  if(type=='add'){
    newLocationList.push({
      name: data.name,
      attendanceLocation: data.attendanceLocation,
      longitude: data.longitude,
      latitude: data.latitude,
      checkInScope: data.checkInScope,
    });
  }else{
    let item= JSON.parse(JSON.stringify(data));
    newLocationList[index]=item
  }
    dataForm.locationList = newLocationList;
};

const onClickDeleteAddress = (index: number) => {
  let newLocationList = JSON.parse(JSON.stringify(dataForm.locationList));
  newLocationList.splice(index, 1);
  dataForm.locationList = newLocationList;
};

//#endregion

// 表单提交
const submitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    let newDataForm = JSON.parse(JSON.stringify(dataForm));
    newDataForm.userList = dataForm.userList.map((item) => {
      return {
        userId: item.userId,
      };
    });
    newDataForm.workStartTime = dayjs(
      "1991-01-01 " + newDataForm.workStartTime + ":00"
    ).format("HH:mm:ss");
    newDataForm.workEndTime = dayjs(
      "1991-01-01 " + newDataForm.workEndTime + ":00"
    ).format("HH:mm:ss");
    let newWeekdayList = [];
    state.weekdayList.forEach((item) => {
      if (item.checked) {
        newWeekdayList.push({
          weekday: item.weekday,
          workStartTime: dayjs("1991-01-01 " + item.workStartTime + ":00").format(
            "HH:mm:ss"
          ),
          workEndTime: dayjs("1991-01-01 " + item.workEndTime + ":00").format("HH:mm:ss"),
        });
      }
    });
    newDataForm.weekdayList = newWeekdayList;
    newDataForm.startClockInAtWork = dayjs(
      "1991-01-01 " + newDataForm.startClockInAtWork + ":00"
    ).format("HH:mm:ss");
    newDataForm.endClockInAtWork = dayjs(
      "1991-01-01 " + newDataForm.endClockInAtWork + ":00"
    ).format("HH:mm:ss");
    newDataForm.startClockInAfterWork = dayjs(
      "1991-01-01 " + newDataForm.startClockInAfterWork + ":00"
    ).format("HH:mm:ss");
    newDataForm.endClockInAfterWork = dayjs(
      "1991-01-01 " + newDataForm.endClockInAfterWork + ":00"
    ).format("HH:mm:ss");
    newDataForm.specialList = newDataForm.specialList.map((item) => {
      return {
        specialDate:item.specialDate,
        // specialDay: item.specialDay,
        type: item.type,
        workStartTime:dayjs("1991-01-01 " + item.workTime[0] + ":00").format("HH:mm:ss"),
        workEndTime:dayjs("1991-01-01 " + item.workTime[1] + ":00").format("HH:mm:ss")
        // specialStartTime: dayjs("1991-01-01 " + item.specialStartTime + ":00").format(
        //   "HH:mm:ss"
        // ),
        // specialEndTime: dayjs("1991-01-01 " + item.specialEndTime + ":00").format(
        //   "HH:mm:ss"
        // ),
      };
    });
    newDataForm.locationList = newDataForm.locationList.map((item) => {
      return {
        name: item.name,
        attendanceLocation: item.attendanceLocation,
        longitude: item.longitude,
        latitude: item.latitude,
        checkInScope: item.checkInScope,
      };
    });
    useAttendanceGroupSubmitApi(newDataForm).then((res) => {
      if (res.code === 0) {
        if ((res.data || []).length > 0) {
          state.sameGroup.list = res.data;
          state.sameGroup.visible = true;
        } else {
          ElMessage.success({
            message: "操作成功",
            duration: 500,
            onClose: () => {
              visible.value = false;
              close();
              emit("refreshDataList");
            },
          });
        }
      }
    });
  });
};

const close = () => {
  emit("close");
};

const onCloseSameGroup = () => {
  state.sameGroup.visible = false;
  state.sameGroup.list = [];
  // visible.value = false;
  // emit("refreshDataList");
};

const onCancelWorkTimeSet = () => {
  state.workTimeSet.visible = false;
};
const onConfirmWorkTimeSet = () => {
  workTimeSetRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    let newWeekdayList = JSON.parse(JSON.stringify(state.weekdayList));
    let weekdayIndex = newWeekdayList.findIndex(
      (item) => item.weekday === state.workTimeSet.weekday
    );
    newWeekdayList[weekdayIndex].workStartTime = state.workTimeSet.workTime[0];
    newWeekdayList[weekdayIndex].workEndTime = state.workTimeSet.workTime[1];
    state.weekdayList = newWeekdayList;
    state.workTimeSet.visible = false;
  });
};

const onChangeWorkTime = (value) => {
  if (value && value.length === 2) {
    dataForm.workStartTime = value[0];
    dataForm.workEndTime = value[1];
    let newWeekdayList = JSON.parse(JSON.stringify(state.weekdayList));
    newWeekdayList.forEach((item) => {
      if (item.weekday === "星期六" || item.weekday === "星期日") {
        item.workStartTime = value[0];
        item.workEndTime = value[1];
        item.type = 0;
      } else {
        item.workStartTime = value[0];
        item.workEndTime = value[1];
        item.type = 1;
      }
    });
    state.weekdayList = newWeekdayList;
  } else {
    dataForm.workStartTime = "";
    dataForm.workEndTime = "";
  }
};

const workSettingRef = ref();

const onSelectionChange = (value) => {
  let newWeekdayList = JSON.parse(JSON.stringify(state.weekdayList));
  newWeekdayList.forEach((item) => {
      let index = value.findIndex((ele) => ele.weekday === item.weekday);

    if (value.findIndex((i) => i.weekday === item.weekday) !== -1) {
      item.workStartTime = value[index].workStartTime?value[index].workStartTime:dataForm.workStartTime;
      item.workEndTime = value[index].workEndTime?value[index].workEndTime:dataForm.workEndTime;
      item.checked = true;
    } else {
      item.workStartTime = "";
      item.workEndTime = "";
      item.checked = false;
    }
  });
  state.weekdayList = newWeekdayList;
};

//#region 设置工作时间
const onClickSetWorkTime = (row) => {
  state.workTimeSet.weekday = row.weekday;
  state.workTimeSet.workTime = [row.workStartTime, row.workEndTime];
  state.workTimeSet.visible = true;
};
//#endregion

//#region 选择成员

const selectUserTreeRef = ref();

const onClickSelectUser = () => {
  let newUserList = JSON.parse(JSON.stringify(dataForm.userList));
  newUserList = newUserList.map((item) => {
    return { id: item.userId, name: item.username };
  });
  selectUserTreeRef.value?.init(newUserList);
};

const getSelectUser = (val) => {
  let newUserList = [];
  val.forEach((item) => {
    if (newUserList.findIndex((i) => i.userId == item.id) == -1) {
      newUserList.push({
        userId: item.id,
        username: item.name,
      });
    }
  });
  dataForm.userList = newUserList;
};

const onClickCloseUser = (userId) => {
  let newUserList = JSON.parse(JSON.stringify(dataForm.userList));
  newUserList = newUserList.filter((item) => item.userId != userId);
  dataForm.userList = newUserList;
};

//#endregion

//#region 更多设置
const onClickSetMore = () => {
  state.moreSet.visible = true;
  setASpecialDate()
};

const onActionSpecialDate = () => {
  let newSpecialList = JSON.parse(JSON.stringify(state.moreSet.specialList));
  if (
    newSpecialList.findIndex((item) => {
      return (item.specialDate ?? "") == "";
    }) !== -1
  ) {
    ElMessage.error("已有数据日期不能为空!");
  } else if (
    newSpecialList.findIndex((item) => {
      return item.type == 1 && (!item.workTime || item.workTime.length < 2);
    }) !== -1
  ) {
    ElMessage.error("工作日模式下班次时间段不能为空!");
  } else {
    newSpecialList.push({
      specialDate: "",
      type: 0,
      workTime: ["09:00", "18:00"],
    });
  }
  state.moreSet.specialList = newSpecialList;
};

const onConfirmMoreSet = () => {
  state.moreSet.visible = false;
  dataForm.startClockInAtWork = state.moreSet.startClockInAt[0];
  dataForm.endClockInAtWork = state.moreSet.startClockInAt[1];
  dataForm.startClockInAfterWork = state.moreSet.startClockInAfter[0];
  dataForm.endClockInAfterWork = state.moreSet.startClockInAfter[1];
  dataForm.specialList = state.moreSet.specialList;
};

const onCancelMoreSet = () => {
  state.moreSet.visible = false;
  state.moreSet.specialList = [];
  state.moreSet.startClockInAt = ["00:00", "12:00"];
  state.moreSet.startClockInAfter = ["12:00", "23:00"];
};

const onClickDeleteSpecialList = (index) => {
  let newSpecialList = JSON.parse(JSON.stringify(state.moreSet.specialList));
  newSpecialList.splice(index, 1);
  state.moreSet.specialList = newSpecialList;
};
//#endregion

defineExpose({
  init,
});
</script>
<style lang="scss" scoped>
.content {
  padding: 30px;
  &-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    &-before {
      width: 6px;
      height: 18px;
      background-color: rgba(37, 97, 239, 1);
      border-radius: 8px;
    }
    &-label {
      margin: 0 10px;
      font-size: 16px;
      color: #333333;
    }
    &-divider {
      flex: 1;
      height: 1px;
      background-color: #f0f0f0;
    }
  }
  &-outside {
    &-desc {
      font-size: 14px;
      color: #999999;
    }
  }
}

.content-setting-more {
  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 10px;
    margin-bottom: 20px;
  }
  &-desc {
    font-size: 12px;
    color: #999999;
    margin-left: 80px;
  }
}
</style>
