<template>
  <el-card>
    <div class="notice">
      <div class="notice-search">
        <el-form
          ref="elFormRef"
          :inline="true"
          :model="state.queryForm"
          @keyup.enter="getDataList()"
        >
          <el-form-item prop="title">
            <el-input
              placeholder="请输入公告标题"
              clearable
              v-model="state.queryForm.title"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="resetForm()">重置</el-button>
            <el-button type="primary" @click="openNotice('', '')">发布公告</el-button>
            <el-button @click="deleteBatchHandle()">批量删除</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="notice-list">
        <el-table
          v-loading="state.dataListLoading"
          :data="state.dataList"
          @selection-change="handleSelectionChange"
          border
        >
          <el-table-column type="selection" :selectable="selectable" width="55">
          </el-table-column>
          <el-table-column
            prop="title"
            label="公告标题"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="createName"
            label="发布人"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="status"
            label="发布状态"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              <span v-if="scope.row.status == 1" style="color: #67c23a">已发布</span>
              <span v-else style="color: #7f7f7f">未发布</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="yesRead"
            label="已读"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="noRead"
            label="未读"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="createTime"
            label="发布时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="200"
          >
            <template #default="scope">
              <el-button
                v-if="scope.row.status == 0 && userId == scope.row.creator"
                type="primary"
                link
                @click="openNotice(scope.row.id, 'edit')"
              >
                编辑
              </el-button>
              <el-button
                v-else
                type="primary"
                link
                @click="openNotice(scope.row.id, 'look')"
              >
                查看
              </el-button>
              <el-button
                type="primary"
                v-if="userId == scope.row.creator"
                link
                @click="viewHandle(scope.row.id)"
              >
                查阅情况
              </el-button>
              <el-button
                type="primary"
                v-if="userId == scope.row.creator"
                link
                @click="deleteBatchHandle(scope.row.id)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="notice-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
    </div>
  </el-card>
  <!-- 查阅情况 -->
  <el-dialog
    v-model="viewVueShow"
    title="查阅情况"
    :width="450"
    :close-on-click-modal="false"
    draggable
    @close="viewVueShow = false"
  >
    <div class="viewQkBox">
      <el-tabs v-model="activeName">
        <el-tab-pane :label="'未读' + unreadNum" name="first">
          <div class="unBox" v-for="(item, index) in unreadList" :key="index">
            <div style="width: 40px">
              <el-avatar
                shape="circle"
                :size="30"
                :src="item.avatar ?? defaultAvatar"
              ></el-avatar>
            </div>
            <div class="inner1">{{ item.sendUserName }}</div>
            <div class="inner1">{{ item.orgName }}</div>
            <div class="inner1" style="color: red">未读</div>
          </div>
        </el-tab-pane>
        <el-tab-pane :label="'已读' + readNum" name="second">
          <div class="unBox" v-for="(item, index) in readList" :key="index">
            <div style="width: 40px">
              <el-avatar
                shape="circle"
                :size="30"
                :src="item.avatar ?? defaultAvatar"
              ></el-avatar>
            </div>
            <div class="inner1">{{ item.sendUserName }}</div>
            <div class="inner1">{{ item.orgName }}</div>
            <div style="color: rgb(153, 153, 153); width: 180px">
              已读&nbsp;&nbsp;&nbsp;{{ item.readTime }}
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="viewVueShow = false">关闭</el-button>
      <!-- <el-button type="primary" @click="viewVueShow = false">确 定</el-button> -->
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { nextTick, reactive, ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import auditlog from "./auditlog.vue";
import viewVue from "./view.vue";
import { morePersonGetInfo } from "@/api/pnotice";
import { useUserStore } from "@/store/modules/user";
import defaultAvatar from "@/assets/avatar.png";

const state: IHooksOptions = reactive({
  dataListUrl: "/work/bulletin/info/page",
  deleteUrl: "/work/bulletin/info",
  dataListSelections: [],
  queryForm: {
    title: "",
  },
  value: "",
});
const router = useRouter();
const viewVueRef = ref();
const elFormRef = ref();
const viewVueShow = ref(false);
const activeName = ref("first");
const readNum = ref(0);
const unreadNum = ref(0);
const readList = ref([]);
const unreadList = ref([]);
const userStore = useUserStore();
const userId = ref(userStore.user.id);
onMounted(() => {
  // console.info(userStore)
});
// 新增编辑
const openNotice = (id: "", type: "") => {
  if (id !== "") {
    router.push({
      path: "/moreUse/action",
      query: { id, type },
    });
  } else {
    router.push("/moreUse/action");
  }
};
const handleSelectionChange = (val) => {
  let selId = [];
  for (let item of val) {
    selId.push(item.id);
  }
  state.dataListSelections = selId;
};
// 查阅情况
const viewHandle = (id: any) => {
  morePersonGetInfo(id).then((res) => {
    unreadList.value = res.data.oaBulletinPersonVOSNo;
    readList.value = res.data.oaBulletinPersonVOSYES;
    readNum.value = res.data.oaBulletinPersonVOSYES.length;
    unreadNum.value = res.data.oaBulletinPersonVOSNo.length;
  });

  activeName.value = "first";
  viewVueShow.value = true;
};

const resetForm = () => {
  elFormRef.value.resetFields();
  getDataList(1);
};

const { getDataList, deleteBatchHandle, sizeChangeHandle, currentChangeHandle } = useCrud(
  state
);

const selectable = (row) => {
  return row.creator == userId.value;
};
</script>
<style lang="scss" scoped>
.notice {
  &-list {
    margin-top: 16px;
  }
}

.viewQkBox {
  margin-bottom: 30px;
}

.dialog-footer {
  text-align: center;
}

.unBox {
  display: flex;
  align-items: center;
  justify-content: start;
  padding: 15px;
  border-bottom: 1px solid rgba(242, 242, 242, 1);

  .inner1 {
    width: 80px;
    text-align: center;
  }
}
</style>
