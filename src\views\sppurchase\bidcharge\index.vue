<template>
  <el-card>
    <div class="notice">
      <div class="notice-search">
        <el-form
          ref="elFormRef"
          :inline="true"
          :model="state.queryForm"
          @keyup.enter="getDataList()"
        >
          <el-form-item prop="packageName">
            <el-input
              v-model="state.queryForm.packageName"
              placeholder="采购计划名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="orderStatus">
            <fast-select
              v-model="state.queryForm.orderStatus"
              dict-type="order_status"
              clearable
              placeholder="支付状态"
            ></fast-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="notice-list">
        <el-table v-loading="state.dataListLoading" :data="state.dataList" border>
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="winBidder"
            label="采购单位"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="orderStatus"
            label="支付状态"
            header-align="center"
            align="center"
            dict-type="order_status"
          >
          </fast-table-column>
          <el-table-column
            prop="winPrice"
            label="中标金额（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="serviceCharge"
            label="中标服务费金额（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="160"
          >
            <template #default="scope">
              <el-button
                v-if="scope.row.orderStatus == '00'"
                type="primary"
                link
                @click="openPay(scope.row.packageId)"
              >
                支付
              </el-button>
              <el-button v-else type="primary" link @click="viewHandle(scope.row.id)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="notice-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
    </div>
  </el-card>
  <!-- 查看 -->
  <view-vue ref="viewVueRef" v-if="state.viewVueShow"></view-vue>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { nextTick, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import viewVue from "./view.vue";

const state: IHooksOptions = reactive({
  dataListUrl: "/purchase/winBidder/bidderPage",
  queryForm: {
    packageName: "",
    orderStatus: "",
  },
  viewVueShow: false,
});
const router = useRouter();
const elFormRef = ref();
const viewVueRef = ref();

// 支付
const openPay = (pid) => {
  if (pid) {
    router.push({
      path: "/sppurchase/bidcharge/pay",
      query: { pid },
    });
  }
};
// 查看
const viewHandle = (id) => {
  state.viewVueShow = true;
  nextTick(() => {
    viewVueRef.value.init(id);
  });
};
const resetForm = () => {
  elFormRef.value.resetFields();
  getDataList(1);
};

const { getDataList, deleteBatchHandle, sizeChangeHandle, currentChangeHandle } = useCrud(
  state
);
</script>
<style lang="scss" scoped>
.notice {
  &-list {
    margin-top: 16px;
  }
}
</style>
