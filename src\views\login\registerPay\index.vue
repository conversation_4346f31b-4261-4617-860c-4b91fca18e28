<template>
  <div class="register">
    <div class="register-header">
      <Header title="用户注册"></Header>
    </div>
    <div class="register-body">
      <div class="register-form">
        <el-form ref="dataFormRef" :model="state.dataForm" :rules="state.dataRules" label-width="120px" @keyup.enter="onClickSubmit">
          <el-form-item label="主体类型：">
            <span v-if="state.queryInfo.subjectType==='01'">供应商</span>
            <span v-if="state.queryInfo.subjectType==='02'">个人</span>
          </el-form-item>
          <el-form-item label="供应商名称：" v-if="state.queryInfo.subjectType==='01'">
            <span>{{state.queryInfo.companyName}}</span>
          </el-form-item>
          <el-form-item label="联系人：">
            <span>{{state.queryInfo.contactName}}</span>
          </el-form-item>
          <el-form-item label="联系电话：">
            <span>{{state.queryInfo.contactPhone}}</span>
          </el-form-item>
          <el-form-item label="支付金额(元)：">
            <span>{{state.queryInfo.payAmount}}</span>
          </el-form-item>
          <el-form-item label="支付方式：" prop="payType">
            <span class="common-check-box">
              <span v-for="(item,index) in payTypeList" :key="index" class="common-check" :class="state.dataForm.payType===item.dictValue?'common-check-selected':''" @click="payTypeClick(item.dictValue)">
                <img v-if="item.dictValue==='AlipaySaoma'" class="pay-type-icon" src="@/assets/image/register/alipay.png" alt="">
                <img v-if="item.dictValue==='MicroNative'" class="pay-type-icon" src="@/assets/image/register/wechat.png" alt="">
                <img v-if="item.dictValue==='AllinpayUnitorder_W01'||item.dictValue==='AllinpayUnitorder_A01'" class="pay-type-icon" src="@/assets/image/register/tonglian.png" alt="">
                {{item.dictLabel}}支付</span>
            </span>
          </el-form-item>
          <el-form-item label="备注：" prop="remarks">
            <el-input type="textarea" rows="3" v-model="state.dataForm.remarks"></el-input>
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" @click="onClickSubmit">立即支付</el-button>
            <el-button @click="onClickBack">返回</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="register-footer">
      <Footer></Footer>
    </div>
    <!-- 支付宝支付-跳转html页面 -->
    <div v-html="state.alipaywap" ref="alipaywap"></div>
  </div>
</template>
<script setup lang="ts">
import { ElMessage, FormRules } from "element-plus";
import { reactive, ref,onMounted,nextTick } from "vue";
import Header from "../components/Header.vue";
import Footer from "../components/Footer.vue";
import { useRoute,useRouter } from "vue-router";
import { registerScanCodeBuyApi,getByCodeApi } from "@/api/auth";

const route = useRoute();
const router = useRouter();
const dataFormRef = ref();
const payTypeList = ref([])
// interface IDataForm {
//   payType: string;
//   remarks: string;
// }
// interface IState {
//   dataForm: IDataForm;
//   dataRules: FormRules;
// }
const state = reactive({
  dataForm: {
    payType:"",
    payTypeName:"",
    remarks:""
  },
  dataRules: {
    payType: [{ required: true, message: "请选择支付方式", trigger: "change" }],
  },
  queryInfo:{
    userId:"",
    payAmount:"",
    subjectType:"",
    contactName:"",
    contactPhone:"",
    companyName:''
  },
  alipaywap:""
});
onMounted(() => {
  state.queryInfo=route.query
  getPayTypeList()
})
const alipaywap=ref()
const onClickSubmit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      if(state.dataForm.payType==="AlipaySaoma"){
        // 支付宝
        let reqData={
          creator:parseInt(state.queryInfo.userId),
          goodsId:parseInt(state.queryInfo.userId),
          orderAmount:parseFloat(state.queryInfo.payAmount),
          payType:state.dataForm.payType,
          remarks:state.dataForm.remarks
        }
        registerScanCodeBuyApi(reqData).then((res: any) => {
          if (res.code === 0) {
            let resData=res.data
            // 打开html
            state.alipaywap = resData.urlCode
            nextTick(()=>{
              alipaywap.value.children[0].submit()
            })
          }
        })
      }else{
        // 非支付宝
        let payInfo={
          payType:state.dataForm.payType,
          payTypeName:state.dataForm.payTypeName,
          remarks:state.dataForm.remarks
        }
        let query={
          payQueryInfo:JSON.stringify(payInfo),
          registerQueryInfo:JSON.stringify(state.queryInfo)
        }
        router.replace({path:"/registerScan",query:query});
      }
    }
  })
}
const onClickBack = () => {
  let query={
    fromPage:"registerPay"
  }
  router.replace({path:"/register",query:query});
}
const payTypeClick=(val:any)=>{
  state.dataForm.payType=val
  let payTypeName=""
  if(val==="AlipaySaoma"||val==="AllinpayUnitorder_A01"){
    payTypeName="支付宝"
  }else if(val==="MicroNative"||val==="AllinpayUnitorder_W01"){
    payTypeName="微信"
  }
  state.dataForm.payTypeName=payTypeName
}
const getPayTypeList=()=>{
  getByCodeApi('pay_type_zc').then((res: any) => {
    if (res.code === 0) {
      payTypeList.value=res.data
    }
  })
}
</script>
<style lang="scss" scoped>
.common-check-box{
  display: flex;
  flex-flow: wrap;
  .common-check {
    padding: 0 18px;
    border: 1px solid #BCBEC4;
    height: 36px;
    line-height: 36px;
    box-sizing: border-box;
    cursor: pointer;
    margin-left: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .common-check-selected {
    border: 2px solid #409eff;
    background: url("@/assets/image/checked.png") right top no-repeat;
    background-size: 20px 20px;
  }
  .common-check:first-child{
    margin-left: 0;
  }
  .pay-type-icon{
    margin-right:10px;
  }
}
</style>
