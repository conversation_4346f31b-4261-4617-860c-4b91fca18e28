<template>
  <el-card>
    <div class="project">
      <div class="project-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="产品名称"
              clearable
              v-model="state.queryForm.materialName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onClickReset()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="project-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="materialName"
            label="产品名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="materialSpec"
            label="产品规格（规格参数）"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="materialUnit"
            label="单位"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="maxPrice"
            label="最高价(元)"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="minPrice"
            label="最低价(元)"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column label="价格趋势" header-align="center" align="center">
            <template #default="scope">
              <el-button type="primary" link @click="onClickDetail(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="project-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <Detail :show="detail.show" :id="detail.id" @on-close="onCloseDetail"></Detail>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive } from "vue";
import Detail from "./detail.vue";

interface IDetail {
  show: boolean;
  id?: number;
}

const detail = reactive<IDetail>({
  show: false,
  id: void 0,
});

const state = reactive<IHooksOptions>({
  queryForm: {
    materialName: "",
  },
  isPage: true,
  dataListUrl: "/purchase/materialPriceCurve/page",
});

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

const onClickDetail = (row: any) => {
  detail.id = row.id;
  detail.show = true;
};

const onCloseDetail = () => {
  detail.show = false;
  detail.id = void 0;
};

const onClickReset = () => {
  let newQueryForm = {
    queryForm: {
      materialName: "",
    },
  };
  state.queryForm = newQueryForm;
  getDataList();
};
</script>
<style lang="scss" scoped>
.project {
  &-list {
    margin-top: 16px;
  }
}
</style>
