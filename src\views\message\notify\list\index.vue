<template>
  <el-card>
    <el-form :inline="true" :model="state.queryForm">
      <el-form-item>
        <el-button @click="onClickReadAll" type="primary">全部已读</el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-loading="state.dataListLoading"
      :data="state.dataList"
      border
      style="width: 100%"
    >
      <el-table-column
        label="序号"
        type="index"
        header-align="center"
        align="center"
        width="70"
      ></el-table-column>
      <el-table-column
        prop="templateContent"
        label="消息内容"
        header-align="center"
        align="center"
      >
        <template #default="scope">
          <div style="display: flex; align-items: center; justify-content: center">
            <span
              style="
                width: 6px;
                height: 6px;
                display: inline-block;
                border-radius: 50%;
                margin-right: 5px;
                background-color: red;
              "
              v-if="!scope.row.readStatus"
            ></span>
            <span>{{ scope.row.templateContent }}</span>
          </div>
        </template>
      </el-table-column>
      <fast-table-column
        prop="templateType"
        label="消息类型"
        header-align="center"
        align="center"
        width="120"
        dict-type="notify_type"
      ></fast-table-column>
      <el-table-column
        prop="createTime"
        label="发送时间"
        header-align="center"
        align="center"
        width="200"
      ></el-table-column>
    </el-table>
    <el-pagination
      :current-page="state.pageNo"
      :page-sizes="state.pageSizes"
      :page-size="state.pageSize"
      :total="state.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    >
    </el-pagination>
  </el-card>
</template>

<script setup lang="ts" name="MessageNotifyList">
import { useCrud } from "@/hooks";
import { reactive, ref } from "vue";
import { IHooksOptions } from "@/hooks/interface";
import { updateAllRead } from "@/api/message/notifyMessage";
import { ElMessage } from "element-plus";
import { useUserStore } from "@/store/modules/user";
import $bus from '@/utils/bus'


const userStore = useUserStore();

const state: IHooksOptions = reactive({
  dataListUrl: "/message/notifyMessage/page",
  queryForm: {
    userId: userStore.user.id,
  },
});

const onClickReadAll = () => {
  updateAllRead().then((res) => {
    if (res.code === 0) {
      ElMessage.success("全部已读成功");
      getDataList();
      $bus.emit('read','read')
    }
  });
};

const {
  getDataList,
  selectionChangeHandle,
  sizeChangeHandle,
  currentChangeHandle,
  deleteBatchHandle,
} = useCrud(state);
</script>
