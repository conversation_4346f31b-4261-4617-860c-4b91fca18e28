<template>
  <el-card>
    <div class="order">
      <div class="order-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <!-- <el-form-item>
            <el-input
              placeholder="供应商名称"
              clearable
              v-model="state.queryForm.companyName"
              style="width: 250px"
            ></el-input>
          </el-form-item> -->
          <el-form-item>
            <fast-select
              v-model="state.queryForm.isInvoice"
              dict-type="is_invoice"
              placeholder="开票状态"
              clearable
              style="width: 250px"
            >
            </fast-select>
          </el-form-item>
          <el-form-item>
            <fast-select
              v-model="state.queryForm.payType"
              dict-type="pay_type"
              placeholder="缴费方式"
              clearable
              style="width: 250px"
            >
            </fast-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="payTime"
              type="daterange"
              clearable
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="onChangeDate"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onClickReset()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="order-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="orderNo"
            label="订单号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="orderAmount"
            label="缴费金额（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="payType"
            label="缴费方式"
            header-align="center"
            align="center"
            dict-type="pay_type"
          ></fast-table-column>
          <el-table-column
            prop="payTime"
            label="缴费时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="isInvoice"
            label="开票状态"
            header-align="center"
            align="center"
            dict-type="is_invoice"
          ></fast-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="200"
          >
            <template #default="scope">
              <el-button type="primary" link @click="onClickDetail(scope.row.id)">
                查看
              </el-button>
              <el-button
                type="primary"
                link
                @click="onApplyInvoice(scope.row.id)"
                v-if="scope.row.isInvoice == 0"
              >
                申请开票
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="invoice-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <Detail
        :show="detail.show"
        :id="detail.id"
        from="supplier-order"
        @on-close="onCloseDetail"
      ></Detail>
      <ApplyInvoice
        :show="apply.show"
        :id="apply.id"
        @close="onCloseApplyInvoice"
        @submit="onSubmitApplyInvoice"
      ></ApplyInvoice>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { reactive, ref } from "vue";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import dayjs from "dayjs";
import Detail from "./detail.vue";
import ApplyInvoice from "./apply-invoice.vue";

const payTime = ref([]);

const state = reactive<IHooksOptions>({
  queryForm: {
    queryType: "0",
    companyName: "",
    isInvoice: "",
    payType: "",
    payTimeStart: "",
    payTimeEnd: "",
  },
  dataListUrl: "/work/record/page",
});

const { getDataList, sizeChangeHandle, currentChangeHandle, deleteHandle } = useCrud(
  state
);

const onClickReset = () => {
  state.queryForm.companyName = "";
  state.queryForm.isInvoice = "";
  state.queryForm.payType = "";
  state.queryForm.payTimeStart = "";
  state.queryForm.payTimeEnd = "";
  payTime.value = [];
  getDataList();
};

const onChangeDate = (value: any) => {
  if (value && value.length === 2) {
    state.queryForm.payTimeStart = dayjs(value[0]).format("YYYY-MM-DD");
    state.queryForm.payTimeEnd = dayjs(value[1]).format("YYYY-MM-DD");
  } else {
    state.queryForm.payTimeStart = "";
    state.queryForm.payTimeEnd = "";
  }
};

//#region 查看详情
interface IDetail {
  show: boolean;
  id: string;
}
const detail = reactive<IDetail>({
  show: false,
  id: "",
});

const onClickDetail = (id: string) => {
  detail.id = id;
  detail.show = true;
};

const onSubmitApplyInvoice = () => {
  getDataList();
};

const onCloseDetail = () => {
  detail.show = false;
  detail.id = "";
};

//#endregion

//#region 申请开票

interface IApply {
  show: boolean;
  id: string;
}

const apply = reactive<IApply>({
  show: false,
  id: "",
});

const onApplyInvoice = (id: string) => {
  console.log(id);
  apply.id = id;
  apply.show = true;
};

const onCloseApplyInvoice = () => {
  apply.show = false;
  apply.id = "";
};

//#endregion
</script>
