/*
 * @Descripttion: 
 * @Author: z<PERSON><PERSON><PERSON>
 * @Date: 2025-01-21 15:31:56
 * @LastEditors: zhang<PERSON><PERSON>
 * @LastEditTime: 2025-01-22 10:35:44
 * @Introduce: 
 */
// src/services/WebSocketService.ts
import cache from "@/utils/cache";
import { useUserStore } from "@/store/modules/user";
const userStore = useUserStore();

export class WebSocketService {
  private socket: WebSocket | null = null;
  private reconnectInterval: number = 5000; // 重连间隔，单位毫秒
  private reconnecting: boolean = false;
  private messageCallback: ((message: string) => void) | null = null;
  
  constructor(url: string, messageCallback?: (message: string) => void) {
    this.connect(url);
    if (messageCallback) {
      this.messageCallback = messageCallback;
    }
  }

  connect(url: string) {
    this.socket = new WebSocket(url);

    this.socket.onopen = () => {
      console.log('WebSocket connection opened');
      this.reconnecting = false;
    };

    this.socket.onmessage = (event) => {
      // console.log('Message received from server', event.data);
      if (this.messageCallback) {
        this.messageCallback(event.data);
      }
    };

    this.socket.onclose = () => {
      this.reconnect()
      console.log('WebSocket connection closed');
    };

    this.socket.onerror = (error) => {
      this.reconnect()
      console.error('WebSocket error', error);
    };
  }

  sendMessage(message: string) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(message);
    } else {
      console.error('WebSocket is not open');
    }
  }
  reconnect(){
    if (this.reconnecting) return;
    this.reconnecting = true;
    setTimeout(() => {
      this.connect(import.meta.env.VITE_WEBSOCKET+`/yuan-oa/ws/${userStore.user.id}?access_token=${cache.getToken()}`);
      this.reconnecting = false;
    }, this.reconnectInterval);
  }
  close() {
    if (this.socket) {
      this.socket.close();
    }
  }
}
