<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-02 16:25:21
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-16 17:22:34
-->
<template>
  <el-card>
    <el-tabs v-model="state.activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="合同内容" name="1">
        <el-descriptions :column="2" border>
          <template #title> <p class="titled">合同信息</p> </template>
          <el-descriptions-item width="200px" label="合同编号"
            >kooriookami</el-descriptions-item
          >
          <el-descriptions-item width="200px" label="采购合同名称"
            >kooriookami</el-descriptions-item
          >
        </el-descriptions>
        <el-descriptions :column="2" border>
          <template #title> <p class="titled">合同内容</p> </template>
          <el-descriptions-item width="200px" label="采购名称"
            >kooriookami</el-descriptions-item
          >
          <el-descriptions-item width="200px" label="采购编号"
            >kooriookami</el-descriptions-item
          >
          <el-descriptions-item width="200px" label="合同金额（元）"
            >kooriookami</el-descriptions-item
          >
          <el-descriptions-item width="200px" label="合同期限（天）"
            >kooriookami</el-descriptions-item
          >
          <el-descriptions-item width="200px" label="预计交付时间"
            >kooriookami</el-descriptions-item
          >
          <el-descriptions-item width="200px" label="合同签署时间"
            >kooriookami</el-descriptions-item
          >
        </el-descriptions>
        <el-descriptions :column="2" border>
          <template #title> <p class="titled">甲方信息</p> </template>
          <el-descriptions-item width="200px" label="采购人名称"
            >kooriookami</el-descriptions-item
          >
          <el-descriptions-item width="200px" label="采购人代码"
            >kooriookami</el-descriptions-item
          >
        </el-descriptions>
        <el-descriptions :column="2" border>
          <template #title> <p class="titled">乙方信息</p> </template>
          <el-descriptions-item width="200px" label="招标代理机构名称"
            >招标代理机构名称</el-descriptions-item
          >
          <el-descriptions-item width="200px" label="招标代理机构代码"
            >kooriookami</el-descriptions-item
          >
        </el-descriptions>
        <div class="action_title">
          <ContentTitle title="采购清单"></ContentTitle>
        </div>
        <el-table v-loading="state.dataListLoading" :data="state.dataList" border>
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            label="产品名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="产品型号（规格参数）"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="计量单位"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="采购数量"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column label="备注" dict-type="user_status"></fast-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="查看合同" name="2">
        <!-- <signer></signer> -->
        <prepdf></prepdf>
      </el-tab-pane>
    </el-tabs>
  </el-card>
</template>
<script setup lang="ts">
import { reactive } from "vue";
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import { usebulletinByPackageIdApi, useGetMaterialByPackIdApi } from "@/api/pnotice";
import { ElMessage } from "element-plus";
// import signer from "@/views/demo/singer/index.vue";
import prepdf from "./preview.vue";

const state = reactive({
  visible: false,
  nowId: "",
  activeName: "1",
});

const init = (id) => {
  state.visible = true;
  state.nowId = id;
  getDetail(id);
};
const getDetail = (id) => {
  usebulletinByPackageIdApi(id).then((res) => {
    if (res.code == 0) {
      state.dataForm = Object.assign({}, res.data);
      getList();
    }
  });
};

defineExpose({
  init,
});
</script>
<style lang="scss" scoped>
.action_title {
  margin: 20px 0;
}
.titled {
  margin: 20px 0 0;
}
</style>
