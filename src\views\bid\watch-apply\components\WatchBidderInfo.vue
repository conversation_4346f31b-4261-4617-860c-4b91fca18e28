<template>
  <el-dialog
    v-model="state.visible"
    title="详情"
    :width="800"
    :close-on-click-modal="false"
    draggable
    @close="onClose"
  >
    <el-form
      ref="dataFormRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="120px"
      class="info-form"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="packageNo" label="采购计划编号">
            <div class="info-form-value">
              {{ state.dataForm.packageNo }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="packageName" label="采购计划名称">
            <div class="info-form-value">
              {{ state.dataForm.packageName }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="bidderName" label="供应商名称">
            <div class="info-form-value">
              {{ state.dataForm.bidderName }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="contactName" label="联系人">
            <div class="info-form-value">
              {{ state.dataForm.contactName }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="contactPhone" label="联系电话">
            <div class="info-form-value">
              {{ state.dataForm.contactPhone }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="createTime" label="报名时间">
            <div class="info-form-value">
              {{ state.dataForm.createTime }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="attachmentList" label="附件">
            <div class="info-form-file">
              <div
                class="info-form-file-item"
                v-for="item in state.dataForm.attachmentList"
              >
                <div class="info-form-file-item-text">
                  {{ item.name }}
                </div>
                <div class="info-form-file-item-action">
                  <el-icon
                    class="action-icon"
                    style="margin-left: 10px"
                    @click="onClickDownload(item)"
                  >
                    <Download />
                  </el-icon>
                  <el-icon
                    class="action-icon"
                    style="margin-left: 10px"
                    @click="onClickPreview(item)"
                  >
                    <View />
                  </el-icon>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="auditStatus" label="审核结果">
            <span
              v-html="getDictLabelList('reg_audit_status', state.dataForm.auditStatus)"
            ></span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="content" label="审核意见">
            <div class="info-form-value">
              {{ state.dataForm.content }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, watch, ref } from "vue";
import { getRegistrationInfo } from "@/api/bid/apply";
import { FormRules } from "element-plus";
import { getDictLabelList } from "@/utils/tool";
import service from "@/utils/request";
import { Download, View } from "@element-plus/icons-vue";

const dataFormRef = ref();

interface IProps {
  show: boolean;
  id?: number;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  id: void 0,
});
watch(
  () => props.show,
  () => {
    if (props.show) {
      state.visible = true;
      if (props.id) {
        GetRegistrationInfo(props.id);
      }
    }
  }
);

interface IAttachmentItem {
  name: string;
  url: string;
  size: number;
  type: string;
}

interface IDataForm {
  packageNo: string;
  packageName: string;
  bidderName: string;
  contactName: string;
  contactPhone: string;
  createTime: string;
  attachmentList: IAttachmentItem[];
  auditStatus: string;
  content: string;
}

interface IState {
  visible: boolean;
  dataForm: IDataForm;
  dataRules: FormRules;
}

const state = reactive<IState>({
  visible: false,
  dataForm: {
    packageNo: "",
    packageName: "",
    bidderName: "",
    contactName: "",
    contactPhone: "",
    createTime: "",
    attachmentList: [],
    auditStatus: "1",
    content: "",
  },
  dataRules: {
    auditStatus: [{ required: true, message: "请选择审核结果", trigger: "change" }],
  },
});

const emit = defineEmits<{
  (e: "on-close"): void;
}>();

const GetRegistrationInfo = (id: number) => {
  getRegistrationInfo(id).then((res: any) => {
    if (res.code === 0) {
      let newDataForm = {
        packageNo: res.data.packageNo,
        packageName: res.data.packageName,
        bidderName: res.data.bidderName,
        contactName: res.data.contactName,
        contactPhone: res.data.contactPhone,
        createTime: res.data.createTime,
        attachmentList: (res.data?.attachmentList ?? []).map((item: any) => {
          return {
            name: item.name,
            url: item.url,
            size: item.size,
            type: item.type,
          };
        }),
        auditStatus: res.data.auditStatus + "",
        content: res.data.content,
      };
      state.dataForm = newDataForm;
    }
  });
};

const onClose = () => {
  state.visible = false;
  emit("on-close");
};

const onClickDownload = async (item: IAttachmentItem) => {
  const response = await service.get(item.url, {
    responseType: "blob", // important
  });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", item.name);
  document.body.appendChild(link);
  link.click();
};

interface IPreview {
  show: boolean;
  url: string[];
}

const preview = reactive<IPreview>({
  show: false,
  url: [],
});

const onClickPreview = (item: any) => {
  let newUrl = [];
  newUrl.push(item.url);
  preview.url = newUrl;
  preview.show = true;
};

const onClosePreviewImage = () => {
  preview.show = false;
  preview.url = [];
};
</script>
<style lang="scss" scoped>
.info-form {
  margin-bottom: 60px;
  &-value {
    color: #949494;
  }
}
.info-form-file {
  &-item {
    display: flex;
    align-items: center;
    &-text {
      color: #409eff;
      cursor: pointer;
    }
    &-action {
      color: #545252;
      cursor: pointer;
      .action-icon {
        margin-left: 4px;
      }
    }
  }
}
</style>
