<template>
  <div class="info">
    <el-form
      ref="dataFormRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="150px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="projectId" label="项目">
            <div style="display: flex; align-items: center; width: 100%">
              <el-input
                readonly
                v-model="state.dataForm.projectName"
                placeholder="项目"
                @click="onSelectProject"
                :disabled="props.type !== 'add'"
              ></el-input>
              <el-button
                type="primary"
                style="margin-left: 10px"
                @click="onSelectProject"
                :disabled="props.type !== 'add'"
              >
                选择
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="packageNo" label="采购计划编号">
            <el-input
              v-model="state.dataForm.packageNo"
              placeholder="采购计划编号"
              disabled
              @blur="onBlurField('packageNo')"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="packageName" label="采购计划名称">
            <el-input
              v-model="state.dataForm.packageName"
              placeholder="采购计划名称"
              @blur="onBlurField('packageName')"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            prop="packageBudget"
            label="本次预算金额（元）"
            class="info_budget"
          >
            <el-input-number
              v-model="state.dataForm.packageBudget"
              placeholder="本次预算金额（元）"
              controls-position="right"
              style="width: 100%"
              @blur="onBlurField('packageBudget')"
            ></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="packageType" label="采购方式">
            <fast-radio-group
              v-model="state.dataForm.packageType"
              dict-type="package_type"
              @change="onBlurField('packageType')"
            ></fast-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="packageMode" label="采购类别">
            <fast-select
              v-model="state.dataForm.packageMode"
              dict-type="package_mode"
              placeholder="采购类别"
              style="width: 100%"
              @change="onBlurField('packageMode')"
            ></fast-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="orgId" label="申请部门">
            <el-tree-select
              v-model="state.dataForm.orgId"
              :data="state.orgList"
              value-key="id"
              check-strictly
              :render-after-expand="false"
              :props="{ label: 'name', children: 'children' }"
              style="width: 100%"
              clearable
              @node-click="onChangeOrg"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="managerName" label="项目经理">
            <el-input
              v-model="state.dataForm.managerName"
              placeholder="项目经理"
              readonly
              @click="onSelectManager"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="deliveryAddress" label="送货地址">
            <el-input
              v-model="state.dataForm.deliveryAddress"
              placeholder="送货地址"
              @blur="onBlurField('deliveryAddress')"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="comment" label="采购说明">
            <WangEditor
              v-model="state.dataForm.comment"
              placeholder="请输入采购说明"
            ></WangEditor>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { FormRules } from "element-plus";
import { ref, reactive, onMounted, nextTick, watch } from "vue";
import { IBaseInfo } from "../action.vue";
import { useOrgTree } from "@/api/sys/orgs";
import WangEditor from "@/components/wang-editor/index.vue";

const dataFormRef = ref();

interface IProps {
  type: string;
  baseInfo: IBaseInfo;
}
const props = withDefaults(defineProps<IProps>(), {
  type: "add",
  baseInfo: () => {
    return {
      projectId: void 0,
      projectName: "",
      packageNo: "",
      packageName: "",
      packageBudget: void 0,
      packageType: "1",
      packageMode: "",
      orgId: void 0,
      orgName: "",
      deliveryAddress: "",
      managerId: void 0,
      managerName: "",
      comment: "",
    };
  },
});

interface IOrgItem {
  id: number;
  name: string;
  disabled: boolean;
  children?: IOrgItem[];
}

interface IState {
  dataForm: IBaseInfo;
  dataRules: FormRules;
  orgList: IOrgItem[];
}

const state = reactive<IState>({
  dataForm: {
    projectId: void 0,
    projectName: "",
    packageNo: "",
    packageName: "",
    packageBudget: void 0,
    packageType: "1",
    packageMode: "",
    orgId: void 0,
    orgName: "",
    managerId: void 0,
    managerName: "",
    deliveryAddress: "",
    comment: "",
  },
  dataRules: {
    projectId: [{ required: true, message: "请选择项目", trigger: "change" }],
    packageNo: [{ required: true, message: "采购计划编号不能为空", trigger: "blur" }],
    packageName: [{ required: true, message: "采购计划名称不能为空", trigger: "blur" }],
    packageBudget: [
      { required: true, message: "本次预算金额（元）不能为空", trigger: "blur" },
    ],
    packageType: [{ required: true, message: "请选择采购方式", trigger: "change" }],
    deliveryAddress: [{ required: true, message: "请填写送货地址", trigger: "blur" }],
    packageMode: [{ required: true, message: "请选择采购类别", trigger: "change" }],
    managerName: [{ required: true, message: "请选择项目经理", trigger: "change" }],
  },
  orgList: [],
});

watch(
  () => props.baseInfo,
  () => {
    let newBaseInfo = JSON.parse(JSON.stringify(props.baseInfo));
    state.dataForm = newBaseInfo;
  }
);

watch(
  () => state.dataForm.comment,
  () => {
    onBlurField("comment");
  }
);

const emit = defineEmits<{
  (e: "on-change-org", orgId: number, orgName: string): void;
  (e: "on-change-value", field: string, value: string | number): void;
  (e: "emit-ref", dataFormRef: any): void;
  (e: "on-select-manager"): void;
  (e: "on-select-project"): void;
}>();

onMounted(() => {
  useOrgTree().then((res: any) => {
    if (res.code === 0) {
      state.orgList = computeTree(res.data);
    }
  });
  nextTick(() => {
    emit("emit-ref", dataFormRef.value);
  });
});

const computeTree = (orgList: IOrgItem[]) => {
  let newOrgList: IOrgItem[] = orgList.map((item: IOrgItem) => {
    return {
      id: item.id,
      name: item.name,
      disabled: (item.children ?? []).length > 0 ? true : false,
      children: computeTree(item.children ?? []),
    };
  });
  return newOrgList;
};

const onSelectManager = () => {
  emit("on-select-manager");
};

const onSelectProject = () => {
  emit("on-select-project");
};

const onBlurField = (field: string) => {
  console.log(field);
  // @ts-ignore
  emit("on-change-value", field, state.dataForm[field]);
};

const onChangeOrg = (orgItem: IOrgItem) => {
  if (!orgItem.disabled) {
    emit("on-change-org", orgItem.id, orgItem.name);
  }
};
</script>
<style lang="scss" scoped>
.info {
  width: 100%;
  max-width: 1000px;
  margin-top: 10px;
  &_budget {
    :deep(.el-input__inner) {
      text-align: left;
    }
  }
}
</style>
