<template>
  <div class="title-line">
    <div class="title">基本信息</div>
    <el-link type="primary" @click="lookSpList">查看审批数据</el-link>
  </div>
  <div class="content">
    <el-form
      label-position="top"
      :model="state.formData"
      :rules="state.rules"
      ref="formRef"
    >
      <el-form-item label="请假类型" prop="holidayRuleId">
        <el-select
          v-model="state.formData.holidayRuleId"
          placeholder="选择选项"
          @change="holidayType"
        >
          <el-option
            v-for="item in state.holidayList"
            :label="item.ruleName"
            :value="item.id"
            :key="item.id"
            >{{ item.ruleName }}</el-option
          >
        </el-select>
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker
          v-model="state.formData.startTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择时间"
          @change="timeChange"
        >
        </el-date-picker>
        <el-select
          v-model="state.formData.startTimePeriod"
          class="spec"
          :disabled="!state.isHalfDay"
          @change="timeChange"
        >
          <el-option label="上午" value="0"></el-option>
          <el-option label="下午" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          v-model="state.formData.endTime"
          type="date"
          value-format="YYYY-MM-DD"
          placeholder="选择时间"
          @change="timeChange"
        >
        </el-date-picker>
        <el-select
          v-model="state.formData.endTimePeriod"
          class="spec"
          :disabled="!state.isHalfDay"
          @change="timeChange"
        >
          <el-option label="上午" value="0"></el-option>
          <el-option label="下午" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时长（天）" prop="days">
        <el-input
          v-model="state.formData.days"
          readonly
          placeholder="自动计算时长"
        ></el-input>
      </el-form-item>
      <div style="position: relative; bottom: 10px">根据排班自动计算时长</div>
      <el-form-item label="请假事由" prop="flowRemark">
        <el-input
          type="textarea"
          v-model="state.formData.flowRemark"
          :rows="5"
          placeholder="请输入请假事由"
        ></el-input>
      </el-form-item>
      <el-form-item label="附件">
        <el-upload
          class="upload"
          v-model:file-list="state.fileList"
          :headers="{ Authorization: cache.getToken() }"
          :action="constant.uploadUrl"
          :before-upload="beforeUpload"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
          accept=".jpeg,.jpg,.doc,.docx,.xls,.xlsx,.pdf,.png,.zip,.rar"
        >
          <el-button type="primary" v-if="state.fileList.length < 30">上传附件</el-button>
          <template #tip>
            <div class="el-upload__tip">最多支持上传30个文件</div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <!-- 审批流程 -->
    <approval ref="approvalRef" @emit-user="getOAuser" @emit-user-copy="getOAuserCopy" />
    <div class="btns">
      <el-button type="primary" @click="submitData" :loading="state.submitLoading"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { getOaHolidayRuleList, workLeaveDay, workLeaveFlowInfo } from "@/api/workbench";
import cache from "@/utils/cache";
import constant from "@/utils/constant";
import dayjs from "dayjs";
import { ElMessage } from "element-plus";
import { reactive, ref } from "vue";
import approval from "./approval.vue";

const approvalRef = ref();
const formRef = ref();
const state = reactive({
  oaflowuser1: [],
  oaflowuser2: [],
  fileList: [],
  holidayList: [],
  isHalfDay: true,
  formData: {
    holidayRuleId: "",
    startTime: "",
    endTime: "",
    startTimePeriod: "0",
    endTimePeriod: "0",
    days: "",
    type: "",
    leaveRequestingUnit: "",
    flowRemark: "",
    attachmentList: [],
    oaFlowPersonList: [],
  },
  rules: {
    flowRemark: [{ required: true, message: "请输入请假事由", trigger: "change" }],
    holidayRuleId: [{ required: true, message: "请选择请假类型", trigger: "change" }],
    startTime: [{ required: true, message: "请选择开始时间", trigger: "change" }],
    days: [{ required: true, message: "计算时长", trigger: "change" }],
    endTime: [{ required: true, message: "请选择结束时间", trigger: "change" }],
  },
  submitLoading: false,
});
import { useRouter } from "vue-router";
const router = useRouter();
// 查看审批数据
const lookSpList = () => {
  router.push({
    path: "/approvalCenter/imake/index",
    query: {
      flowType: "2",
    },
  });
};
const handleSuccess = (res, file) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  state.formData.attachmentList.push(res.data);
};
const beforeUpload = (file) => {
  let fileType=""
  if(file.name){
    let nameArray=file.name.split(".");
    fileType=nameArray[nameArray.length-1];
  }
  if (
    [
      "jpg",
      "png",
      "jpeg",
      "doc",
      "docx",
      "xls",
      "pdf",
      "xlsx",
      "gif",
      "zip",
      "rar",
    ].indexOf(fileType) == -1
  ) {
    ElMessage.error("格式不支持");
    return false;
  }
  return true;
};
const handleRemove = (file, files) => {
  let uploadList = [];
  for (let i of files) {
    if ("response" in i) {
      uploadList.push(i.response.data);
    } else {
      uploadList.push(i);
    }
  }
  state.formData.attachmentList = uploadList;
  // Object.assign(state.formData.attachmentList, uploadList);
};
const getHolidayList = () => {
  getOaHolidayRuleList().then((res) => {
    if (res.code == 0) {
      state.holidayList = res.data;
    }
  });
};
const timeChange = () => {
  if (
    state.formData.holidayRuleId &&
    state.formData.startTime &&
    state.formData.endTime
  ) {
    if (dayjs(state.formData.startTime).isAfter(dayjs(state.formData.endTime))) {
      ElMessage.error("开始时间不能大于结束时间");
      state.formData.endTime = "";
    } else {
      let params = {
        type: state.formData.leaveRequestingUnit,
        startLocalDate: state.formData.startTime,
        endLocalDate: state.formData.endTime,
        startPeriod: state.formData.startTimePeriod,
        endPeriod: state.formData.endTimePeriod,
        holidayRuleId: state.formData.holidayRuleId,
      };
      workLeaveDay(params).then((res) => {
        if (res.code == 0) {
          state.formData.days = res.data.days;
        } else {
          state.formData.days = "";
        }
      });
    }
  }
};
const holidayType = () => {
  let tmp = state.holidayList.find((item) => item.id == state.formData.holidayRuleId);
  state.formData.leaveRequestingUnit = tmp.leaveRequestingUnit;
  if (tmp.leaveRequestingUnit == 0) {
    // 全天
    state.formData.startTimePeriod = "0";
    state.formData.endTimePeriod = "1";
    state.isHalfDay = false;
  } else {
    state.formData.startTimePeriod = "0";
    state.formData.endTimePeriod = "0";
    state.isHalfDay = true;
  }
  // 请假类型改变，时间和天数清空
  state.formData.startTime = "";
  state.formData.endTime = "";
  state.formData.days = "";
  // if (state.formData.startTime && state.formData.endTime) {
  //   let params = {
  //     type: state.formData.leaveRequestingUnit,
  //     startLocalDate: state.formData.startTime,
  //     endLocalDate: state.formData.endTime,
  //     startPeriod: state.formData.startTimePeriod,
  //     endPeriod: state.formData.endTimePeriod,
  //     holidayRuleId: state.formData.holidayRuleId,
  //   };
  //   workLeaveDay(params).then((res) => {
  //     if (res.code == 0) {
  //       state.formData.days = res.data.days;
  //     }else{
  //       state.formData.days=""
  //     }
  //   });
  // }
};
const getOAuser = (val) => {
  let arr = val.map((item) => ({
    type: 1,
    status: 2,
    userId: item.id,
    userName: item.name,
  }));
  arr[0].status = 3;
  state.oaflowuser1 = arr;
};
const getOAuserCopy = (val) => {
  state.oaflowuser2 = val.map((item) => ({
    type: 2,
    userId: item.id,
    userName: item.name,
  }));
};
const submitData = () => {
  formRef.value.validate((valid) => {
    if (!valid) return;
    if (
      state.formData.startTime &&
      state.formData.endTime &&
      state.formData.startTimePeriod &&
      state.formData.endTimePeriod
    ) {
      if (
        state.formData.startTime === state.formData.endTime &&
        state.formData.startTimePeriod === "1" &&
        state.formData.endTimePeriod === "0"
      ) {
        ElMessage.error("结束时间段不能早于开始时间段");
        return false;
      }
    }
    if (state.oaflowuser1.length <= 0) return ElMessage.error("请选择审批人");
    state.formData.oaFlowPersonList = [...state.oaflowuser1, ...state.oaflowuser2];
    let tmp = state.holidayList.find((item) => item.id == state.formData.holidayRuleId);
    state.formData.typeName = tmp.ruleName;
    state.submitLoading = true;
    workLeaveFlowInfo(state.formData).then((res) => {
      state.submitLoading = false;
      if (res.code !== 0) {
        ElMessage.error(res.msg);
        return;
      } else {
        ElMessage.success("提交成功");
        resetData();
      }
    });
  });
};
const resetData = () => {
  state.formData.attachmentList = [];
  state.formData.flowRemark = "";
  state.formData.holidayRuleId = "";
  state.formData.days = "";
  state.formData.startTime = "";
  state.formData.endTime = "";
  state.formData.startTimePeriod = "0";
  state.formData.endTimePeriod = "0";
  state.isHalfDay = true;
  state.oaflowuser1 = [];
  state.oaflowuser2 = [];
  state.fileList = [];
  state.formData.oaFlowPersonList = [];
  approvalRef.value.clearInfo();
  formRef.value.clearValidate();
  formRef.value.resetFields();
};

getHolidayList();
</script>

<style scoped lang="scss">
.title-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .title {
    text-align: center;
    padding: 8px 0;
    width: 100px;
    border-left: 3px solid #409eff;
  }
}
.editor {
  width: 98%;
}
.btns {
  margin: 20px 0;
}

.spec {
  width: 100px;
  margin: 0 20px;
}
.upload {
  width: 98%;
}
</style>
