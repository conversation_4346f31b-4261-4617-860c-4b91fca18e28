<template>
  <div class="quick">
    <div class="quick-title">
      <HomeTitle icon="icon-kuaijierukou" title="快速入口"> </HomeTitle>
    </div>
    <div class="quick-content">
      <div
        class="quick-content-item"
        v-for="item in functionList"
        :key="item.key"
        @click="onQuickEnter(item.url)"
      >
        <div class="quick-content-item-icon">
          <svg-icon :icon="item.icon" class-name="svg-size" />
        </div>
        <div class="quick-content-item-label">{{ item.label }}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useRouter } from "vue-router";
import HomeTitle from "./HomeTitle.vue";

const router = useRouter();

const functionList = [
  {
    key: "project",
    label: "审批申请",
    icon: "icon-shenpi",
    url: "/approvalCenter/pending/index",
  },
  {
    key: "plan",
    label: "我的申请",
    icon: "icon-wodeshenqing",
    url: "/approvalCenter/imake/index",
  },
  {
    key: "notice",
    label: "通知公告",
    icon: "icon-tongzhigonggao1",
    url: "/moreUse/index",
  },
  {
    key: "invitation",
    label: "项目管理",
    icon: "icon-xiangmuguanli1",
    url: "/purchase/project/index",
  },
  {
    key: "project",
    label: "采购计划",
    icon: "icon-a-caigoujihua1",
    url: "/purchase/plan/index",
  },
  {
    key: "project",
    label: "合同库",
    icon: "icon-caigouhetong",
    url: "/contractLib/index",
  },
];

const onQuickEnter = (url: string) => {
  router.push(url);
};
</script>
<style lang="scss" scoped>
.quick {
  background-color: #ffffff;
  padding: 16px 10px;
  border-radius: 4px;
  &-content {
    display: flex;
    align-items: center;
    margin: 25px 0;
    flex-wrap: wrap;
    &-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 126px;
      height: 106px;
      cursor: pointer;
      &-icon {
        :deep(.svg-size) {
          font-size: 44px;
        }
      }
      &-label {
        margin-top: 8px;
        font-size: 14px;
        color: #333333;
      }
    }
  }
}
</style>
