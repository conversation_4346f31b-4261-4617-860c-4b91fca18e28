<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-01 10:19:02
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-06-13 15:09:09
-->
<template>
  <el-card>
    <div class="action" v-loading="state.sloading">
      <div class="action_base">
        <el-descriptions class="margin-top" title="项目信息" :column="2" border>
          <el-descriptions-item label-align="right" width="150px" label="采购计划编号">{{
            state.dataForm.packageNo
          }}</el-descriptions-item>
          <el-descriptions-item label-align="right" width="150px" label="采购计划名称">{{
            state.dataForm.packageName
          }}</el-descriptions-item>
          <el-descriptions-item
            label-align="right"
            width="150px"
            label="本次预算金额（元）"
            >{{ state.dataForm.packageBudget }}</el-descriptions-item
          >
          <el-descriptions-item label-align="right" width="150px" label="报价开始时间">{{
            state.dataForm.offerStartTime
          }}</el-descriptions-item>
          <el-descriptions-item label-align="right" width="150px" label="项目状态"
            >第
            <span class="red">{{ state.dataForm.roundNo }}</span>
            轮报价中</el-descriptions-item
          >
          <el-descriptions-item label-align="right" width="150px" label="报价截止时间"
            ><span class="red">{{
              state.dataForm.offerEndTime
            }}</span></el-descriptions-item
          >
          <el-descriptions-item label-align="right" width="150px" label="报价说明"
            ><span>{{ state.dataForm.quotationDescription }}</span></el-descriptions-item
          >
        </el-descriptions>
      </div>
      <div class="action_list">
        <div class="action_title">
          <ContentTitle title="采购清单"></ContentTitle>
        </div>
        <div class="action_content">
          <div class="list_content">
            <el-table
              v-loading="state.dataListLoading"
              :data="state.dataForm.tbPackageMaterialVOS"
              :height="400"
              border
              :summary-method="getSummaries"
              show-summary
            >
              <el-table-column
                type="index"
                label="序号"
                header-align="center"
                align="center"
                width="70"
              ></el-table-column>
              <el-table-column
                prop="materialName"
                label="产品名称"
                header-align="center"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="materialType"
                label="产品型号（规格参数）"
                header-align="center"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="materialUnit"
                label="计量单位"
                header-align="center"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="materialQuantity"
                label="采购数量"
                header-align="center"
                align="center"
              ></el-table-column>
              <el-table-column label="单价（元）" width="200" header-align="center">
                <template #default="scope">
                  <el-input-number
                    v-model="scope.row.unitPrice"
                    :precision="2"
                    :min="0"
                    @change="blurPrice(scope.$index)"
                  />
                </template>
              </el-table-column>
              <el-table-column
                label="总价（元）"
                prop="totalPrice"
                width="200"
                header-align="center"
              ></el-table-column>
              <el-table-column
                label="备注"
                prop="remark"
                width="200"
                header-align="center"
              >
                <template #default="scope">
                  <el-input type="textarea" :rows="2" v-model="scope.row.remark">
                  </el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="form_content">
            <el-form
              ref="infoRef"
              :model="state.dataForm"
              :rules="state.dataRules"
              label-width="140px"
              class="normalform"
            >
              <el-form-item prop="discountAmount" label="优惠金额">
                <el-input
                  v-model="state.dataForm.discountAmount"
                  placeholder="优惠金额"
                  type="number"
                  @blur="onBlurDiscountAmount()"
                  ><template #append>元</template></el-input
                >
              </el-form-item>
              <el-form-item prop="quotationPrice" label="报价金额">
                <el-input
                  v-model="state.dataForm.quotationPrice"
                  placeholder="报价金额"
                  type="number"
                  @blur="onBlurQuotationPrice()"
                  ><template #append>元</template></el-input
                >
              </el-form-item>
              <el-form-item prop="deliveryTimeArr" label="交货期">
                <el-date-picker
                  v-model="state.dataForm.deliveryTimeArr"
                  type="datetimerange"
                  @change="deliveryTimeChange"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                />
              </el-form-item>
              <el-form-item label="报价情况说明">
                <el-input
                  type="textarea"
                  :autosize="{ minRows: 4 }"
                  v-model="state.dataForm.quotationComment"
                  placeholder="报价情况说明"
                ></el-input>
              </el-form-item>
              <el-form-item label="附件">
                <el-upload
                  v-model:file-list="state.fileList"
                  :headers="{ Authorization: cache.getToken() }"
                  :action="constant.uploadUrl"
                  :before-upload="beforeUpload"
                  :on-success="handleSuccess"
                  :on-exceed="handleExceed"
                  accept=".doc,.docx,.xls,.xlsx,.pdf,.ppt,.pptx"
                  :limit="1"
                >
                  <el-button type="primary">上传</el-button>
                  <template #tip>
                    <div class="el-upload__tip">支持word/excel/pdf/ppt等格式</div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <div class="action_btn">
        <el-button type="primary" @click="saveSubmit">提交报价</el-button>
        <el-button @click="closentab">返回</el-button>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import BigNumber from "bignumber.js";
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import { closeTab } from "@/utils/tabs";
import {
  useGetQuotationBasicInfoApi,
  useQuotationSaveApi,
  useQuotationSavePutApi,
  useGetQuotationDetailByPackIdAndRoundNumApi,
} from "@/api/pnotice";
import { useRoute, useRouter } from "vue-router";
import { reactive, ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import cache from "@/utils/cache";
import constant from "@/utils/constant";

const route = useRoute();
const router = useRouter();
const infoRef = ref();
const state = reactive({
  dataForm: {
    attachmentList: [],
  },
  dataRules: {
    quotationPrice: [{ required: true, message: "必填项", trigger: "blur" }],
    deliveryTimeArr: [{ required: true, message: "必填项", trigger: "change" }],
  },
  saveFlag: false, // false 暂存 true 提交
  dataListLoading: false,
  sloading: false,
  uploadList: [],
  fileList: [],
});

onMounted(() => {
  if (route.query.packageId) {
    Object.assign(state.dataForm, route.query);
    getBasicInfo();
    // if(route.query.qstatus == 2){
    //   getQuotationDetailByPackIdAndRoundNum()
    // }else if(route.query.qstatus == 0){
    //   getBasicInfo()
    // }
  }
});

// 返回
const closentab = () => {
  closeTab(router, route);
};
// 回显
const getQuotationDetailByPackIdAndRoundNum = () => {
  useGetQuotationDetailByPackIdAndRoundNumApi(
    state.dataForm.packageId,
    state.dataForm.roundNum
  ).then((res) => {
    if (res.code == 0) {
      Object.assign(state.dataForm, route.query);
    }
  });
};
// 提交
const saveSubmit = () => {
  infoRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    } else {
      state.sloading = true;
      // 处理清单
      state.dataForm.tbPackageMaterialVOS.map((item) => (item.materialId = item.id));
      if (state.dataForm.qstatus == 0) {
        // 报价
        useQuotationSaveApi(state.dataForm).then((res) => {
          if (res.code == 0) {
            ElMessage.success("报价成功");
            state.sloading = false;
            closentab();
          }
        });
      } else {
        // 修改报价
        useQuotationSavePutApi(state.dataForm).then((res) => {
          if (res.code == 0) {
            ElMessage.success("报价成功");
            state.sloading = false;
            closentab();
          }
        });
      }
    }
  });
};
// 查看
const getBasicInfo = () => {
  useGetQuotationBasicInfoApi(state.dataForm.packageId, state.dataForm.roundNum).then(
    (res) => {
      if (res.code == 0) {
        Object.assign(state.dataForm, res.data);
        if (res.data.deliveryTime) {
          state.dataForm.deliveryTimeArr = [
            res.data.deliveryTime,
            res.data.deliveryEndTime,
          ];
        }
      }
    }
  );
};
const deliveryTimeChange = (value) => {
  if (value.length > 0) {
    state.dataForm.deliveryTime = value[0];
    state.dataForm.deliveryEndTime = value[1];
  } else {
    state.dataForm.deliveryTime = "";
    state.dataForm.deliveryEndTime = "";
  }
};
const handleSuccess = (res, file, files) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  state.uploadList = [];
  for (let i of state.fileList) {
    if ("response" in i) {
      state.uploadList.push(i.response.data);
    } else {
      state.uploadList.push(i);
    }
  }
  Object.assign(state.dataForm.attachmentList, state.uploadList);
};
const handleRemove = (file, files) => {
  state.uploadList = [];
  for (let i of files) {
    if ("response" in i) {
      state.uploadList.push(i.response.data);
    } else {
      state.uploadList.push(i);
    }
  }
  state.dataForm.attachmentList = state.uploadList;
  // Object.assign(state.dataForm.attachmentList, state.uploadList);
};
const beforeUpload = (file) => {
  let types = [
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/pdf",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  ];
  if (!types.includes(file.type)) {
    ElMessage.error("格式不支持");
    return false;
  }
  return true;
};
const handleExceed = () => {
  ElMessage.error("文件超出限制个数");
};
// 合计
const getSummaries = (param) => {
  const { columns, data } = param;
  const sums: string[] = [];
  columns.forEach((column, index) => {
    if (index === 6) {
      const values = data.map((item) => Number(item[column.property]));
      if (!values.every((value) => Number.isNaN(value))) {
        sums[index] = `总报价金额（元）： ${values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!Number.isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0)}`;
      } else {
        sums[index] = "";
      }
    } else {
      sums[index] = "";
    }
  });

  return sums;
};
// 总价
const blurPrice = (ind) => {
  let row = state.dataForm.tbPackageMaterialVOS[ind];
  if (row.unitPrice) {
    let bigUnitPrice = new BigNumber(row.unitPrice);
    let bugTotal = bigUnitPrice.multipliedBy(row.materialQuantity);
    row.totalPrice = bugTotal.toNumber();
  } else {
    row.totalPrice = 0;
  }
  let flag = true;
  state.dataForm.tbPackageMaterialVOS.forEach((item) => {
    if (!item.totalPrice) {
      flag = false;
    }
  });
  if (flag) {
    let totalMoney = new BigNumber(0);
    state.dataForm.tbPackageMaterialVOS.forEach((item) => {
      if (item.totalPrice) {
        totalMoney = totalMoney.plus(item.totalPrice);
      }
    });
    if (state.dataForm.discountAmount) {
      state.dataForm.quotationPrice = totalMoney
        .minus(state.dataForm.discountAmount)
        .toNumber();
    } else {
      state.dataForm.quotationPrice = totalMoney.toNumber();
    }
  }
};

const onBlurDiscountAmount = () => {
  let totalMoney = new BigNumber(0);
  state.dataForm.tbPackageMaterialVOS.forEach((item) => {
    if (item.totalPrice) {
      totalMoney = totalMoney.plus(item.totalPrice);
    }
  });
  state.dataForm.quotationPrice = totalMoney
    .minus(state.dataForm.discountAmount)
    .toNumber();
};

const onBlurQuotationPrice = () => {
  let totalMoney = new BigNumber(0);
  state.dataForm.tbPackageMaterialVOS.forEach((item) => {
    if (item.totalPrice) {
      totalMoney = totalMoney.plus(item.totalPrice);
    }
  });
  state.dataForm.discountAmount = totalMoney
    .minus(state.dataForm.quotationPrice)
    .toNumber();
};
</script>
<style lang="scss" scoped>
.action {
  .list_content {
    margin-bottom: 20px;
  }
  .action_title {
    margin: 20px 0;
  }
  .action_btn {
    margin-left: 150px;
  }
  .red {
    color: #d50000;
  }
  .normalform {
    width: 500px;
  }
}
</style>
