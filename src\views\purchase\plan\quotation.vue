<template>
  <el-card>
    <div class="quotation">
      <div class="quotation-title">项目信息</div>
      <div class="quotation-info">
        <QuotationInfo :quotationInfo="state.quotationInfo"></QuotationInfo>
      </div>
      <div class="quotation-content">
        <el-tabs v-model="state.activeName" type="card">
          <el-tab-pane label="报价清单" name="quotation">
            <QuotationList
              from="quotation"
              :packageId="state.quotationInfo.packageId"
              :roundNo="state.quotationInfo.roundNo"
              :roundStatus="state.quotationInfo.roundStatus"
              :maxOffer="state.maxOffer"
              :minOffer="state.minOffer"
              :quotationList="state.quotationList"
              @start-quotation="onStartQuotation"
            ></QuotationList>
          </el-tab-pane>
          <el-tab-pane label="比价清单" name="compare">
            <CompareList
              :activeName="state.activeName"
              :packageId="state.quotationInfo.packageId"
              :roundNo="state.quotationInfo.roundNo"
            ></CompareList>
          </el-tab-pane>
          <el-tab-pane label="采购清单" name="buy">
            <BuyList
              :activeName="state.activeName"
              :packageId="state.quotationInfo.packageId"
            ></BuyList>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import QuotationInfo, { IQuotationInfo } from "./components/QuotationInfo.vue";
import QuotationList, { IQuotationItem } from "./components/QuotationList.vue";
import { onMounted, onUnmounted, reactive, watch } from "vue";
import { getQuotationInfo } from "@/api/purchase/plan";
import BuyList from "./components/BuyList.vue";
import CompareList from "./components/CompareList.vue";
import { useRoute } from "vue-router";

const route = useRoute();

interface IState {
  interval: any;
  id?: number;
  activeName: string;
  quotationInfo: IQuotationInfo;
  maxOffer?: number;
  minOffer?: number;
  quotationList: IQuotationItem[];
}

const state = reactive<IState>({
  interval: void 0,
  id: void 0,
  activeName: "quotation",
  quotationInfo: {
    packageId: void 0,
    packageNo: "",
    packageName: "",
    packageBudget: "",
    offerStartTime: "",
    offerEndTime: "",
    roundNo: 1,
    roundStatus: 0,
    quotationDescription: "",
  },
  maxOffer: void 0,
  minOffer: void 0,
  quotationList: [],
});

onMounted(() => {
  const id: any = route.query?.id;
  state.id = id ? id : void 0;
  GetQuotationInfo();
  PollingQuotationInfo();
});

const PollingQuotationInfo = () => {
  state.interval = setInterval(() => {
    console.log("轮询报价信息");
    GetQuotationInfo();
  }, 5000);
};

onUnmounted(() => {
  if (state.interval) {
    clearInterval(state.interval);
    state.interval = void 0;
  }
});

watch(
  () => state.activeName,
  () => {
    if (state.activeName === "quotation") {
      GetQuotationInfo();
      PollingQuotationInfo();
    } else {
      if (state.interval) {
        clearInterval(state.interval);
        state.interval = void 0;
      }
    }
  }
);

const onStartQuotation = () => {
  if (state.interval) {
    clearInterval(state.interval);
    state.interval = void 0;
  }
  GetQuotationInfo();
  PollingQuotationInfo();
};

const GetQuotationInfo = () => {
  if (state.id) {
    getQuotationInfo(state.id).then((res: any) => {
      if (res.code === 0) {
        let newQuotationInfo = {
          packageId: res.data.id,
          packageNo: res.data.packageNo,
          packageName: res.data.packageName,
          packageBudget: res.data.packageBudget,
          offerStartTime: res.data.offerStartTime,
          offerEndTime: res.data.offerEndTime,
          roundNo: res.data.roundNo,
          roundStatus: res.data.roundStatus,
          quotationDescription: res.data.quotationDescription ?? "",
        };
        state.quotationInfo = newQuotationInfo;
        state.maxOffer = res.data?.maxOffer ? res.data?.maxOffer : void 0;
        state.minOffer = res.data?.minOffer ? res.data?.minOffer : void 0;
        state.quotationList = res.data?.quotationVOS ?? [];
      }
    });
  }
};
</script>
<style lang="scss" scoped>
.quotation {
  &-title {
    font-weight: 650;
    font-style: normal;
    font-size: 16px;
  }
  &-info {
    margin-top: 16px;
  }
  &-content {
    margin-top: 16px;
  }
}
</style>
