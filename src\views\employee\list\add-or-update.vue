<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    draggable
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="120px"
      @keyup.enter="submitHandle()"
    >
      <el-row>
        <el-col :span="12">
          <el-form-item prop="realName" label="姓名">
            <el-input v-model="dataForm.realName" placeholder="姓名"></el-input>
          </el-form-item>
          <el-form-item prop="orgId" label="所属公司">
            <el-tree-select
              v-model="dataForm.companyDept"
              :data="orgList"
              value-key="id"
              check-strictly
              :render-after-expand="false"
              :props="{ label: 'name', children: 'children' }"
              style="width: 100%"
              @node-click="onSelectCompany"
            />
          </el-form-item>

          <el-form-item prop="position" label="职位">
            <el-select v-model="dataForm.position" placeholder="职位" style="width: 100%">
              <el-option
                v-for="post in postList"
                :key="post.id"
                :label="post.postName"
                :value="post.id"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item prop="entryDate" label="入职日期">
            <el-date-picker
              v-model="dataForm.entryDate"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="入职日期"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item prop="mobile" label="手机号">
            <el-input v-model="dataForm.mobile" placeholder="手机号"></el-input>
          </el-form-item>
          <el-form-item prop="status" label="账号状态">
            <el-switch
              v-model="dataForm.status"
              inline-prompt
              active-text="启用"
              inactive-text="禁用"
              active-value="1"
              inactive-value="0"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item prop="jobNum" label="工号">
            <el-input v-model="dataForm.jobNum" placeholder="工号"></el-input>
          </el-form-item>
          <el-form-item prop="orgId" label="部门">
            <el-tree-select
              v-model="dataForm.orgId"
              :data="orgList"
              value-key="id"
              check-strictly
              :render-after-expand="false"
              :props="{ label: 'name', children: 'children' }"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item prop="userPost" label="职务">
            <el-select v-model="dataForm.userPost" placeholder="职务" style="width: 100%">
              <el-option
                v-for="role in roleList"
                :key="role.id"
                :label="role.name"
                :value="role.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="userStatus" label="员工状态">
            <fast-select
              v-model="dataForm.userStatus"
              dict-type="user_info_status"
              placeholder="员工状态"
            ></fast-select>
          </el-form-item>
          <el-form-item prop="probationPeriod" label="试用期">
            <div style="display: flex; align-items: center; width: 100%">
              <el-input
                v-model="dataForm.probationPeriod"
                placeholder="试用期"
              ></el-input>
              <div style="flex-shrink: 0; margin-left: 10px">个月</div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { ElMessage } from "element-plus/es";
import { useOrgListApi } from "@/api/sys/orgs";
import { useUserApi, useUserSubmitApi } from "@/api/employee/index";
import { usePostListApi } from "@/api/sys/post";
import { useRoleListApi } from "@/api/sys/role";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const postList = ref<any[]>([]);
const roleList = ref<any[]>([]);
const orgList = ref([]);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  realName: "",
  jobNum: "",
  companyDept: "",
  companyName: "",
  orgId: "",
  position: "",
  userPost: "",
  entryDate: "",
  userStatus: "",
  mobile: "",
  probationPeriod: 3,
  status: "1",
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  // id 存在则为修改
  if (id) {
    getUser(id);
  }

  getOrgList();
  getPostList();
  getRoleList();
};

// 获取岗位列表
const getPostList = () => {
  return usePostListApi().then((res) => {
    postList.value = res.data;
  });
};

// 获取角色列表
const getRoleList = () => {
  return useRoleListApi().then((res) => {
    roleList.value = res.data;
  });
};

// 获取机构列表
const getOrgList = () => {
  return useOrgListApi().then((res) => {
    orgList.value = res.data;
  });
};

// 获取信息
const getUser = (id: number) => {
  useUserApi(id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

const dataRules = ref({
  realName: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  jobNum: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  companyDept: [
    { required: true, message: "必填项不能为空", trigger: ["blur", "change"] },
  ],
  orgId: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  entryDate: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  userStatus: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  mobile: [{ required: true, message: "必填项不能为空", trigger: "blur" }],

  probationPeriod: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
});

// 表单提交
const submitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    useUserSubmitApi(dataForm).then(() => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        },
      });
    });
  });
};

const onSelectCompany = (value) => {
  dataForm.companyDept = value.id;
  dataForm.companyName = value.name;
};

defineExpose({
  init,
});
</script>
