<template>
  <el-dialog
    v-model="state.visible"
    title="审核"
    :width="800"
    :close-on-click-modal="false"
    draggable
    @close="onCloseAudit"
  >
    <el-form
      ref="dataFormRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="120px"
      class="info-form"
    >
      <template v-if="props.type === 'one'">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item prop="packageNo" label="采购计划编号">
              <div class="info-form-value">
                {{ state.dataForm.packageNo }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item prop="packageName" label="采购计划名称">
              <div class="info-form-value">
                {{ state.dataForm.packageName }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="bidderName" label="供应商名称">
              <div class="info-form-value">
                {{ state.dataForm.bidderName }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="contactName" label="联系人">
              <div class="info-form-value">
                {{ state.dataForm.contactName }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="contactPhone" label="联系电话">
              <div class="info-form-value">
                {{ state.dataForm.contactPhone }}
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="createTime" label="报名时间">
              <div class="info-form-value">
                {{ state.dataForm.createTime }}
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item prop="attachmentList" label="附件">
              <div class="info-form-file">
                <div
                  class="info-form-file-item"
                  v-for="item in state.dataForm.attachmentList"
                >
                  <div class="info-form-file-item-text">
                    {{ item.name }}
                  </div>
                  <div class="info-form-file-item-action">
                    <el-icon class="action-icon" @click="onClickDownload(item)">
                      <Download />
                    </el-icon>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </template>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="auditStatus" label="审核结果">
            <el-radio-group v-model="state.dataForm.auditStatus">
              <el-radio key="1" label="1">通过</el-radio>
              <el-radio key="2" label="2">不通过</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="content" label="审核意见">
            <el-input
              v-model="state.dataForm.content"
              placeholder="审核意见"
              type="textarea"
              :rows="4"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onCloseAudit">取消</el-button>
        <el-button type="primary" @click="onSubmitAudit">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, watch, ref, nextTick } from "vue";
import {
  auditRegistration,
  getRegistrationInfo,
  IAuditRegistration,
} from "@/api/bid/apply";
import { ElMessage, FormRules } from "element-plus";
import service from "@/utils/request";
import { Download } from "@element-plus/icons-vue";

const dataFormRef = ref();

interface IProps {
  show: boolean;
  type: string;
  id: number[];
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  type: "one",
  id: () => {
    return [];
  },
});
watch(
  () => props.show,
  () => {
    if (props.show) {
      state.visible = true;
      nextTick(() => {
        dataFormRef.value.resetFields();
      });
      if (props.type === "one") {
        if (props.id.length === 1) {
          GetRegistrationInfo(props.id[0]);
        }
      } else {
        state.dataForm.auditStatus = "1";
        state.dataForm.content = "";
      }
    }
  }
);

interface IAttachmentItem {
  name: string;
  url: string;
  size: number;
  type: string;
}

interface IDataForm {
  packageNo: string;
  packageName: string;
  bidderName: string;
  contactName: string;
  contactPhone: string;
  createTime: string;
  attachmentList: IAttachmentItem[];
  auditStatus: string;
  content: string;
}

interface IState {
  visible: boolean;
  dataForm: IDataForm;
  dataRules: FormRules;
}

const state = reactive<IState>({
  visible: false,
  dataForm: {
    packageNo: "",
    packageName: "",
    bidderName: "",
    contactName: "",
    contactPhone: "",
    createTime: "",
    attachmentList: [],
    auditStatus: "1",
    content: "",
  },
  dataRules: {
    auditStatus: [{ required: true, message: "请选择审核结果", trigger: "change" }],
  },
});

const emit = defineEmits<{
  (e: "on-close"): void;
  (e: "on-audit"): void;
}>();

const GetRegistrationInfo = (id: number) => {
  getRegistrationInfo(id).then((res: any) => {
    if (res.code === 0) {
      let newDataForm = {
        packageNo: res.data.packageNo,
        packageName: res.data.packageName,
        bidderName: res.data.bidderName,
        contactName: res.data.contactName,
        contactPhone: res.data.contactPhone,
        createTime: res.data.createTime,
        attachmentList: (res.data?.attachmentList ?? []).map((item: any) => {
          return {
            name: item.name,
            url: item.url,
            size: item.size,
            type: item.type,
          };
        }),
        auditStatus: "1",
        content: "",
      };
      state.dataForm = newDataForm;
    }
  });
};

const onSubmitAudit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      let reqData: IAuditRegistration = {
        ids: props.id,
        auditStatus: state.dataForm.auditStatus,
        content: state.dataForm.content,
      };
      auditRegistration(reqData).then((res: any) => {
        if (res.code === 0) {
          state.visible = false;
          emit("on-audit");
          ElMessage.success("审核成功");
        } else {
          ElMessage.error(res.msg);
        }
      });
    }
  });
};

const onCloseAudit = () => {
  state.visible = false;
  emit("on-close");
};

const onClickDownload = async (item: IAttachmentItem) => {
  const response = await service.get(item.url, {
    responseType: "blob", // important
  });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", item.name);
  document.body.appendChild(link);
  link.click();
};
</script>
<style lang="scss" scoped>
.info-form-file {
  &-item {
    display: flex;
    align-items: center;
    &-text {
      color: #409eff;
      cursor: pointer;
    }
    &-action {
      color: #545252;
      cursor: pointer;
      .action-icon {
        margin-left: 4px;
      }
    }
  }
}
</style>
