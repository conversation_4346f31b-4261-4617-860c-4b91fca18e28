<template>
  <el-drawer v-model="state.show" title="审批记录">
    <div class="record">
      <div class="record_item">
        <div class="left">
          <div class="left_circle">
            <div class="left_circle_content"></div>
          </div>
          <div class="left_divider">
            <div class="left_divider_content"></div>
          </div>
        </div>
        <div class="right">
          <div class="right_status">
            <div class="right_status_icon">
              <el-icon class="icon"><Select /></el-icon>
            </div>
            <div class="right_status_text">审核通过</div>
          </div>
          <div class="right_content">
            <div class="right_content_user">审核人：总经理-张立新</div>
            <div class="right_content_date">审核时间：2022-11-11 11:11:11</div>
            <div class="right_content_remark">审核意见：无</div>
          </div>
        </div>
      </div>
      <div class="record_item">
        <div class="left">
          <div class="left_circle">
            <div class="left_circle_content"></div>
          </div>
          <div class="left_divider">
            <div class="left_divider_content"></div>
          </div>
        </div>
        <div class="right">
          <div class="right_status">
            <div class="right_status_icon">
              <el-icon class="icon"><Select /></el-icon>
            </div>
            <div class="right_status_text">审核通过</div>
          </div>
          <div class="right_content">
            <div class="right_content_user">审核人：总经理-张立新</div>
            <div class="right_content_date">审核时间：2022-11-11 11:11:11</div>
            <div class="right_content_remark">审核意见：无</div>
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>
<script setup lang="ts">
import { Select } from "@element-plus/icons-vue";
import { reactive } from "vue";

const state = reactive({
  show: true,
});
</script>
<style lang="scss" scoped>
.record {
  &_item {
    display: flex;
    .left {
      margin-right: 16px;
      &_circle {
        margin-top: 7px;
        &_content {
          width: 4px;
          height: 4px;
          border-radius: 50%;
          border: 2px solid #1890ff;
          background-color: #ffffff;
        }
      }
      &_divider {
        height: 100%;
        width: 6px;
        display: flex;
        flex-direction: column;
        align-items: center;
        &_content {
          width: 1px;
          height: 100%;
          background-color: #e4e4e4;
        }
      }
    }
    .right {
      &_status {
        display: flex;
        align-items: center;
        &_icon {
          width: 20px;
          height: 20px;
          background-color: #67c23a;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          .icon {
            color: #ffffff;
          }
        }
        &_text {
          margin-left: 6px;
        }
      }
      &_content {
        margin-top: 6px;
        margin-bottom: 10px;
        background-color: #f5f7fa;
        border-radius: 8px;
        padding: 10px;
        width: 350px;
        &_user {
          color: #030303;
        }
        &_date {
          margin-top: 6px;
          color: #767676;
        }
        &_remark {
          margin-top: 6px;
          color: #767676;
        }
      }
    }
  }
}
</style>
