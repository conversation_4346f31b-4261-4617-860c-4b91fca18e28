<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-01 10:19:02
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-06-03 15:54:56
-->
<template>
  <el-card>
    <div class="action">
      <div class="action_base">
        <BaseInfo
          ref="baseInfoRef"
          @refresh-provide="refreshProvide"
          @pick-planres="pickPlanres"
        ></BaseInfo>
      </div>
      <div class="action_inventory">
        <div class="action_inventory_title">
          <ContentTitle title="采购清单"></ContentTitle>
        </div>
        <div class="action_inventory_content">
          <div class="list_content">
            <el-table v-loading="state.dataListLoading" :data="state.materialList" border>
              <el-table-column
                type="index"
                label="序号"
                header-align="center"
                align="center"
                width="70"
              ></el-table-column>
              <el-table-column
                prop="materialName"
                label="产品名称"
                header-align="center"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="materialType"
                label="产品型号（规格参数）"
                header-align="center"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="materialUnit"
                label="计量单位"
                header-align="center"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="materialQuantity"
                label="采购数量"
                header-align="center"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="comment"
                label="备注"
                header-align="center"
                align="center"
              ></el-table-column>
            </el-table>
          </div>
          <div class="list_file">
            <div class="list_file_label">附件：</div>
            <el-upload
              v-model:file-list="state.fileList"
              :headers="{ Authorization: cache.getToken() }"
              :action="constant.uploadUrl"
              :before-upload="beforeUpload"
              :on-success="handleSuccess"
              :on-remove="handleRemove"
              accept=".jpeg,.jpg,.doc,.docx,.xls,.xlsx,.pdf,.png,.zip,.rar"
              multiple
            >
              <el-button type="primary">上传</el-button>
              <template #tip>
                <div class="el-upload__tip">
                  支持word/excel/pdf/jpg/gif/png/zip/rar等格式
                </div>
              </template>
            </el-upload>
          </div>
        </div>
      </div>
      <div class="action_btn">
        <el-button type="primary" @click="saveSubmit">发布邀请函</el-button>
        <el-button type="primary" plain @click="saveTemp">暂存</el-button>
        <el-button @click="closentab">返回</el-button>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import BaseInfo from "./components/BaseInfo.vue";
import { closeTab } from "@/utils/tabs";
import {
  useInvitationSaveTempApi,
  useInvitationByIdApi,
  useInvitationSaveSubmitApi,
  useGetMaterialByPackIdApi,
  useGetListByPackIdApi,
} from "@/api/pnotice";
import { useRoute, useRouter } from "vue-router";
import { provide, reactive, ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { objectEach } from "xe-utils";
import cache from "@/utils/cache";
import constant from "@/utils/constant";

const route = useRoute();
const router = useRouter();
const baseInfoRef = ref();
const state = reactive({
  dataForm: {
    packageId: "",
    packageNo: "",
    packageName: "",
    title: "",
    packageMode: "2",
    supplyAddress: "",
    needAudit: 0,
    makeType: 1,
    content: "",
    signFlag: 0,
    signStartDate: "",
    signEndDate: "",
    bidStartDate: "",
    bidEndDate: "",
    bidbondFlag: 0,
    bidbond: 0,
    deliveryAddress: "",
    buyersName: "河北惠丰源科技有限公司",
    buyersAddress: "石家庄桥西区",
    buyersLinkerName: "周东平",
    buyersLinkerTel: "19932196868",
    contentAttach: null,
    bulletinAttachs: [],
    invitationBidders: [],
  },
  dataRules: {
    packageName: [{ required: true, message: "必填项", trigger: "change" }],
    packageNo: [{ required: true, message: "必填项", trigger: "blur" }],
    title: [{ required: true, message: "必填项", trigger: "blur" }],
    packageMode: [{ required: true, message: "必填项", trigger: "change" }],
    supplyAddress: [{ required: true, message: "必填项", trigger: "blur" }],
    needAudit: [{ required: true, message: "必填项", trigger: "change" }],
    makeType: [{ required: true, message: "必填项", trigger: "change" }],
    content: [{ required: true, message: "必填项", trigger: "blur" }],
    contentAttach: [{ required: true, message: "必填项", trigger: "change" }],
    signFlag: [{ required: true, message: "必填项", trigger: "change" }],
    signStartDate: [{ required: true, message: "必填项", trigger: "change" }],
    signEndDate: [{ required: true, message: "必填项", trigger: "change" }],
    bidStartDate: [{ required: true, message: "必填项", trigger: "change" }],
    bidEndDate: [{ required: true, message: "必填项", trigger: "change" }],
    bidbondFlag: [{ required: true, message: "必填项", trigger: "change" }],
    bidbond: [{ required: true, message: "必填项", trigger: "blur" }],
    buyersName: [{ required: true, message: "必填项", trigger: "change" }],
    buyersAddress: [{ required: true, message: "必填项", trigger: "change" }],
    buyersLinkerName: [{ required: true, message: "必填项", trigger: "change" }],
    buyersLinkerTel: [{ required: true, message: "必填项", trigger: "change" }],
    invitationBidders: [{ required: true, message: "必填项", trigger: "change" }],
  },
  nowId: "",
  saveFlag: false, // false 暂存 true 提交
  dataListLoading: false,
  materialList: [],
  fileList: [],
  uploadList: [],
});

provide("dataForm", state.dataForm);
provide("dataRules", state.dataRules);

onMounted(() => {
  if (route.query.id) {
    state.nowId = route.query.id;
    getDetail();
  }
});

// 返回
const closentab = () => {
  closeTab(router, route);
};
// 暂存
const saveTemp = () => {
  state.saveFlag = false;
  baseInfoRef.value.submitHandle();
};
// 提交
const saveSubmit = () => {
  state.saveFlag = true;
  baseInfoRef.value.submitHandle();
};
// 值变化
const refreshProvide = (res) => {
  if (res) {
    if (state.saveFlag) {
      useInvitationSaveSubmitApi(state.dataForm).then((res) => {
        if (res.code == 0) {
          ElMessage.success({
            message: "保存成功",
            duration: 500,
            onClose: () => {
              closentab();
            },
          });
        }
      });
    } else {
      useInvitationSaveTempApi(state.dataForm).then((res) => {
        if (res.code == 0) {
          ElMessage.success({
            message: "保存成功",
            duration: 500,
            onClose: () => {
              closentab();
            },
          });
        }
      });
    }
  }
};
// 查看
const getDetail = () => {
  useInvitationByIdApi(state.nowId).then((res) => {
    if (res.code == 0) {
      Object.assign(state.dataForm, res.data);
      if (state.dataForm.bulletinAttachs) {
        state.fileList = state.uploadList = state.dataForm.bulletinAttachs;
      }
      getSupplier(res.data.packageId);
    }
  });
};
const getSupplier = (packid) => {
  useGetListByPackIdApi(packid).then((res) => {
    if (res.code == 0) {
      state.dataForm.invitationBidders = res.data;
    }
  });
};
// 清单id
const pickPlanres = (res) => {
  getList(res.packageId);
};
// 清单
const getList = (id) => {
  useGetMaterialByPackIdApi(id).then((result) => {
    if (result.code == 0) {
      state.materialList = result.data;
    }
  });
};

const handleSuccess = (res, file, files) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  state.uploadList = [];
  for (let i of state.fileList) {
    if ("response" in i) {
      state.uploadList.push(i.response.data);
    } else {
      state.uploadList.push(i);
    }
  }
  Object.assign(state.dataForm.bulletinAttachs, state.uploadList);
  // console.log(state.uploadList)
};
const handleRemove = (file, files) => {
  state.uploadList = [];
  for (let i of files) {
    if ("response" in i) {
      state.uploadList.push(i.response.data);
    } else {
      state.uploadList.push(i);
    }
  }
  state.dataForm.bulletinAttachs = state.uploadList;
  // Object.assign(state.dataForm.bulletinAttachs, state.uploadList);
};
const beforeUpload = (file) => {
  let types = [
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/pdf",
    "image/jpeg",
    "image/png",
    "aplication/zip",
    "application/x-compressed",
  ];
  if (!types.includes(file.type)) {
    ElMessage.error("格式不支持");
    return false;
  }
  return true;
};
</script>
<style lang="scss" scoped>
.action {
  &_base {
    &_content {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
  &_purchase {
    margin-top: 16px;
    &_content {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
  &_inventory {
    margin-top: 16px;
    &_content {
      margin-top: 16px;
    }
  }
  &_btn {
    text-align: center;
    margin-top: 15px;
  }
  .list_file {
    margin: 20px 0 0 30px;
    display: flex;
  }
}
</style>
