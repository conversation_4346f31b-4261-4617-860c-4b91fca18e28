<template>
  <div class="track">
    <el-table v-loading="state.dataListLoading" :data="state.dataList" border>
      <el-table-column
        type="index"
        label="序号"
        header-align="center"
        align="center"
        width="70"
      ></el-table-column>
      <el-table-column
        prop="packageNo"
        label="采购计划编号"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="packageName"
        label="采购计划名称"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="title"
        label="变更公告名称"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        header-align="center"
        align="center"
        width="160"
      >
        <template #default="scope">
          <el-button type="primary" link @click="viewHandle(scope.row.id)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <!-- 查看 -->
  <view-vue ref="viewVueRef" v-if="state.viewVueShow"></view-vue>
</template>
<script setup lang="ts">
import { reactive, ref, nextTick, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import viewVue from "@/views/purchase/changenotice/view.vue";

interface IProps {
  id?: number;
}

const props = withDefaults(defineProps<IProps>(), {
  id: void 0,
});

const state: IHooksOptions = reactive({
  createdIsNeed: false,
  queryForm: {
    packageId: "",
  },
  dataListUrl: "/purchase/tracking/pageChangeBulletin4Track",
  limit: 999,
  viewVueShow: false,
  auditlogShow: false,
});
const router = useRouter();
const viewVueRef = ref();
const auditlogRef = ref();
const elFormRef = ref();

onMounted(() => {
  state.queryForm.packageId = props.id;
  getDataList();
});

// 查看
const viewHandle = (id) => {
  state.viewVueShow = true;
  nextTick(() => {
    viewVueRef.value.init(id);
  });
};
// 记录
const logHandle = (id) => {
  state.auditlogShow = true;
  nextTick(() => {
    auditlogRef.value.init(id);
  });
};
const resetForm = () => {
  elFormRef.value.resetFields();
  getDataList(1);
};

const { getDataList, deleteBatchHandle, sizeChangeHandle, currentChangeHandle } = useCrud(
  state
);
</script>
<style lang="scss" scoped>
.notice {
  &-list {
    margin-top: 16px;
  }
}
</style>
