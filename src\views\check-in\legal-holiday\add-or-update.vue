<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    width="600px"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="100px"
      @keyup.enter="submitHandle()"
    >
      <el-form-item label="节假日名称" prop="holidaysName">
        <el-input v-model="dataForm.holidaysName" placeholder="节假日名称"></el-input>
      </el-form-item>
      <el-form-item label="年度" prop="year">
        <el-select v-model="dataForm.year" placeholder="请选择年度" style="width: 100%">
          <el-option
            v-for="data in yearList"
            :key="data.dictValue"
            :label="data.dictLabel"
            :value="data.dictValue"
          >
            {{ data.dictLabel }}
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="日期" prop="holiday">
        <el-date-picker
          v-model="state.selectDate"
          type="daterange"
          unlink-panels
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="onChangeDate"
          style="width: 350px"
        />
      </el-form-item>
      <el-form-item label="调休上班日期" prop="makeUpClass">
        <el-date-picker
          v-model="dataForm.makeUpClass"
          type="dates"
          placeholder="调休上班日期"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import dayjs from "dayjs";
import { onMounted, reactive, ref } from "vue";
import { ElMessage } from "element-plus/es";
import {
  useLegalHolidayRulesApi,
  useLegalHolidayRulesSubmitApi,
} from "@/api/check-in/legalHoliday";
import { IDictItem } from "@/types/common";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  holidaysName: "",
  holiday: "",
  year: "",
  makeUpClass: [],
});

const state = reactive({
  selectDate: [],
});

const yearList = ref<IDictItem[]>([]);

onMounted(() => {
  let currentYear = dayjs().year();
  let newYearList: IDictItem[] = [];
  for (let i = 10; i > 0; i--) {
    newYearList.push({
      dictLabel: currentYear - i + "",
      dictValue: currentYear - i + "",
    });
  }
  for (let i = 0; i < 10; i++) {
    newYearList.push({
      dictLabel: currentYear + i + "",
      dictValue: currentYear + i + "",
    });
  }
  yearList.value = newYearList;
});

const onChangeDate = (value) => {
  if (value && value.length === 2) {
    dataForm.holiday = value.map((date) => dayjs(date).format("YYYY-MM-DD")).join("~");
  } else {
    dataForm.holiday = "";
  }
};

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  state.selectDate = [];
  if (id) {
    getLegalHolidayRules(id);
  }
};

const getLegalHolidayRules = (id: number) => {
  useLegalHolidayRulesApi(id).then((res) => {
    Object.assign(dataForm, res.data);
    dataForm.id = id;
    dataForm.holidaysName = res.data.holidaysName;
    dataForm.year = res.data.year;
    dataForm.makeUpClass = res.data.makeUpClass ? res.data.makeUpClass.split(",") : [];
    state.selectDate = res.data.holiday
      ? dataForm.holiday.split("~").map((date) => dayjs(date).format("YYYY-MM-DD"))
      : [];
  });
};

const dataRules = ref({
  holidaysName: [{ required: true, message: "节假日名称不能为空", trigger: "blur" }],
  year: [{ required: true, message: "年度不能为空", trigger: "blur" }],
  holiday: [{ required: true, message: "日期不能为空", trigger: "blur" }],
});

// 表单提交
const submitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    let newDataForm = JSON.parse(JSON.stringify(dataForm));
    newDataForm.makeUpClass = (newDataForm.makeUpClass ?? [])
      .map((date) => dayjs(date).format("YYYY-MM-DD"))
      .join(",");
    let makeUpClassList = newDataForm.makeUpClass.split(",");
    let holidayList = newDataForm.holiday.split("~");
    let flag = false;
    if (holidayList.length === 2) {
      let beginDate = holidayList[0];
      let endDate = holidayList[1];
      makeUpClassList.forEach((item) => {
        console.log(dayjs(item).diff(dayjs(beginDate)));
        console.log(dayjs(item).diff(dayjs(endDate)));
        if (!flag) {
          if (
            dayjs(item).diff(dayjs(beginDate)) >= 0 &&
            dayjs(item).diff(dayjs(endDate)) <= 0
          ) {
            flag = true;
          }
        }
      });
    }
    console.log(flag);
    if (!flag) {
      useLegalHolidayRulesSubmitApi(newDataForm).then(() => {
        ElMessage.success({
          message: "操作成功",
          duration: 500,
          onClose: () => {
            visible.value = false;
            emit("refreshDataList");
          },
        });
      });
    } else {
      ElMessage.error({
        message: "调休时间与节假日时间不能重叠",
        duration: 2000,
      });
    }
  });
};

defineExpose({
  init,
});
</script>
