<template>
  <div class="track">
    <div class="track-table">
      <el-table
        v-loading="state.dataListLoading"
        show-overflow-tooltip
        :data="state.dataList"
        border
      >
        <el-table-column
          type="index"
          label="序号"
          header-align="center"
          align="center"
          width="70"
        ></el-table-column>
        <el-table-column
          prop="bidderName"
          label="供应商"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="contactName"
          label="联系人"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="contactPhone"
          label="联系电话"
          header-align="center"
          align="center"
        ></el-table-column>
        <fast-table-column
          prop="auditStatus"
          label="参与审核状态"
          dict-type="reg_audit_status"
        ></fast-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          header-align="center"
          align="center"
          width="120"
        >
          <template #default="scope">
            <el-button type="primary" link @click="onClickDetail(scope.row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="track-page">
      <el-pagination
        :current-page="state.pageNo"
        :page-sizes="state.pageSizes"
        :page-size="state.pageSize"
        :total="state.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
      >
      </el-pagination>
    </div>
    <WatchBidderInfo
      :show="detail.show"
      :id="detail.id"
      @on-close="onCloseDetail"
    ></WatchBidderInfo>
  </div>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import WatchBidderInfo from "@/views/bid/watch-apply/components/WatchBidderInfo.vue";
import { onMounted, reactive } from "vue";

interface IProps {
  id?: number;
}

const props = withDefaults(defineProps<IProps>(), {
  id: void 0,
});

const state = reactive<IHooksOptions>({
  queryForm: {
    packageId: void 0,
  },
  createdIsNeed: false,
  dataListUrl: "/purchase/registration/page",
});

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

onMounted(() => {
  state.queryForm.packageId = props.id;
  getDataList();
});

interface IDetail {
  show: boolean;
  id?: number;
}

const detail = reactive<IDetail>({
  show: false,
  id: void 0,
});

const onClickDetail = (row: any) => {
  detail.id = row.id;
  detail.show = true;
};

const onCloseDetail = () => {
  detail.show = false;
  detail.id = void 0;
};
</script>

<style lang="scss" scoped></style>
