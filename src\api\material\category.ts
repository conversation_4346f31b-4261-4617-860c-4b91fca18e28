import service from '@/utils/request'

//获取物料分类树
export const getCategoryList = () => {
  return service.get(`/purchase/category/tree`)
}

export interface IActionCategory {
  id?: number
  name: string,
  pid?: number
  parentName?: string
  sort: number
}

//新增物料分类
export const addCategory = (reqData: IActionCategory) => {
  return service.post(`/purchase/category`, reqData)
}

//修改物料分类
export const updateCategory = (reqData: IActionCategory) => {
  return service.put(`/purchase/category`, reqData)
}

export const getMaterialPriceCurve = (id: number) => {
  return service.get(`/purchase/materialPriceCurve/${id}`)
}