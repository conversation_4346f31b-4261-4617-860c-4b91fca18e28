<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-08 18:54:42
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-12 10:46:53
-->
<template>
  <el-dialog
    v-model="state.visible"
    title="多轮报价"
    width="70%"
    :close-on-click-modal="false"
  >
    <div class="roundbox">
      <el-table
          v-loading="state.dataListLoading"
          :data="state.dataList"
          border stripe>
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="companyName"
            label="采购单位"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="轮次"
            header-align="center"
            align="center"
          ><template #default="scope">第{{scope.row.roundNum}}轮</template></el-table-column>
          <el-table-column
            prop="startTime"
            label="报价开始时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="endTime"
            label="报价截止时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="roundStatus"
            label="状态"
            header-align="center"
            align="center"
          >
          <template #default="scope">
            <el-tag type="success">{{scope.row.bidderQuptationStatus == 0?'未报价':'已报价'}}</el-tag>
          </template>
          </el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center">
            <template #default="scope">
              <!-- 已开启，未报价 -->
              <el-button
                v-if="scope.row.roundStatus==1&&scope.row.bidderQuptationStatus==0"
                type="primary" link @click="openOffer(scope.row)">
                报价
              </el-button>
              <el-button
                type="primary" link @click="historyHd(scope.row)">
                报价明细
              </el-button>
            </template>
          </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.visible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 当前报价 -->
  <curoffer ref="curofferRef" v-if="state.curofferShow"></curoffer>
</template>
<script setup lang="ts">
import { reactive,ref,nextTick } from "vue"
import { useGetRoundByPackIdApi } from '@/api/pnotice'
import curoffer from './curoffer.vue'
import { useCrud } from '@/hooks'
import {useRouter} from 'vue-router'


const router = useRouter()
const state = reactive({
  visible: false,
  dataListLoading: false,
  curofferShow: false,
  dataList: [],
  rowData:{},
})
const viewVueRef = ref()
const curofferRef = ref()

const init = (row)=>{
  state.visible = true
  Object.assign(state.rowData, row)
  getList()
}

const getList = ()=>{
  state.dataListLoading = true
  useGetRoundByPackIdApi(state.rowData.id).then((res) => {
    if(res.code == 0){
      state.dataList = res.data.map(item=>{
        item.companyName = state.rowData.companyName
        return item
      })
      state.dataListLoading = false
    }
  })
}
// 历史报价
const historyHd = (row) => {
  state.curofferShow = true
  nextTick(()=>{
    curofferRef.value.init(row)
  })
}
// 新增
const openOffer = (row) => {
  if(row.packageId){
    router.push({
      path:'/sppurchase/makeoffer/offer',
      query:{
        packageId:row.packageId,
        roundNum:row.roundNum,
        qstatus:0
      }
    })
  }
}

defineExpose({
	init
})
</script>
<style lang="scss" scoped></style>
