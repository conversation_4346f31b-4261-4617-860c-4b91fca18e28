<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    draggable
  >
    <div class="action-work">
      <el-form :model="dataForm" :rules="dataRule" ref="dataFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="companyName" label="公司名称">
              <el-input v-model="dataForm.companyName" placeholder="公司名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="position" label="职位">
              <el-input v-model="dataForm.position" placeholder="职位"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="rzTime" label="入职日期">
              <el-date-picker
                v-model="dataForm.rzTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="入职日期"
                style="width: 100%"
                :disabled-date="disabledRzTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="lzTime" label="离职日期">
              <el-date-picker
                v-model="dataForm.lzTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="离职日期"
                style="width: 100%"
                :disabled-date="disabledLzTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="workYear" label="工作时长">
              <el-input v-model="dataForm.workYear" placeholder="工作时长"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="lzSalary" label="离职工资">
              <el-input v-model="dataForm.lzSalary" placeholder="离职工资"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="userPost" label="岗位职责">
              <el-input v-model="dataForm.userPost" placeholder="岗位职责"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="lzWhy" label="离职原因">
              <el-input v-model="dataForm.lzWhy" placeholder="离职原因"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="remark" label="备注">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";
import { useWorkSubmitApi, getUserWorkInfo } from "@/api/employee";

const dataForm = reactive({
  id: "",
  companyName: "",
  position: "",
  rzTime: "",
  lzTime: "",
  workYear: "",
  lzSalary: "",
  userPost: "",
  lzWhy: "",
  remark: "",
});

const disabledRzTime = (time) => {
  if (time && dataForm.lzTime) {
    return time.getTime() > new Date(dataForm.lzTime).getTime();
  } else {
    return false;
  }
};

const disabledLzTime = (time) => {
  if (time && dataForm.rzTime) {
    return time.getTime() < new Date(dataForm.rzTime).getTime();
  } else {
    return false;
  }
};

const dataRule = reactive({
  companyName: [{ required: true, message: "公司名称不能为空", trigger: "blur" }],
});

const visible = ref(false);
const dataFormRef = ref();

const init = (id) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  // id 存在则为修改
  if (id) {
    GetUserWorkInfo(id);
  }
};

const GetUserWorkInfo = (id) => {
  getUserWorkInfo(id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

const emit = defineEmits(["refreshDataList"]);

const submitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    useWorkSubmitApi(dataForm).then(() => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        },
      });
    });
  });
};

defineExpose({
  init,
});
</script>
<style lang="scss" scoped></style>
