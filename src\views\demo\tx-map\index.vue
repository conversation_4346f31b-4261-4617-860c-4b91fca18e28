<template>

    <div class="ipt">
      城市：<el-input v-model="mydata.city" class="cityname" @keyup.enter="search"></el-input>
      <el-button type="primary" @click="search">查询</el-button>

      半径：<el-input v-model="mydata.radius" class="cityname" @keyup.enter="setRadius"></el-input>
      <el-button type="primary" @click="setRadius">确定</el-button>
    </div>
    
    <div id="mapContainer" style="width: 100%; height: 450px"></div>
    <p>{{ mydata.latlngInfo }}</p>
    <p>{{ mydata.nowAddress }}</p>

</template>

<script setup lang='ts'>
import {ref,reactive,onMounted} from 'vue'
import { jsonp } from 'vue-jsonp'

let map,markerLayer,circle
const mydata = reactive({
  radius: "500", 
  nowAddress: null,
  latlngInfo: null,
  lat: 39.916527,
  lng: 116.397128,
  city: ''
})

onMounted(()=>{
  initMap()
})

const initMap = ()=>{
  let lation = new TMap.LatLng(mydata.lat,mydata.lng)
  map = new TMap.Map(document.getElementById('mapContainer'),{
    center: lation,
    zoom: 15
  })

  markerLayer = new TMap.MultiMarker({
    map: map,
    styles:{
      'myStyle':new TMap.MarkerStyle({
        "width": 25,  // 点标记样式宽度（像素）
        "height": 35, // 点标记样式高度（像素）
        "src": './icon.png',  //图片路径
        "anchor": { x: 16, y: 32 }   //焦点在图片中的像素位置，一般大头针类似形式的图片以针尖位置做为焦点，圆形点以圆心位置为焦点
      })
    },
    geometries:[
      {
        id: 'marker-1',
        style: 'myStyle',
        position: lation,
        properties:{
          title:'我的位置'
        }
      }
    ]
  })

  circle = new TMap.MultiCircle({
    map: map,
    styles:{
      'circle':new TMap.CircleStyle({
        'color': 'rgba(41,91,255,0.16)',
        'showBorder': true,
        'borderColor': 'rgba(41,91,255,1)',
        'borderWidth': 2,
      })
    },
    geometries: [{
      id: 'circle-1',
      rank: 1,
      styleId: 'circle',
      center: lation, //圆形中心点坐标 
      radius: Number(mydata.radius),	//半径（单位：米）
    }],
  })

  var listener = function(evt){
    var lat = evt.latLng.lat.toFixed(6)
    var lng = evt.latLng.lng.toFixed(6)
    mydata.lat = lat
    mydata.lng = lng
    mydata.latlngInfo = `坐标是：${lat},${lng}`
    updatePoint(lat,lng)
    getAreaCode()
  }

  map.on('click', listener)
}

const updatePoint = (lat,lng) => {
  markerLayer.updateGeometries([
    {
      id: 'marker-1',
      position: new TMap.LatLng(lat,lng)
    }
  ])
  circle.updateGeometries([
    {
      id: 'circle-1',
      rank: 1,
      styleId: 'circle',
      radius: Number(mydata.radius),
      center: new TMap.LatLng(lat,lng),
    }
  ])
}

const setRadius = () => {
  if(mydata.radius){
    circle.updateGeometries([
      {
        id: 'circle-1',
        rank: 1,
        styleId: 'circle',
        radius: Number(mydata.radius),
        center: new TMap.LatLng(mydata.lat,mydata.lng),
      }
    ])
  }
}

const getCitySearch = () => {
  // 定位城市
  jsonp('https://apis.map.qq.com/ws/district/v1/search?',{
    keyword: mydata.city,
    key: '5XBBZ-XL6CQ-55M5K-B3AJV-WMBOE-NGFAD',
    output:'jsonp'
  }).then((data)=>{
    if(data.result.length>0){
      mydata.latlngInfo = '所在城市：'+ data.result[0][0].name
      map.setCenter(data.result[0][0].location)
      updatePoint(data.result[0][0].location.lat,data.result[0][0].location.lng)
      mydata.lat = data.result[0][0].location.lat
      mydata.lng = data.result[0][0].location.lng
    }
  })
}

const getAreaCode = () => {
  // 经纬度解析
  jsonp('https://apis.map.qq.com/ws/geocoder/v1/?',{
    location: mydata.lat + ',' + mydata.lng,
    key: '5XBBZ-XL6CQ-55M5K-B3AJV-WMBOE-NGFAD',
    output:'jsonp'
  }).then((data)=>{
    mydata.nowAddress = data.result.address
  })
}

const search = ()=>{
  mydata.city && getCitySearch()
}

</script>

<style scoped>
.cityname{
  width: 200px;
}
</style>