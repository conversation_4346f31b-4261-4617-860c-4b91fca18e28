<template>
  <el-card>
    <div class="apply">
      <div class="apply-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="采购计划名称"
              clearable
              v-model="state.queryForm.packageName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="供应商名称"
              clearable
              v-model="state.queryForm.bidderName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onResetSearch()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="apply-action">
        <el-button type="primary" @click="onClickAudit('many', void 0)">
          批量审核
        </el-button>
      </div>
      <div class="apply-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="selection"
            header-align="center"
            align="center"
            width="35"
          ></el-table-column>
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="bidderName"
            label="供应商名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="contactName"
            label="联系人"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="contactPhone"
            label="联系电话"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="createTime"
            label="报名时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-button type="primary" link @click="onClickAudit('one', scope.row.id)">
                审核
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="apply-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
    </div>
    <audit
      :id="action.id"
      :type="action.type"
      :show="action.show"
      @on-close="onCloseAudit"
      @on-audit="onAuditSuccess"
    ></audit>
  </el-card>
</template>
<script setup lang="ts">
import audit from "./audit.vue";

import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { ElMessage } from "element-plus";
import { reactive } from "vue";

const state = reactive<IHooksOptions>({
  queryForm: {
    auditStatus: "0",
    packageName: "",
    bidderName: "",
  },
  dataListUrl: "/purchase/registration/page",
  deleteUrl: "/purchase/registration",
});

const { getDataList, sizeChangeHandle, currentChangeHandle, deleteBatchHandle } = useCrud(
  state
);

interface IState {
  show: boolean;
  type: string;
  id: number[];
}

const action = reactive<IState>({
  show: false,
  type: "one",
  id: [],
});

const onResetSearch = () => {
  state.queryForm.packageName = "";
  state.queryForm.bidderName = "";
  state.pageNo = 1;
  getDataList();
};

const onClickAudit = (type: string, id?: number) => {
  let newId = [];
  if (type === "many" && (state?.selectedData ?? []).length == 0) {
    ElMessage.error("请先选择数据");
  } else {
    if (type === "one") {
      id && newId.push(id);
    } else {
      (state?.selectedData ?? []).forEach((item) => {
        newId.push(item.id);
      });
    }
    action.id = newId;
    action.type = type;
    action.show = true;
  }
};

const onCloseAudit = () => {
  action.show = false;
  action.id = [];
  action.type = "one";
};

const onAuditSuccess = () => {
  onCloseAudit();
  getDataList();
};
</script>
<style lang="scss" scoped>
.apply {
  &-list {
    margin-top: 16px;
  }
}
</style>
