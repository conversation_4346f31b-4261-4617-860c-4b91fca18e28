 <template>
  <div class="center">
    <div class="center-title">
      <HomeTitle icon="icon-daishenpi" title="采购项目统计"> </HomeTitle>
    </div>
    <div class="center-content">
      <div class="center-content-project" @click="onSkip('/purchase/project/index')">
        <div class="project-number">
          {{ state.projectAllNum }}
        </div>
        <div class="project-label">已提交采购项目</div>
        <div class="project-icon">
          <svgIcon icon="icon-xiangmu-xuanzhong" class-name="svg-size"></svgIcon>
        </div>
        <div class="project-bg">
          <svgIcon icon="icon-xiangmu" class-name="svg-size"></svgIcon>
        </div>
      </div>
      <div class="center-content-chart">
        <PurchaseProject></PurchaseProject>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { getPurchaseCenterStatistics } from "@/api/home";
import svgIcon from "@/components/svg-icon";
import { onMounted, reactive } from "vue";
import { useRouter } from "vue-router";
import HomeTitle from "./HomeTitle.vue";
import PurchaseProject from "./PurchaseProject.vue";

const router = useRouter();

interface IState {
  packAllNum: number;
  packAuditNum: number;
  projectAllNum: number;
}

const state = reactive<IState>({
  packAllNum: 0,
  packAuditNum: 0,
  projectAllNum: 0,
});

onMounted(() => {
  GetPurchaseCenterStatistics();
});

const GetPurchaseCenterStatistics = () => {
  getPurchaseCenterStatistics().then((res: any) => {
    console.log(res);
    if (res.code === 0) {
      state.packAllNum = res.data.packAllNum;
      state.packAuditNum = res.data.packAuditNum;
      state.projectAllNum = res.data.projectAllNum;
    }
  });
};

const onSkip = (url: string) => {
  router.push(url);
};
</script>
<style lang="scss" scoped>
.center {
  background-color: #ffffff;
  padding: 16px 10px;
  border-radius: 4px;
  &-content {
    display: flex;
    align-items: center;
    margin-top: 16px;
    &-project {
      width:200px;
      margin-right: 12px;
      height: 154px;
      background-image: url("@/assets/image/home/<USER>");
      background-size: 100% 100%;
      position: relative;
      .project-number {
        font-size: 20px;
        font-weight: 700;
        color: #333333;
        margin: 0px 16px;
        margin-top: 16px;
        position: relative;
        z-index: 2;
        cursor: pointer;
      }

      .project-label {
        font-size: 13px;
        color: #333333;
        margin: 0px 16px;
        margin-top: 6px;
        position: relative;
        z-index: 2;
      }

      .project-icon {
        position: absolute;
        bottom: 16px;
        left: 16px;
        :deep(.svg-size) {
          font-size: 32px;
        }
      }

      .project-bg {
        position: absolute;
        right: 16px;
        bottom: 16px;
        opacity: 0.5;
        :deep(.svg-size) {
          font-size: 86px;
        }
      }
    }
    &-chart{
      flex: 1;
    }
  }
}
</style>
