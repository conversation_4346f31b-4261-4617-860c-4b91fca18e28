<template>
  <el-dialog
    v-model="dialog.show"
    title="新增"
    :close-on-click-modal="false"
    draggable
    width="1000px"
    @close="onClose"
  >
    <div class="material">
      <div class="material-left">
        <el-tree
          ref="treeRef"
          :data="dialog.categoryList"
          :expand-on-click-node="false"
          :props="{ label: 'name', children: 'children' }"
          highlight-current
          node-key="id"
          @node-click="onSelectCategory"
        />
      </div>
      <div class="material-right">
        <div class="material-right-select">
          <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
            <el-form-item>
              <el-input
                placeholder="物料编码"
                clearable
                v-model="state.queryForm.materialNo"
                style="width: 200px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-input
                placeholder="物料名称"
                clearable
                v-model="state.queryForm.materialName"
                style="width: 200px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button @click="getDataList()" type="primary">查询</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="material-right-table">
          <el-table
            v-loading="state.dataListLoading"
            show-overflow-tooltip
            :data="state.dataList"
            border
            @selection-change="selectionChangeHandle"
            ref="tableRef"
            @select="selectHandle"
            @select-all="selectAllHandle"
          >
            <el-table-column type="selection" width="35"></el-table-column>
            <el-table-column
              prop="materialNo"
              label="物料编码"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="materialName"
              label="物料名称"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="materialType"
              label="型号"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="materialSpec"
              label="规格"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="materialUnit"
              label="计量单位"
              header-align="center"
              align="center"
            ></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onClose">取消</el-button>
        <el-button type="primary" @click="onConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, onMounted, watch, ref } from "vue";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { getCategoryList } from "@/api/material/category";

interface IProps {
  show: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
});

interface ICategoryItem {
  id: number;
  name: string;
  disabled: boolean;
  children?: ICategoryItem[];
}

interface IDialog {
  show: boolean;
  categoryList: ICategoryItem[];
}

const dialog = reactive<IDialog>({
  show: false,
  categoryList: [],
});

const tableRef = ref();

const state = reactive<IHooksOptions>({
  queryForm: {
    categoryId: -1,
    materialNo: "",
    materialName: "",
  },
  dataListUrl: "/purchase/material/page",
});

const { getDataList, selectionChangeHandle, selectHandle, selectAllHandle } = useCrud(
  state,
  tableRef
);

const onSelectCategory = (selectCategory: ICategoryItem) => {
  if (!selectCategory.disabled) {
    state.queryForm.categoryId = selectCategory.id;
    getDataList();
  }
};

const emit = defineEmits<{
  (e: "on-close"): void;
  (e: "on-confirm", data: any[]): void;
}>();

const onClose = () => {
  dialog.show = false;
  emit("on-close");
};

const onConfirm = () => {
  dialog.show = false;
  emit("on-confirm", state.selectedData ?? []);
};

onMounted(() => {
  state.selectedData = [];
  getCategoryList().then((res: any) => {
    if (res.code === 0) {
      let newCategoryList: ICategoryItem[] = res.data.map((item: ICategoryItem) => {
        return {
          id: item.id,
          name: item.name,
          disabled: true,
          children: (item?.children ?? []).map((ele: ICategoryItem) => {
            return {
              id: ele.id,
              name: ele.name,
              disabled: false,
            };
          }),
        };
      });
      dialog.categoryList = newCategoryList;
    }
  });
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      dialog.show = true;
    }
  }
);
</script>
<style lang="scss" scoped>
.material {
  display: flex;
  overflow-x: auto;
  &-left {
    width: 250px;
    height: 450px;
    border: 1px solid #d7d7d7;
    padding: 10px 0;
    flex-shrink: 0;
  }
  &-right {
    flex: 1;
    margin-left: 20px;
  }
}
</style>
