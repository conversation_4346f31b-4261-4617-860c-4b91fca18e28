export const printHtml = (content: string) => {
  return `
  <!DOCTYPE html>
  <html>
  
  <head>
    <meta charset="utf-8" />
    <title></title>
    <style>
      .print-title {
        font-size: 20px;
        display: flex;
        justify-content: center;
      }
  
      .print-people {
        font-size: 12px;
        color: rgb(153, 153, 153);
        display: flex;
        justify-content: flex-end;
        margin-top: 10px;
      }
  
      .print-id {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
      }
  
      .print-id-main {
        font-size: 12px;
        color: rgb(153, 153, 153);
      }
  
      .print-id-time {
        font-size: 12px;
        color: rgb(153, 153, 153);
      }
  
      .content {
        margin-top: 20px;
        position: relative;
      }
  
      .content-item {
        font-size: 12px;
        position: relative;
      }

      .content-item-top{
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        border-top: 1px solid #000000;
        z-index: 2000;
      }

      .content-item-left{
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        border-left: 1px solid #000000;
        z-index: 2000;
      }

      .content-item-right{
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        border-right: 1px solid #000000;
        z-index: 2000;
      }

      .content-item-bottom{
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        border-bottom: 1px solid #000000;
        z-index: 2000;
      }
  
      .content-two {
        display: flex;
      }
  
      .content-two-item {
        min-width: 0;
        flex: 1;
        flex-shrink: 0;
        display: flex;
      }

      .content-two-item-title {
        width: 140px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 6px 10px;
        position: relative;
        overflow: hidden;
      }

      .content-two-item-title-left{
        position:absolute;
        left:0;
        top:0;
        bottom:0;
        border-left: 1px solid #000000;
        z-index: 2000;
      }
  
      .content-two-item-title-image {
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        z-index: 1;
      }

      .content-two-item-title-image img{
        width: 100%;
        height: 100%;
      }
  
      .content-two-item-title-text {
        z-index: 2;
      }
  
      .content-two-item-value {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 6px 10px;
        position: relative;
      }

      .content-two-item-value-left{
        position:absolute;
        left:0;
        top:0;
        bottom:0;
        border-left: 1px solid #000000;
        z-index: 2000;
      }

      .content-two-item-value-main{
        word-break: break-all;
      }
  
      .content-title {
        padding: 6px 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
      }

      .content-titleleft{
        padding: 6px 10px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
      }
  
      .content-one {
        display: flex;
      }
  
      .content-one-title {
        width: 140px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 6px 10px;
        position: relative;
        overflow: hidden;
      }

      .content-one-title-left{
        position:absolute;
        left:0;
        top:0;
        bottom:0;
        border-left: 1px solid #000000;
        z-index: 2000;
      }

      .content-one-value-left{
        position:absolute;
        left:0;
        top:0;
        bottom:0;
        border-left: 1px solid #000000;
        z-index: 2000;
      }
  
      .content-one-title-image {
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        z-index: 1;
      }

      .content-one-title-image img{
        width: 100%;
        height: 100%;
      }
  
      .content-one-title-text {
        position: relative;
        z-index: 2;
      }
  
      .content-one-value {
        flex: 1;
        display: flex;
        align-items: center;
        padding: 6px 10px;
        position: relative;
        word-wrap: break-word;
      }

      .content-one-value-main-html img{
        max-width:100%;
      }

      .content-one-value-main-html table{
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #000;
      }

      .content-one-value-main-html th{
        border: 1px solid #000;
      }

      .content-one-value-main-html td{
        border: 1px solid #000;
      }
      
      .content-one-value-main-more-item{
        margin-bottom: 6px;
      }

      .content-one-value-main-more-item:last-child{
        margin-bottom: 0px;
      }

  
      .content-empty {
        padding: 6px 10px;
      }

      .content-emptytwo{
        display: flex;
      }

      .content-emptytwo-item{
        flex: 1;
        flex-shrink: 0;
        padding: 6px 10px;
        position: relative;
        overflow: hidden;
        word-break: break-all;
      }

      .content-emptytwo-item-left{
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        border-left: 1px solid #000000;
        z-index: 2000;
      }

      .content-table{
        display: flex;
      }

      .content-table-item{
        flex: 1;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 6px 10px;
        position: relative;
        overflow: hidden;
      }

      .content-table-item-left{
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        border-left: 1px solid #000000;
        z-index: 2000;
      }

      .content-table-item-image{
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        z-index: 1;
      }

      .content-table-item-image img{
        width: 100%;
        height: 100%;
      }

      .content-table-item-text{
        z-index: 2;
        word-wrap: break-word;
      }

      .content-more{
        display: flex;
      }

      .content-more-title{
        width: 140px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 6px 10px;
        position: relative;
        overflow: hidden;
      }

      .content-more-title-left{
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        border-left: 1px solid #000000;
        z-index: 2000;
      }

      .content-more-title-image{
        position: absolute;
        top: -5px;
        left: -5px;
        right: -5px;
        bottom: -5px;
        z-index: 1;
      }

      .content-more-title-text{
        position: relative;
        z-index: 2;
      }

      .content-more-value{
        flex: 1;
        position: relative;
      }

      .content-more-value-left{
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        border-left: 1px solid #000000;
        z-index: 2000;
      }

      .content-more-value-item{
        padding: 6px 10px;
        position: relative;
      }

      .content-more-value-item-top{
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        border-top: 1px solid #000000;
        z-index: 2000;
      }
  
      @media print {
        .next-page {
          page-break-after: always;
        }
  
        @page {
          margin-top: 10mm;
          margin-right: 5mm;
          margin-left: 5mm;
        }
      }
    </style>
  </head>
  
  <body>
    ${content}
  </body>
  
  </html>
  `;
};
