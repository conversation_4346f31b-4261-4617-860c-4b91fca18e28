<template>
  <div class="info">
    <el-form
      ref="infoRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="140px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="companyName" label="企业名称">
            <el-input
              v-model="state.dataForm.companyName"
              placeholder="企业名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="unitNature" label="企业认证状态" class="info_status">
            <div class="info_status_content">
              <div class="info_status_content_no">未认证</div>
              <!-- <div class="info_status_content_pass">认证通过</div> -->
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="legalPerson" label="统一社会信用代码">
            <el-input
              v-model="state.dataForm.legalPerson"
              placeholder="统一社会信用代码"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="industry" label="所属行业">
            <fast-select
              v-model="state.dataForm.industry"
              dict-type="user_gender"
              clearable
              placeholder="所属行业"
            ></fast-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="areaId" label="企业所在地">
            <el-tree-select
              v-model="state.dataForm.areaId"
              :data="state.areaList"
              value-key="id"
              check-strictly
              :render-after-expand="false"
              :props="{ label: 'name', children: 'children' }"
              style="width: 100%"
              clearable
              placeholder="企业所在地"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="legalPerson" label="法定代表人">
            <el-input
              v-model="state.dataForm.legalPerson"
              placeholder="法定代表人"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="unitNumber" label="注册资金(万元)" class="info_number">
            <el-input-number
              v-model="state.dataForm.unitNumber"
              placeholder="注册资金(万元)"
              controls-position="right"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="establishTime" label="成立时间">
            <el-date-picker
              v-model="state.dataForm.establishTime"
              type="date"
              placeholder="成立时间"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="issuingAuthority" label="办公地址">
            <el-input
              v-model="state.dataForm.issuingAuthority"
              placeholder="办公地址"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="introduction" label="单位简介">
            <el-input
              v-model="state.dataForm.introduction"
              placeholder="单位简介"
              type="textarea"
              :rows="4"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="businessScope" label="经营范围">
            <el-input
              v-model="state.dataForm.businessScope"
              placeholder="经营范围"
              type="textarea"
              :rows="4"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { FormRules } from "element-plus";
import { onMounted, reactive, ref, nextTick, watch } from "vue";
import { getAreaTree } from "@/api/supplier/manage";
import { IBaseInfo } from "../action.vue";

const infoRef = ref();

interface IProps {
  baseInfo: IBaseInfo;
}

const props = withDefaults(defineProps<IProps>(), {
  baseInfo: () => {
    return {
      companyName: "",
      unitNature: "",
      areaId: "",
      legalPerson: "",
      unitNumber: void 0,
      industry: "",
      creditRating: "",
      establishTime: void 0,
      unitCategory: "",
      issuingAuthority: "",
      introduction: "",
      businessScope: "",
    };
  },
});

interface IAreaItem {
  id: number;
  name: string;
  disabled: boolean;
  children?: IAreaItem[];
}

interface IState {
  dataForm: IBaseInfo;
  dataRules: FormRules;
  areaList: IAreaItem[];
}

const state = reactive<IState>({
  dataForm: {
    companyName: "",
    unitNature: "",
    areaId: "",
    legalPerson: "",
    unitNumber: void 0,
    industry: "",
    creditRating: "",
    establishTime: void 0,
    unitCategory: "",
    issuingAuthority: "",
    introduction: "",
    businessScope: "",
  },
  dataRules: {
    companyName: [{ required: true, message: "请输入单位名称", trigger: "blur" }],
    areaId: [{ required: true, message: "请选择单位地址", trigger: ["blur", "change"] }],
  },
  areaList: [],
});

watch(
  () => props.baseInfo,
  () => {
    let newBaseInfo = JSON.parse(JSON.stringify(props.baseInfo));
    state.dataForm = newBaseInfo;
  }
);

const emit = defineEmits<{
  (e: "emit-ref", baseInfoRef: any): void;
}>();

onMounted(() => {
  GetAreaTree();
  nextTick(() => {
    emit("emit-ref", infoRef.value);
  });
});

const GetAreaTree = () => {
  getAreaTree().then((res: any) => {
    if (res.code === 0) {
      let newAreaList = computeAreaList(res.data);
      state.areaList = newAreaList;
    }
  });
};

const computeAreaList = (list: IAreaItem[]) => {
  let newAreaList: IAreaItem[] = list.map((item) => {
    return {
      id: item.id,
      name: item.name,
      disabled: (item.children ?? []).length > 0 ? true : false,
      children: computeAreaList(item.children ?? []),
    };
  });
  return newAreaList;
};
</script>
<style lang="scss" scoped>
.info {
  width: 100%;
  max-width: 1000px;
  margin-top: 10px;
  &_number {
    :deep(.el-input__inner) {
      text-align: left;
    }
  }
}
</style>
