<template>
  <el-card>
    <div class="deposit">
      <div class="deposit-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input placeholder="采购计划名称" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-input placeholder="供应商名称" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onClickReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="deposit-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="bidderName"
            label="供应商名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageDeposit"
            label="保证金金额（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="depositStatus"
            label="退款状态"
            dict-type="deposit_pay_status"
          ></fast-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="onClickRefund(scope.row.id)"
                v-if="scope.row.depositStatus == 1"
                v-auth="'purchase:deposit:update'"
              >
                退款
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="deposit-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { reactive } from "vue";

import { refundDeposit } from "@/api/bid/deposit";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { ElMessage } from "element-plus";

const state = reactive<IHooksOptions>({
  queryForm: {
    packageName: "",
    bidderName: "",
  },
  dataListUrl: "/purchase/deposit/page",
});

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

const onClickRefund = (id: number) => {
  refundDeposit(id).then((res: any) => {
    if (res.code === 0) {
      ElMessage.success("退款成功");
      getDataList();
    }
  });
};

const onClickReset = () => {};
</script>
<style lang="scss" scoped>
.apply {
  &-list {
    margin-top: 16px;
  }
}
</style>
