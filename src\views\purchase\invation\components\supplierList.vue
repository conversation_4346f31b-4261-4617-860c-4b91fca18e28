<template>
  <el-dialog
    v-model="innerState.visible"
    title="选择供应商"
    width="60%"
    :close-on-click-modal="false"
  >
    <div class="notice-search">
      <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
        <el-form-item>
          <el-input
            v-model="state.queryForm.bidderName"
            placeholder="供应商名称"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()" type="primary">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="list_content">
      <el-table
        :row-key="row=>row.id"
        v-loading="state.dataListLoading"
        @selection-change="selectionRowHandle"
        :data="state.dataList"
        ref="tableRef"
        border
      >
        <el-table-column
          type="selection"
          header-align="center"
          reserve-selection
          align="center"
          width="70"
        ></el-table-column>
        <el-table-column
          type="index"
          label="序号"
          header-align="center"
          align="center"
          width="70"
        ></el-table-column>
        <el-table-column
          prop="userDetailsName"
          label="供应商名称"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="contactName"
          label="联系人"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="contactPhone"
          label="联系电话"
          header-align="center"
          align="center"
        ></el-table-column>
      </el-table>
    </div>
    <div class="notice-page">
      <el-pagination
        :current-page="state.pageNo"
        :page-sizes="state.pageSizes"
        :page-size="state.pageSize"
        :total="state.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
      >
      </el-pagination>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="innerState.visible = false">取消</el-button>
        <el-button type="primary" @click="selectBid">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { reactive,ref,nextTick } from "vue";

const tableRef = ref();

const innerState = reactive({
  visible: false,
});
const state: IHooksOptions = reactive({
  dataListUrl: "/sys/purchaserBidder/page",
  queryForm: {
    userDetailsName: "",
  },
});
const emit = defineEmits(["selectResult"]);

const init = () => {
  innerState.visible = true;
  nextTick(() => {
    tableRef.value.clearSelection()
  });
};
// 选择结果
const selectBid = () => {
  emit("selectResult", state.dataRowSelections);
  innerState.visible = false;
};

const {
  getDataList,
  selectionRowHandle,
  sizeChangeHandle,
  currentChangeHandle,
} = useCrud(state);

defineExpose({
  init,
});
</script>
<style lang="scss" scoped></style>
