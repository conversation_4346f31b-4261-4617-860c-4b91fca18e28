<template>
  <el-card>
    <div class="employee">
      <div class="employee-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-select
              v-model="state.queryForm.position"
              placeholder="岗位"
              style="width: 250px"
              clearable
            >
              <el-option
                v-for="post in postList"
                :key="post.id"
                :label="post.postName"
                :value="post.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-tree-select
              v-model="state.queryForm.orgId"
              :data="orgList"
              value-key="id"
              check-strictly
              :render-after-expand="false"
              :props="{ label: 'name', children: 'children', disabled: 'disabled' }"
              style="width: 250px"
              clearable
              placeholder="请选择部门"
            />
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="state.queryForm.realName"
              placeholder="姓名"
              clearable
              style="width: 250px"
            ></el-input>
          </el-form-item>

          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="success" @click="onClickExport()">导出</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="employee-statistics">
        <div
          :class="[
            'employee-statistics-item',
            status === '-2' && 'employee-statistics-item-selected',
          ]"
          @click="onChangeUserStatus('-2')"
        >
          <div class="employee-statistics-item-title">全部</div>
          <div class="employee-statistics-item-value">
            {{ userStatus.userAllCount }}
          </div>
        </div>
        <div
          :class="[
            'employee-statistics-item',
            status === '-1' && 'employee-statistics-item-selected',
          ]"
          @click="onChangeUserStatus('-1')"
        >
          <div class="employee-statistics-item-title">在职</div>
          <div class="employee-statistics-item-value">
            {{ userStatus.userTotal }}
          </div>
        </div>
        <div
          :class="[
            'employee-statistics-item',
            status === '1' && 'employee-statistics-item-selected',
          ]"
          @click="onChangeUserStatus('1')"
        >
          <div class="employee-statistics-item-title">正式</div>
          <div class="employee-statistics-item-value">
            {{ userStatus.regularCount }}
          </div>
        </div>
        <div
          :class="[
            'employee-statistics-item',
            status === '0' && 'employee-statistics-item-selected',
          ]"
          @click="onChangeUserStatus('0')"
        >
          <div class="employee-statistics-item-title">试用</div>
          <div class="employee-statistics-item-value">
            {{ userStatus.probationaryCount }}
          </div>
        </div>
        <div
          :class="[
            'employee-statistics-item',
            status === '2' && 'employee-statistics-item-selected',
          ]"
          @click="onChangeUserStatus('2')"
        >
          <div class="employee-statistics-item-title">离职</div>
          <div class="employee-statistics-item-value">
            {{ userStatus.leaveCount }}
          </div>
        </div>
      </div>
      <div class="employee-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
          style="width: 100%"
          @selection-change="selectionChangeHandle"
        >
          <el-table-column
            type="selection"
            header-align="center"
            align="center"
            width="50"
          ></el-table-column>
          <el-table-column
            prop="realName"
            label="姓名"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="jobNum"
            label="工号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column prop="name" label="部门" header-align="center" align="center">
            <template #default="scope">
              <span v-if="(scope.row.companyName ?? '') !== ''"
                >{{ scope.row.companyName }}/</span
              >{{ scope.row.orgName }}
            </template>
          </el-table-column>
          <el-table-column
            prop="mobile"
            label="手机号码"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="postName"
            label="职位"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="username"
            label="档案登记状态"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              <div
                v-if="(scope.row.registrationStatus || '0') == '0'"
                style="display: flex; align-items: center; justify-content: center"
              >
                <div style="margin-right: 10px">未发送</div>
                <el-button type="primary" link @click="onClickSend(scope.row.userId)"
                  >发送</el-button
                >
              </div>
              <div
                v-else-if="scope.row.registrationStatus == '1'"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: #f56c6c;
                "
              >
                登记中
              </div>
              <div
                v-else-if="scope.row.registrationStatus == '2'"
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: #67c23a;
                "
              >
                已登记
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="idCard"
            label="身份证号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="entryDate"
            label="入职日期"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-button type="primary" link @click="onClickDetail(scope.row.userId)"
                >查看详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update
        ref="addOrUpdateRef"
        @refresh-data-list="getDataList"
      ></add-or-update>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import AddOrUpdate from "./add-or-update.vue";
import { exportUserApi, getUserCountStatus, sendEntryMsg } from "@/api/employee";
import { useRouter } from "vue-router";
import { usePostListApi } from "@/api/sys/post";
import { useOrgListApi } from "@/api/sys/orgs";
import { ElMessage } from "element-plus";

const router = useRouter();

const status = ref("-2");

const state: IHooksOptions = reactive({
  dataListUrl: "/work/user/info/findPage",
  order: "createTime",
  // asc: "desc",
  queryForm: {
    realName: "",
    orgId: "",
    userStatus: "",
    status: "",
  },
});

const {
  getDataList,
  selectionChangeHandle,
  sizeChangeHandle,
  currentChangeHandle,
  deleteBatchHandle,
  downloadHandle,
} = useCrud(state);

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

const GetUserCountStatus = () => {
  getUserCountStatus(state.queryForm).then((res) => {
    if (res.code === 0) {
      userStatus.userAllCount = res.data.userAllCount;
      userStatus.userTotal = res.data.userTotal;
      userStatus.regularCount = res.data.regularCount;
      userStatus.probationaryCount = res.data.probationaryCount;
      userStatus.leaveCount = res.data.leaveCount;
    }
  });
};

const userStatus = reactive({
  userAllCount: 0,
  userTotal: 0,
  regularCount: 0,
  probationaryCount: 0,
  leaveCount: 0,
});

onMounted(() => {
  GetUserCountStatus();
  getPostList();
  getOrgList();
});

const postList = ref<any[]>([]);

const getPostList = () => {
  return usePostListApi().then((res) => {
    postList.value = res.data;
  });
};

const orgList = ref([]);
const getOrgList = () => {
  return useOrgListApi().then((res) => {
    orgList.value = computeOrgList(res.data);
  });
};

const computeOrgList = (list) => {
  return list.map((item: any) => {
    return {
      ...item,
      children: (item.children ?? []).length > 0 ? computeOrgList(item.children) : [],
      disabled: item.type !== "2",
    };
  });
};

const onClickDetail = (userId: number) => {
  router.push({ path: "/employee/info/detail", query: { userId: userId } });
};

const onChangeUserStatus = (userStatus: string) => {
  status.value = userStatus;
  switch (userStatus) {
    case "-2":
      state.queryForm.userStatus = "";
      state.queryForm.status = "";
      break;
    case "-1":
      state.queryForm.userStatus = "-1";
      state.queryForm.status = "1";
      break;
    case "0":
      state.queryForm.userStatus = "0";
      state.queryForm.status = "1";
      break;
    case "1":
      state.queryForm.userStatus = "1";
      state.queryForm.status = "1";
      break;
    case "2":
      state.queryForm.userStatus = "";
      state.queryForm.status = "0";
      break;
    default:
      break;
  }
  state.pageNo = 1;
  getDataList();
};

const onClickExport = () => {
  exportUserApi(state.queryForm).then((res) => {
    const url = window.URL.createObjectURL(new Blob([res.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "花名册.xlsx");
    document.body.appendChild(link);
    link.click();
  });
};

const onClickSend = (userId) => {
  sendEntryMsg([userId]).then((res) => {
    console.log(res);
    if (res.code === 0) {
      ElMessage.success("发送成功");
      getDataList();
    }
  });
};
</script>
<style lang="scss" scoped>
.employee {
  &-statistics {
    display: flex;
    align-items: center;
    &-item {
      margin-right: 20px;
      width: 220px;
      height: 90px;
      border: 1px solid #f1f1f1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      &-value {
        margin-top: 10px;
        font-weight: 700;
      }
      &-selected {
        border-color: #409eff;
        .employee-statistics-item-title {
          color: #409eff;
        }
        .employee-statistics-item-value {
          color: #409eff;
        }
      }
    }
  }
  &-list {
    margin-top: 20px;
  }
}
</style>
