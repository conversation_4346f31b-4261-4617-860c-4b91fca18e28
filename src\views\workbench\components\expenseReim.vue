<template>
  <div class="title-line">
    <div class="title">基本信息</div>
    <el-link type="primary" @click="lookSpList">查看审批数据</el-link>
  </div>
  <div class="content">
    <el-form
      label-position="top"
      :model="state.formData"
      :rules="state.rules"
      ref="formRef"
    >
      <el-form-item label="费用类型" prop="type">
        <fast-select
          v-model="state.formData.type"
          dict-type="cost_type"
          placeholder="选择选项"
          clearable
        ></fast-select>
      </el-form-item>
      <el-form-item label="费用说明" prop="flowRemark">
        <el-input
          type="textarea"
          v-model="state.formData.flowRemark"
          placeholder="请输入费用说明"
          :rows="5"
        ></el-input>
      </el-form-item>
      <el-form-item label="费用汇总（元）">
        <el-input
          v-model="state.formData.costSumMoney"
          readonly
          placeholder="自动计算"
        ></el-input>
      </el-form-item>
      <div class="fordiv" v-for="(fm, index) in state.formData.oaCostFlowInfoVOS">
        <div class="title-line title-pd">
          <div class="title">
            报销明细（{{ index + 1 }}）
            <el-link
              v-show="index != 0"
              class="file"
              :underline="false"
              type="danger"
              @click="delDetail(index)"
              >删除</el-link
            >
          </div>
        </div>
        <el-form-item
          label="费用金额（元）"
          :prop="'oaCostFlowInfoVOS.' + index + '.costMoney'"
          :rules="state.rules.costMoney"
        >
          <el-input-number
            v-model="fm.costMoney"
            @change="getCostAll"
            placeholder="请输入金额"
            :min="0"
            :precision="2"
          ></el-input-number>
        </el-form-item>
        <el-form-item
          label="发生时间"
          :prop="'oaCostFlowInfoVOS.' + index + '.startTime'"
          :rules="state.rules.startTime"
        >
          <el-date-picker
            v-model="fm.startTime"
            type="date"
            placeholder="选择时间"
            value-format="YYYY-MM-DD"
          >
          </el-date-picker>
        </el-form-item>
      </div>
    </el-form>
    <el-button class="wd100" type="success" @click="addDetail">新增明细</el-button>
    <div class="upload">
      <div class="label">附件</div>
      <el-upload
        class="upload"
        v-model:file-list="state.fileList"
        :headers="{ Authorization: cache.getToken() }"
        :action="constant.uploadUrl"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :on-remove="handleRemove"
        accept=".jpeg,.jpg,.doc,.docx,.xls,.xlsx,.pdf,.png,.zip,.rar"
      >
        <el-button type="primary" v-if="state.fileList.length < 30">上传附件</el-button>
        <template #tip>
          <div class="el-upload__tip">最多支持上传30个文件</div>
        </template>
      </el-upload>
    </div>
    <!-- 审批流程 -->
    <approval ref="approvalRef" @emit-user="getOAuser" @emit-user-copy="getOAuserCopy" />
    <div class="btns">
      <el-button type="primary" @click="submitData" :loading="state.submitLoading"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { workCostFlowInfo } from "@/api/workbench";
import cache from "@/utils/cache";
import constant from "@/utils/constant";
import { ElMessage } from "element-plus";
import { reactive, ref } from "vue";
import approval from "./approval.vue";

const approvalRef = ref();
const formRef = ref();
const state = reactive({
  oaflowuser1: [],
  oaflowuser2: [],
  fileList: [],
  formData: {
    type: "",
    flowRemark: "",
    costSumMoney: "",
    oaCostFlowInfoVOS: [
      {
        costMoney: "",
        startTime: "",
      },
    ],
    attachmentList: [],
  },
  rules: {
    type: [{ required: true, message: "请选择费用类型", trigger: "change" }],
    flowRemark: [{ required: true, message: "请输入费用说明", trigger: "blur" }],
    costMoney: [{ required: true, message: "请输入费用金额", trigger: "blur" }],
    startTime: [{ required: true, message: "请选择发生时间", trigger: "change" }],
  },
  submitLoading: false,
});
import { useRouter } from "vue-router";
const router = useRouter();
// 查看审批数据
const lookSpList = () => {
  router.push({
    path: "/approvalCenter/imake/index",
    query: {
      flowType: "3",
    },
  });
};
const addDetail = () => {
  state.formData.oaCostFlowInfoVOS.push({
    costMoney: "",
    startTime: "",
  });
};
const delDetail = (index) => {
  state.formData.oaCostFlowInfoVOS.splice(index, 1);
  getCostAll();
};
const handleSuccess = (res, file) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  state.formData.attachmentList.push(res.data);
};
const beforeUpload = (file) => {
  let fileType=""
  if(file.name){
    let nameArray=file.name.split(".");
    fileType=nameArray[nameArray.length-1];
  }
  if (
    [
      "jpg",
      "png",
      "jpeg",
      "doc",
      "docx",
      "xls",
      "pdf",
      "xlsx",
      "gif",
      "zip",
      "rar",
    ].indexOf(fileType) == -1
  ) {
    ElMessage.error("格式不支持");
    return false;
  }
  return true;
};
const handleRemove = (file, files) => {
  let uploadList = [];
  for (let i of files) {
    if ("response" in i) {
      uploadList.push(i.response.data);
    } else {
      uploadList.push(i);
    }
  }
  state.formData.attachmentList = uploadList;
  // Object.assign(state.formData.attachmentList, uploadList);
};
const getCostAll = () => {
  let costSumMoney = 0;
  state.formData.oaCostFlowInfoVOS.map((item) => {
    if (item.costMoney) {
      costSumMoney += parseFloat(item.costMoney);
    }
  });
  state.formData.costSumMoney = costSumMoney;
};
const getOAuser = (val) => {
  let arr = val.map((item) => ({
    type: 1,
    status: 2,
    userId: item.id,
    userName: item.name,
  }));
  arr[0].status = 3;
  state.oaflowuser1 = arr;
};
const getOAuserCopy = (val) => {
  state.oaflowuser2 = val.map((item) => ({
    type: 2,
    userId: item.id,
    userName: item.name,
  }));
};
const submitData = () => {
  formRef.value.validate((valid) => {
    if (!valid) return;
    if (state.oaflowuser1.length <= 0) return ElMessage.error("请选择审批人");
    state.formData.oaFlowPersonList = [...state.oaflowuser1, ...state.oaflowuser2];
    state.submitLoading = true;
    workCostFlowInfo(state.formData).then((res) => {
      state.submitLoading = false;
      if (res.code !== 0) {
        ElMessage.error(res.msg);
        return;
      } else {
        ElMessage.success("提交成功");
        state.formData.attachmentList = [];
        state.formData.oaCostFlowInfoVOS = [
          {
            costMoney: "",
            startTime: "",
          },
        ];
        state.oaflowuser1 = [];
        state.oaflowuser2 = [];
        state.fileList = [];
        state.formData.costSumMoney = "";
        state.formData.flowRemark = "";
        state.formData.oaFlowPersonList = [];
        approvalRef.value.clearInfo();
        formRef.value.clearValidate();
        formRef.value.resetFields();
      }
    });
  });
};
</script>

<style scoped lang="scss">
.title-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .title {
    padding: 8px;
    border-left: 3px solid #409eff;
  }
}
.title-pd {
  padding: 0 5px;
}
.btns {
  margin: 20px 0;
}
.wd100 {
  width: 100%;
  margin-bottom: 10px;
}
.upload {
  margin-bottom: 30px;
  width: 100%;
}
.label {
  box-sizing: border-box;
  color: var(--el-text-color-regular);
  font-size: var(--el-form-label-font-size);
  line-height: 22px;
  padding: 0 12px 0 0;
  margin-bottom: 8px;
}
</style>
