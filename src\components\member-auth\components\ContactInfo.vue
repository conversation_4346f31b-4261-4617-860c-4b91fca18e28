<template>
  <div class="info">
    <el-form
      ref="infoRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="140px"
      class="info_form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="contactName" label="联系人">
            <el-input
              v-model="state.dataForm.contactName"
              placeholder="联系人"
              @blur="onBlurField('contactName')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.contactName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="contactPhone" label="联系电话">
            <el-input
              v-model="state.dataForm.contactPhone"
              placeholder="联系电话"
              @blur="onBlurField('contactPhone')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.contactPhone }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="idNumber" label="身份证">
            <el-input
              v-model="state.dataForm.idNumber"
              placeholder="身份证"
              @blur="onBlurField('idNumber')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.idNumber }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="address" label="联系地址">
            <el-input
              v-model="state.dataForm.address"
              placeholder="联系地址"
              @blur="onBlurField('address')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.address }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="email" label="电子邮箱">
            <el-input
              v-model="state.dataForm.email"
              placeholder="电子邮箱"
              @blur="onBlurField('email')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.email }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="zipCode" label="邮政编码">
            <el-input
              v-model="state.dataForm.zipCode"
              placeholder="邮政编码"
              @blur="onBlurField('zipCode')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.zipCode }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { FormRules } from "element-plus";
import { reactive, watch, ref, onMounted, nextTick } from "vue";

export interface IContactInfo {
  contactName: string;
  contactPhone: string;
  idNumber: string;
  address: string;
  email: string;
  zipCode: string;
}

const infoRef = ref();

//#region props相关
interface IProps {
  action: string;
  contactInfo: IContactInfo;
}

const props = withDefaults(defineProps<IProps>(), {
  action: "edit",
  contactInfo: () => {
    return {
      contactName: "",
      contactPhone: "",
      idNumber: "",
      address: "",
      email: "",
      zipCode: "",
    };
  },
});
//#endregion

interface IState {
  dataForm: IContactInfo;
  dataRules: FormRules;
}

const state = reactive<IState>({
  dataForm: {
    contactName: "",
    contactPhone: "",
    idNumber: "",
    address: "",
    email: "",
    zipCode: "",
  },
  dataRules: {
    contactName: [{ required: true, message: "请输入联系人", trigger: "blur" }],
    contactPhone: [{ required: true, message: "请输入联系电话", trigger: "blur" }],
    idNumber: [{ required: true, message: "请输入身份证", trigger: "blur" }],
    email: [{ required: true, message: "请输入电子邮箱", trigger: "blur" }],
  },
});

watch(
  () => props.contactInfo,
  () => {
    let newContactInfo = JSON.parse(JSON.stringify(props.contactInfo));
    state.dataForm = newContactInfo;
  },
  { immediate: true }
);

const emit = defineEmits<{
  (e: "on-change-value", type: string, field: string, value: any): void;
  (e: "emit-ref", baseInfoRef: any): void;
}>();

onMounted(() => {
  nextTick(() => {
    emit("emit-ref", infoRef.value);
  });
});

const onBlurField = (field: string) => {
  // @ts-ignore
  emit("on-change-value", "contactInfo", field, state.dataForm[field]);
};
</script>
<style lang="scss" scoped>
.info {
  width: 100%;
  max-width: 1000px;
  margin-top: 10px;
}
</style>
