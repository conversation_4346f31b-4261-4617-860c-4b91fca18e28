export default {
	loading: 'Loading...',
	add: 'Add',
	delete: 'Delete',
	edit: 'Edit',
	query: 'Query',
	export: 'Export',
	handle: 'Action',
	back: 'Back',
	confirm: 'Confirm',
	cancel: 'Cancel',
	clear: 'Clear',
	close: 'Close',
	createTime: 'Create Time',
	updateTime: 'Update Time',
	required: 'Required items cannot be empty',
	app: {
		title: 'E采智链',
		description:
			'The backstage management template developed based on Vue3, TypeScript, Element Plus, Vue Router, Pinia, Axios, i18n、Vite, etc., has a very low threshold for use, adopts the MIT open source protocol, is completely free and open source, and can be used for commercial projects and other scenarios for free! ',
		logoText: 'E采智链',
		miniLogoText: '<PERSON>',
		username: 'Userna<PERSON>',
		password: 'Password',
		captcha: 'Captcha',
		mobileSignIn: 'Mobile SignIn',
		mobile: 'Mobile',
		signIn: 'Sign in',
		signOut: 'Sign Out',
		large: 'Large',
		default: 'Default',
		small: 'Small',
		close: 'Close',
		closeOthers: 'Close Others',
		closeAll: 'Close All'
	},
	settings: {
		title: 'Layout Settings',
		sidebarDark: 'Dark Sidebar',
		sidebarLight: 'Light Sidebar',
		navbarLight: 'Light Navbar',
		navbarTheme: 'Theme Navbar',
		layout: 'Layout Switch',
		vertical: 'Vertical',
		columns: 'Columns',
		transverse: 'Transverse',
		interface: 'Interface Settings',
		uniqueOpened: 'Unique Opened',
		dark: 'Enable Dark',
		logo: 'Enable Logo',
		breadcrumb: 'Enable Breadcrumb',
		tabs: 'Enable Tabs',
		tabsCache: 'Enable Tabs Cache',
		tabsStyle: 'Tabs Style',
		tips: 'After setting, it will only take effect temporarily. To take effect permanently, you need to click the "Copy Config" button below to replace the configuration in store/theme/config.ts. ',
		copyConfig: 'Copy Config',
		reset: 'Reset',
		copySuc: 'Copy Succeeded',
		style1: 'Style-1',
		style2: 'Style2'
	},
	error: {
		email: 'The email format is incorrect',
		password: 'The password cannot be less than {len} digits'
	},
	router: {
		home: 'Home',
		profile: 'Profile Center',
		profilePassword: 'Change Password'
	}
}
