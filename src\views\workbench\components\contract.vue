<template>
  <div class="title-line">
    <div class="title">基本信息</div>
    <el-link type="primary" @click="lookSpList">查看审批数据</el-link>
  </div>
  <div class="content">
    <el-form
      label-position="top"
      :model="state.formData"
      :rules="state.rules"
      ref="formRef"
    >
      <el-form-item label="采购计划" prop="packageName">
        <el-input
          v-model="state.formData.packageName"
          readonly
          class="custom-input"
          placeholder="请选择"
        >
          <template #append>
            <el-button type="primary" @click="pickplan">选择</el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同名称" prop="contractName">
            <el-input
              v-model="state.formData.contractName"
              @blur="contractNameChange"
              placeholder="请输入合同名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="合同金额（元）" prop="contractPrice">
            <el-input
              type="number"
              v-model="state.formData.contractPrice"
              placeholder="请输入合同金额"
            ></el-input>
          </el-form-item>
          <el-form-item label="合同开始日期" prop="startTime">
            <el-date-picker
              v-model="state.formData.startTime"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="我方主体名称" prop="mySubjectName">
            <el-input
              v-model="state.formData.mySubjectName"
              placeholder="请输入我方主体名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="对方主体名称" prop="youSubjectName">
            <el-input
              v-model="state.formData.youSubjectName"
              placeholder="请输入对方主体名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="合同编号" prop="contractNo">
            <el-input
              maxlength="30"
              v-model="state.formData.contractNo"
              placeholder="请输入合同编号"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="用章类型" prop="signType">
            <el-select v-model="state.formData.signType" multiple placeholder="请选择">
              <el-option
                v-for="item in dictList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="合同结束日期" prop="endTime">
            <el-date-picker
              v-model="state.formData.endTime"
              type="date"
              value-format="YYYY-MM-DD"
              placeholder="请选择"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="我方负责人" prop="myResponsibilityName">
            <el-input
              v-model="state.formData.myResponsibilityName"
              placeholder="请输入我方负责人"
            ></el-input>
          </el-form-item>
          <el-form-item label="对方负责人" prop="youResponsibilityName">
            <el-input
              v-model="state.formData.youResponsibilityName"
              placeholder="请输入对方负责人"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- <el-form-item label="签约日期" prop="signTime">
        <el-date-picker v-model="state.formData.signTime" type="date" value-format="YYYY-MM-DD">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="合同附件">
        <template #label> <span style="color: red">*</span>合同附件 </template>
        <el-upload
          class="upload"
          v-model:file-list="state.fileList"
          :headers="{ Authorization: cache.getToken() }"
          :action="constant.uploadUrl"
          :before-upload="beforeUpload"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
          accept=".pdf"
        >
          <el-button type="primary" v-if="state.fileList.length == 0">上传附件</el-button>
          <template #tip v-if="state.fileList.length == 0">
            <div class="el-upload__tip">仅支持pdf格式</div>
          </template>
        </el-upload>
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          type="textarea"
          v-model="state.formData.flowRemark"
          :rows="5"
          placeholder="请输入备注"
        ></el-input>
      </el-form-item>
    </el-form>
    <!-- 审批流程 -->
    <approval ref="approvalRef" @emit-user="getOAuser" @emit-user-copy="getOAuserCopy" />
    <div class="btns">
      <el-button type="primary" @click="submitData" :loading="state.submitLoading"
        >提交</el-button
      >
    </div>
  </div>

  <!-- 选择计划 -->
  <PlanSelect
    :show="state.planSelect"
    from="announcement"
    @on-select="onSelectedPlan"
    @on-close="
      () => {
        state.planSelect = false;
      }
    "
  ></PlanSelect>
</template>

<script setup lang="ts">
import PlanSelect from "@/views/picketage/components/PlanSelect.vue";
import { workContractFlowInfo, getHtCodeData,workContractCheckCode } from "@/api/workbench";
import { ElMessage } from "element-plus";
import constant from "@/utils/constant";
import { nextTick, reactive, ref, onMounted } from "vue";
import approval from "./approval.vue";
import cache from "@/utils/cache";
import { getDictDataList } from "@/utils/tool";
import { useAppStore } from "@/store/modules/app";

const approvalRef = ref();
const appStore = useAppStore();
const dictList = ref(getDictDataList(appStore.dictList, "sign_type"));
const formRef = ref();
const validateContractNo = (rule: any, value: any, callback: (e?: Error) => any) => {
  if(value){
    let params={
      contractNo:value
    }
    workContractCheckCode(params).then((res:any)=>{
      if(res.code===0){
        if(res.data===true){
          callback()
        }else{
          callback(new Error('当前合同编号已存在，请重新输入。'))
        }
      }else{
        callback(new Error(res.msg))
      }
    })
  }
}
const state = reactive({
  uploadUrl: "",
  formData: {
    packageName: "",
    packageNo: "",
    contractName: "",
    contractPrice: "",
    contractNo: "",
    mySubjectName: "",
    myResponsibilityName: "",
    youSubjectName: "",
    youResponsibilityName: "",
    signTime: "",
    flowRemark: "",
    startTime: "",
    signType: "",
    endTime: "",
    attachmentList: [],
    oaFlowPersonList: [],
    winBidderId: "",
    packageId: "",
  },
  fileList: [],
  oaflowuser1: [],
  oaflowuser2: [],
  planSelect: false,
  rules: {
    contractName: [{ required: true, message: "必填项", trigger: "blur" }],
    contractNo: [
      { required: true, message: "必填项", trigger: "blur" },
      {validator:validateContractNo,trigger: "blur"}
    ],
    mySubjectName: [{ required: true, message: "必填项", trigger: "blur" }],
    youSubjectName: [{ required: true, message: "必填项", trigger: "blur" }],
    signTime: [{ required: true, message: "必填项", trigger: "blur" }],
    startTime: [{ required: true, message: "必填项", trigger: "blur" }],
    endTime: [{ required: true, message: "必填项", trigger: "blur" }],
    contractPrice: [{ required: true, message: "必填项", trigger: "blur" }],
  },
  submitLoading: false,
});
import { useRouter } from "vue-router";
const router = useRouter();
// onMounted(()=>{
//   dictList.value=getDictDataList(appStore.dictList,'sign_type')
// })
// 查看审批数据
const lookSpList = () => {
  router.push({
    path: "/approvalCenter/imake/index",
    query: {
      flowType: "6",
    },
  });
};
// 选择计划
const pickplan = () => {
  state.planSelect = true;
};

// 选取采购计划回调
const onSelectedPlan = (row: any) => {
  // if (state.formData.contractNo === "") {
  //   getHtCode();
  // }
  state.formData.packageNo = row.packageNo;
  state.formData.packageName = row.packageName;
  state.formData.youSubjectName = row.winBidder;
  state.formData.youResponsibilityName = row.contactName;
  state.formData.winBidderId = row.winBidderId;
  state.formData.packageId = row.packageId;
  state.formData.contractPrice = row.winPrice;
  state.planSelect = false;
};
// 合同名称修改
const contractNameChange = () => {
  // if (state.formData.contractNo === "") {
  //   getHtCode();
  // }
};
// 获取合同编号
const getHtCode = () => {
  getHtCodeData("合同").then((res) => {
    state.formData.contractNo = res.data;
  });
};
const handleSuccess = (res: any) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  state.formData.attachmentList.push(res.data);
};
const beforeUpload = (file: any) => {
  let fileType=""
  if(file.name){
    let nameArray=file.name.split(".");
    fileType=nameArray[nameArray.length-1];
  }
  if (["pdf"].indexOf(fileType) == -1) {
    ElMessage.error("格式不支持");
    return false;
  }
  return true;
};
const handleRemove = (file: any, files: any) => {
  console.info(file);
  let uploadList = [];
  for (let i of files) {
    if ("response" in i) {
      uploadList.push(i.response.data);
    } else {
      uploadList.push(i);
    }
  }
  state.formData.attachmentList = uploadList;
  // Object.assign(state.formData.attachmentList, uploadList);
};
const getOAuser = (val: any) => {
  let arr = val.map((item: any) => ({
    type: 1,
    status: 2,
    userId: item.id,
    userName: item.name,
  }));
  arr[0].status = 3;
  state.oaflowuser1 = arr;
};
const getOAuserCopy = (val: any) => {
  state.oaflowuser2 = val.map((item: any) => ({
    type: 2,
    userId: item.id,
    userName: item.name,
  }));
};
const submitData = () => {
  if (state.fileList.length == 0) {
    return ElMessage.error("请上传附件！");
  }
  formRef.value.validate((valid: any) => {
    if (!valid) return;
    if (state.oaflowuser1.length <= 0) return ElMessage.error("请选择审批人");
    state.formData.signType = state.formData.signType.join(",");
    state.formData.oaFlowPersonList = [...state.oaflowuser1, ...state.oaflowuser2];
    state.submitLoading = true;
    workContractFlowInfo(state.formData).then((res) => {
      state.submitLoading = false;
      if (res.code !== 0) {
        ElMessage.error(res.msg);
        return;
      } else {
        ElMessage.success("提交成功");
        state.formData.attachmentList = [];
        // state.formData.oaCostFlowInfoVOS = [{
        //   costMoney:'',
        //   startTime:'',
        // }]
        state.oaflowuser1 = [];
        state.oaflowuser2 = [];
        state.fileList = [];
        state.formData.oaFlowPersonList = [];
        approvalRef.value.clearInfo();
        formRef.value.clearValidate();
        formRef.value.resetFields();
      }
    });
  });
};
</script>

<style scoped lang="scss">
.title-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;

  .title {
    text-align: center;
    padding: 8px 0;
    width: 100px;
    border-left: 3px solid #409eff;
  }
}

.editor {
  width: 98%;
}

.btns {
  margin: 20px 0;
}

.custom-input :deep(.el-input-group__append button.el-button) {
  color: var(--el-color-white);
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary);
}

.spec {
  width: 100px;
  margin: 0 20px;
}
</style>
