<template>
	<el-card>
    <el-tabs v-model="state.activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="发票抬头信息" name="first">
        <invoice-title></invoice-title>
      </el-tab-pane>
      <el-tab-pane label="邮寄地址" name="second">
        <invoice-address></invoice-address>
      </el-tab-pane>
    </el-tabs>
	</el-card>
</template>

<script setup lang="ts">
import { reactive } from "vue"
import invoiceTitle from './invoice-title.vue'
import invoiceAddress from './invoice-address.vue'

const state = reactive({
  activeName:'first'
})

// tab点击
const handleClick = (tab, event) => {
  console.log(tab.paneName)
  // tab.paneName == 'first' ? getDataList(): nextTick(()=> joinedRef.value.getDataList())
}

</script>
<style scoped lang="scss">
h3 {
	margin: 20px 0;
	color: #0196f3;
	font-size: 16px;
}
</style>
