<template>

  <div class="detail-ctr" v-if="props.alldata.oaCostFlowInfoVO">
   <div class="multi">
    <div class="detail-ctr-label">费用类型</div>
    <div class="detail-ctr-txt">{{getDictLabel(appStore.dictList, "cost_type", props.alldata.oaCostFlowInfoVO.type)}}</div>
    <div class="detail-ctr-label">费用说明</div>
      <div class="detail-ctr-txt">{{props.alldata.oaCostFlowInfoVO.flowRemark}}</div>
    <div class="detail-ctr-label">费用汇总（元）</div>
    <div class="detail-ctr-txt">{{props.alldata.oaCostFlowInfoVO.costSumMoney}}</div>
    <div v-for="item,index in props.alldata.oaCostFlowInfoVO.oaCostFlowInfoVOS">
      <div class="detail-ctr-line">报销明细（{{index+1}}）</div>
      <div class="detail-ctr-label">费用金额（元）</div>
      <div class="detail-ctr-txt">{{item.costMoney}}</div>
      <div class="detail-ctr-label">发生时间</div>
      <div class="detail-ctr-txt">{{item.startTime}}</div>
    </div>
   </div>
   <div class="detail-ctr-label">附件</div>
   <div class="detail-ctr-txt">
      <el-link class="file" :underline="false" type="primary" v-for="item in props.alldata.oaCostFlowInfoVO.attachmentList"
      @click="downloadFile(item.url,item.name)">{{item.name}}</el-link>
    </div>
  </div>
 
 </template>
 
 <script setup lang='ts'>
 import { useAppStore } from "@/store/modules/app";
import { downloadFile, getDictLabel } from "@/utils/tool";
import { IView } from '../types';

  const appStore = useAppStore();
  
  interface IProps {
    id: number;
    alldata: IView;
  }

  const props = withDefaults(defineProps<IProps>(), {
    id: 0,
    alldata: {
      oaCostFlowInfoVO:{
        type:'',
        flowRemark:'',
        costSumMoney:'',
        oaCostFlowInfoVOS:[{
          costMoney:'',
          startTime:'',
        }],
        attachmentList:[]
      }
    }
  })
 </script>
 
 <style scoped lang='scss'>
 .detail-ctr{
  &-line{
    margin-bottom: 10px;
    color:#409eff;
  }
   &-label{
     color: #a2a2a2;
     padding: 0 12px 0 0;
     margin: 0 0 8px 0;
     line-height: 22px;
   }
   &-txt{
     margin-bottom: 15px;
     .file{display: block;line-height: 1.8;}
   }
 }
 </style>