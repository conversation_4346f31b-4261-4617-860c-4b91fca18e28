<template>
  <el-form :inline="true" :model="state.queryForm">
    <el-form-item>
      <el-button :icon="Plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
    </el-form-item>
  </el-form>
  <el-table
    v-loading="state.dataListLoading"
    :data="state.dataList"
    border
    style="width: 100%"
    stripe
  >
    <el-table-column
      type="index"
      label="序号"
      header-align="center"
      align="center"
      width="60"
    ></el-table-column>
    <el-table-column
      prop="recipient"
      label="收件人姓名"
      header-align="center"
      align="center"
    ></el-table-column>
    <el-table-column
      prop="telePhone"
      label="电话号码"
      header-align="center"
      align="center"
    ></el-table-column>
    <el-table-column prop="email" label="电子邮箱" align="center"></el-table-column>
    <el-table-column
      prop="address"
      label="邮寄地址"
      header-align="center"
      align="center"
    ></el-table-column>
    <el-table-column
      prop="defaultAddress"
      label="是否默认地址"
      header-align="center"
      align="center"
    >
      <template #default="scope">
        {{ scope.row.defaultAddress === "01" ? "是" : "否" }}
      </template>
    </el-table-column>
    <el-table-column prop="operation" label="操作" header-align="center" align="center">
      <template #default="scope">
        <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)"
          >编辑</el-button
        >
        <el-button type="primary" link @click="deleteBatchHandle(scope.row.id)"
          >删除</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    :current-page="state.pageNo"
    :page-sizes="state.pageSizes"
    :page-size="state.pageSize"
    :total="state.total"
    layout="total, prev, pager, next"
    @size-change="sizeChangeHandle"
    @current-change="currentChangeHandle"
  >
  </el-pagination>

  <!-- 新增 / 修改 -->
  <address-addorupdate
    ref="addressAddorupdateRef"
    v-if="state.addressAddorupdateShow"
    @refresh-data-list="getDataList"
  ></address-addorupdate>
</template>

<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { Plus } from "@element-plus/icons-vue";
import { nextTick, reactive, ref } from "vue";
import addressAddorupdate from "./address-addorupdate.vue";

const state: IHooksOptions = reactive({
  dataListUrl: "/sys/mailingAddress/page",
  deleteUrl: "/sys/mailingAddress",
  queryForm: {
    username: "",
    address: "",
    status: "",
  },
  addressAddorupdateShow: false,
});

const addressAddorupdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  state.addressAddorupdateShow = true;
  nextTick(() => {
    addressAddorupdateRef.value.init(id);
  });
};

const { getDataList, sizeChangeHandle, currentChangeHandle, deleteBatchHandle } = useCrud(
  state
);
</script>
