<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-06-25 16:26:32
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-07-03 17:32:23
-->
<template>
  <div class="title-line">
    <div class="title">基本信息</div>
    <el-link type="primary" @click="lookSpList">查看审批数据</el-link>
  </div>
  <div class="content">
    <el-form
      label-position="top"
      ref="formRef"
      :model="state.formData"
      :rules="state.rules"
    >
      <el-form-item label="审批事由" prop="title">
        <el-input v-model="state.formData.title" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="审批详情" prop="flowRemark" required>
        <WangEditor
          class="editor"
          v-model="state.formData.flowRemark"
          placeholder="请输入..."
        ></WangEditor>
        <!-- <el-input
          type="textarea"
          v-model="state.formData.flowRemark"
          placeholder="请输入..."
          :rows="5"
        ></el-input> -->
      </el-form-item>
      <el-form-item label="附件">
        <el-upload
          class="upload"
          v-model:file-list="state.fileList"
          :headers="{ Authorization: cache.getToken() }"
          :action="constant.uploadUrl"
          :before-upload="beforeUpload"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
          accept=".jpeg,.jpg,.doc,.docx,.xls,.xlsx,.pdf,.png,.zip,.rar"
        >
          <el-button type="primary" v-if="state.fileList.length < 30">上传附件</el-button>
          <template #tip>
            <div class="el-upload__tip">最多支持上传30个文件</div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <!-- 审批流程 -->
    <approval ref="approvalRef" @emit-user="getOAuser" @emit-user-copy="getOAuserCopy" />
    <div class="btns">
      <el-button type="primary" @click="submitData" :loading="state.submitLoading"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { workCommonFlowInfo } from "@/api/workbench";
import WangEditor from "@/components/wang-editor/index.vue";
import cache from "@/utils/cache";
import constant from "@/utils/constant";
import { ElMessage } from "element-plus";
import { reactive, ref } from "vue";
import approval from "./approval.vue";

const formRef = ref();
const approvalRef = ref();
const state = reactive({
  oaflowuser1: [],
  oaflowuser2: [],
  fileList: [],
  formData: {
    title: "",
    flowRemark: "",
    attachmentList: [],
    oaFlowPersonList: [],
  },
  rules: {
    title: [{ required: true, message: "请输入审批事由", trigger: "blur" }],
  },
  submitLoading: false,
});
import { useRouter } from "vue-router";
const router = useRouter();
// 查看审批数据
const lookSpList = () => {
  router.push({
    path: "/approvalCenter/imake/index",
    query: {
      flowType: "1",
    },
  });
};
const handleSuccess = (res, file) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  state.formData.attachmentList.push(res.data);
};
const beforeUpload = (file) => {
  let fileType=""
  if(file.name){
    let nameArray=file.name.split(".");
    fileType=nameArray[nameArray.length-1];
  }
  if (
    [
      "jpg",
      "png",
      "jpeg",
      "doc",
      "docx",
      "xls",
      "pdf",
      "xlsx",
      "gif",
      "zip",
      "rar",
    ].indexOf(fileType) == -1
  ) {
    ElMessage.error("格式不支持");
    return false;
  }
  return true;
};
const handleRemove = (file, files) => {
  let uploadList = [];
  for (let i of files) {
    if ("response" in i) {
      uploadList.push(i.response.data);
    } else {
      uploadList.push(i);
    }
  }
  state.formData.attachmentList = uploadList;
};
const getOAuser = (val) => {
  let arr = val.map((item) => ({
    type: 1,
    status: 2,
    userId: item.id,
    userName: item.name,
  }));
  arr[0].status = 3;
  state.oaflowuser1 = arr;
};
const getOAuserCopy = (val) => {
  state.oaflowuser2 = val.map((item) => ({
    type: 2,
    userId: item.id,
    userName: item.name,
  }));
};
const submitData = () => {
  formRef.value.validate((valid) => {
    console.log(state.formData);
    if (!valid) return;
    if (state.formData.flowRemark == "") return ElMessage.error("请输入审批详情");
    if (state.oaflowuser1.length <= 0) return ElMessage.error("请选择审批人");
    state.formData.oaFlowPersonList = [...state.oaflowuser1, ...state.oaflowuser2];
    state.submitLoading = true;
    workCommonFlowInfo(state.formData).then((res) => {
      state.submitLoading = false;
      if (res.code !== 0) {
        ElMessage.error(res.msg);
        return;
      } else {
        ElMessage.success("提交成功");
        state.formData.attachmentList = [];
        state.formData.flowRemark = "";
        state.formData.title = "";
        state.oaflowuser1 = [];
        state.oaflowuser2 = [];
        state.fileList = [];
        state.formData.oaFlowPersonList = [];
        approvalRef.value.clearInfo();
        formRef.value.clearValidate();
        formRef.value.resetFields();
      }
    });
  });
};
</script>

<style scoped lang="scss">
.title-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .title {
    text-align: center;
    padding: 8px 0;
    width: 100px;
    border-left: 3px solid #409eff;
  }
}
.editor {
  width: 98%;
}
.btns {
  margin: 20px 0;
}
.upload {
  width: 98%;
}
</style>
