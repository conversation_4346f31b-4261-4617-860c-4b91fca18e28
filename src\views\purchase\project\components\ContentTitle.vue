<template>
  <div class="title">
    {{ props.title }}
    <slot name="right"></slot>
  </div>
</template>
<script setup lang="ts">
interface IProps {
  title: string;
}
const props = withDefaults(defineProps<IProps>(), {
  title: "标题",
});
</script>
<style lang="scss" scoped>
.title {
  padding-bottom: 12px;
  padding-left: 8px;
  font-size: 16px;
  font-weight: 700;
  border-bottom: 1px solid #e4e7ed;
}
</style>
