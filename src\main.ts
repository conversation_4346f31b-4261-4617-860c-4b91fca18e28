import '@/assets/icon/iconfont'
import FastRadioGroup from '@/components/fast-radio-group'
import FastSelect from '@/components/fast-select'
import FastTableColumn from '@/components/fast-table-column'
import FastUser from '@/components/fast-user'
import SvgIcon from '@/components/svg-icon'
import '@/icons/iconfont/iconfont'
import '@/icons/iconfont/pc-iconfont.css'
import '@/icons/iconfont/pc-iconfont.js'
import '@/styles/index.scss'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { createPinia } from 'pinia'
import 'virtual:svg-icons-register'
import { createApp } from 'vue'
import VueDOMPurifyHTML from 'vue-dompurify-html'; // 解决v-html 的安全隐患
import VXETable from 'vxe-table'
import 'vxe-table/lib/style.css'
import 'xe-utils'
import App from './App.vue'
import { i18n } from './i18n'
import { router } from './router'
import { registerStore } from './store'
import { directive } from './utils/directive'


VXETable.setup({
	zIndex: 3000,
	select: {
		transfer: true
	}
})

const app = createApp(App)
app.use(createPinia())
// 注册 Pinia
registerStore()

// 注册 自定义指令
directive(app)
app.use(router)
app.use(i18n)
app.use(FastTableColumn)
app.use(FastRadioGroup)
app.use(VueDOMPurifyHTML)
app.use(FastSelect)
app.use(FastUser)
app.use(SvgIcon)
app.use(ElementPlus)
app.use(VXETable)
app.mount('#app')
