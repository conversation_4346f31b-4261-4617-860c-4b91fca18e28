<template>
  <div>
    <!-- 生成二维码组件 -->
    <canvas ref="qrcodeCanvas" class="canvas"></canvas>
  </div>
</template>
<script lang="ts" setup>
import QRCode from 'qrcode'
import { ref } from 'vue'
const props = defineProps({
	urlCode: {
		type: String,
		required: true
	},
  size:{
		type: String,
		required: true
  }
})
const qrcodeCanvas=ref()
const generateQRCode=()=> {
  const canvas = qrcodeCanvas.value
  QRCode.toCanvas(canvas, props.urlCode, (error) => {
    if (error) console.error(error)
  })
}
defineExpose({
	generateQRCode
})
</script>
<style scoped>
.canvas{
  width: 250px!important;
  height: 250px!important;
}
</style>