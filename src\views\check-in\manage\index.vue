<template>
  <el-card>
    <div style="display: flex; justify-content: space-between">
      <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
        <el-form-item>
          <el-input
            v-model="state.queryForm.name"
            placeholder="请输入考勤组名称"
            clearable
            style="width: 250px"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()" type="primary">查询</el-button>
        </el-form-item>
      </el-form>
      <div>
        <el-button type="primary" @click="addOrUpdateHandle()">新增</el-button>
      </div>
    </div>
    <el-table
      v-loading="state.dataListLoading"
      :data="state.dataList"
      border
      style="width: 100%"
      @selection-change="selectionChangeHandle"
    >
      <el-table-column
        prop="name"
        label="考勤组名称"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="name"
        label="适用人员"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="shiftType"
        label="班次类型"
        header-align="center"
        align="center"
      >
        <template #default="scope">
          <div>固定班制</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="weekdayList"
        label="考勤时间"
        header-align="center"
        align="center"
      >
        <template #default="scope">
          <div>
            <div v-for="item in computeWeeklist(scope.row.weekdayList)">
              {{ item.weekday + " " + item.workStartTime + "-" + item.workEndTime }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        label="更新时间"
        header-align="center"
        align="center"
      ></el-table-column>
      <!-- <el-table-column
        prop="updateTime"
        label="负责人"
        header-align="center"
        align="center"
      ></el-table-column> -->
      <el-table-column
        label="操作"
        fixed="right"
        header-align="center"
        align="center"
        width="150"
      >
        <template #default="scope">
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)"
            >编辑</el-button
          >
          <ElButton type="primary" link @click="deleteBatchHandle(scope.row.id)"
            >删除</ElButton
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="state.pageNo"
      :page-sizes="state.pageSizes"
      :page-size="state.pageSize"
      :total="state.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    >
    </el-pagination>

    <!-- 弹窗, 新增 / 修改 -->
    <div v-if="edit">
      <add-or-update
        ref="addOrUpdateRef"
        @refreshDataList="
          () => {
            edit = false;
            getDataList();
          }
        "
        @close="() => (edit = false)"
      ></add-or-update>
    </div>
  </el-card>
</template>

<script setup lang="ts" name="WorkAttendanceGroupIndex">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { ElButton } from "element-plus";
import { nextTick, reactive, ref } from "vue";
import AddOrUpdate from "./add-or-update.vue";
import dayjs from "dayjs";

const edit = ref(false);

const state: IHooksOptions = reactive({
  dataListUrl: "/work/attendanceGroup/page",
  deleteUrl: "/work/attendanceGroup",
  queryForm: {
    name: "",
  },
});

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  edit.value = true;
  nextTick(() => {
    addOrUpdateRef.value.init(id);
  });
};

const computeWeeklist = (weekdayList) => {
  let weekList = ["星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"];
  let newWeekdayList = [];
  let resWeekdayList = weekdayList ?? [];
  //休息日
  let restWeekList = weekList.filter((item) => {
    return resWeekdayList.findIndex((ele) => ele.weekday === item) === -1;
  });
  weekList = weekList.filter((item) => {
    return resWeekdayList.findIndex((ele) => ele.weekday === item) !== -1;
  });
  computeWeekdayList(weekList, weekdayList ?? []).forEach((item) => {
    if (item.length > 0) {
      newWeekdayList.push({
        weekday: item
          .map((ele) => {
            return ele.weekday;
          })
          .join("、"),
        workStartTime: dayjs("1997-01-01 " + item[0].workStartTime).format("HH:mm"),
        workEndTime: dayjs("1997-01-01 " + item[0].workEndTime).format("HH:mm"),
      });
    }
  });
  return newWeekdayList;
};
const computeWeekdayList = (weekList, resWeekdayList) => {
  let newWeekdayList = [];
  if (weekList.length > 0) {
    let weekdayItem = [];
    let firstWeek = weekList[0];
    let index = resWeekdayList.findIndex((item) => item.weekday === firstWeek);
    if (index !== -1) {
      let firstWorkStartTime = resWeekdayList[index].workStartTime;
      let firstWorkEndTime = resWeekdayList[index].workEndTime;
      resWeekdayList
        .filter((ele) => {
          return (
            ele.workStartTime === firstWorkStartTime &&
            ele.workEndTime === firstWorkEndTime
          );
        })
        .forEach((item) => {
          weekdayItem.push(item);
        });
      weekList = weekList.filter((item) => {
        return weekdayItem.findIndex((ele) => ele.weekday === item) === -1;
      });
      newWeekdayList.push(weekdayItem);
      newWeekdayList.push(...computeWeekdayList(weekList, resWeekdayList));
    } else {
      return newWeekdayList;
    }
  }
  return newWeekdayList;
};

const {
  getDataList,
  selectionChangeHandle,
  sizeChangeHandle,
  currentChangeHandle,
  deleteBatchHandle,
} = useCrud(state);
</script>
