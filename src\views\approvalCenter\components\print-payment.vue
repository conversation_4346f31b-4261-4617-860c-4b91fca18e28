<template>
  <div class="print">
    <div class="print-title">{{ props?.alldata?.infoTypeLabel }}</div>
    <div class="print-people">打印人员：{{ userStore.user.realName }}</div>
    <div class="print-id">
      <div class="print-id-main">审批编号：{{ props?.alldata?.flowNo }}</div>
      <div class="print-id-time">
        打印时间：{{ dayjs().format("YYYY-MM-DD HH:mm:ss") }}
      </div>
    </div>
    <div class="content">
      <div class="content-item content-two">
        <div class="content-item-top"></div>
        <div class="content-two-item">
          <div class="content-two-item-title">
            <div class="content-two-item-title-left"></div>
            <span class="content-two-item-title-text">申请人</span>
            <el-image class="content-two-item-title-image" :src="PrintBgPng"></el-image>
          </div>

          <div class="content-two-item-value">
            <div class="content-two-item-value-left"></div>
            <div class="content-two-item-value-main">
              {{ props?.alldata?.creatorName }}
            </div>
          </div>
        </div>
        <div class="content-two-item">
          <div class="content-two-item-title">
            <div class="content-two-item-title-left"></div>
            <el-image class="content-two-item-title-image" :src="PrintBgPng"></el-image>
            <span class="content-two-item-title-text">申请时间</span>
          </div>
          <div class="content-two-item-value">
            <div class="content-two-item-value-left"></div>
            <div class="content-two-item-value-main">
              {{ props?.alldata?.createTime }}
            </div>
          </div>
        </div>
        <div class="content-item-right"></div>
      </div>
      <div class="content-item content-two">
        <div class="content-item-top"></div>
        <div class="content-two-item">
          <div class="content-two-item-title">
            <div class="content-two-item-title-left"></div>
            <el-image class="content-two-item-title-image" :src="PrintBgPng"></el-image>
            <span class="content-two-item-title-text">所属部门</span>
          </div>
          <div class="content-two-item-value">
            <div class="content-two-item-value-left"></div>
            <div class="content-two-item-value-main">{{ props?.alldata?.orgName }}</div>
          </div>
        </div>
        <div class="content-two-item">
          <div class="content-two-item-title">
            <div class="content-two-item-title-left"></div>
            <el-image class="content-two-item-title-image" :src="PrintBgPng"></el-image>
            <span class="content-two-item-title-text">审批状态</span>
          </div>
          <div class="content-two-item-value">
            <div class="content-two-item-value-left"></div>
            <div class="content-two-item-value-main">
              {{
                getDictLabel(
                  appStore.dictList,
                  "flow_status",
                  props?.alldata?.auditStatus
                )
              }}
            </div>
          </div>
        </div>
        <div class="content-item-right"></div>
      </div>
      <template v-if="props.showDetail">
        <div class="content-item content-title">
          <div class="content-item-top"></div>
          <div class="content-item-left"></div>
          <div class="content-title-text">审批详情</div>
          <div class="content-item-right"></div>
        </div>
        <div class="content-item content-one">
          <div class="content-item-top"></div>
          <div class="content-one-title">
            <div class="content-one-title-left"></div>
            <el-image class="content-one-title-image" :src="PrintBgPng"></el-image>
            <span class="content-one-title-text">项目</span>
          </div>
          <div class="content-one-value">
            <div class="content-one-value-left"></div>
            <div class="content-one-value-main">
              {{ props?.alldata?.oaPaymentFlowInfoVO?.projectName }}
            </div>
          </div>
          <div class="content-item-right"></div>
        </div>
        <div class="content-item content-one">
          <div class="content-item-top"></div>
          <div class="content-one-title">
            <div class="content-one-title-left"></div>
            <el-image class="content-one-title-image" :src="PrintBgPng"></el-image>
            <span class="content-one-title-text">付款事由</span>
          </div>
          <div class="content-one-value">
            <div class="content-one-value-left"></div>
            <div class="content-one-value-main">
              {{ props?.alldata?.oaPaymentFlowInfoVO?.flowRemark }}
            </div>
          </div>
          <div class="content-item-right"></div>
        </div>
        <div class="content-item content-two">
          <div class="content-item-top"></div>
          <div class="content-two-item">
            <div class="content-two-item-title">
              <div class="content-two-item-title-left"></div>
              <span class="content-two-item-title-text">付款金额（元）</span>
              <el-image class="content-two-item-title-image" :src="PrintBgPng"></el-image>
            </div>

            <div class="content-two-item-value">
              <div class="content-two-item-value-left"></div>
              <div class="content-two-item-value-main">
                {{ props?.alldata?.oaPaymentFlowInfoVO?.amount }}
              </div>
            </div>
          </div>
          <div class="content-two-item">
            <div class="content-two-item-title">
              <div class="content-two-item-title-left"></div>
              <el-image class="content-two-item-title-image" :src="PrintBgPng"></el-image>
              <span class="content-two-item-title-text">付款时间</span>
            </div>
            <div class="content-two-item-value">
              <div class="content-two-item-value-left"></div>
              <div class="content-two-item-value-main">
                {{ props?.alldata?.oaPaymentFlowInfoVO?.createTime }}
              </div>
            </div>
          </div>
          <div class="content-item-right"></div>
        </div>
        <div class="content-item content-two">
          <div class="content-item-top"></div>
          <div class="content-two-item">
            <div class="content-two-item-title">
              <div class="content-two-item-title-left"></div>
              <span class="content-two-item-title-text">收款账号名称</span>
              <el-image class="content-two-item-title-image" :src="PrintBgPng"></el-image>
            </div>

            <div class="content-two-item-value">
              <div class="content-two-item-value-left"></div>
              <div class="content-two-item-value-main">
                {{ props?.alldata?.oaPaymentFlowInfoVO?.receivingCompany }}
              </div>
            </div>
          </div>
          <div class="content-two-item">
            <div class="content-two-item-title">
              <div class="content-two-item-title-left"></div>
              <el-image class="content-two-item-title-image" :src="PrintBgPng"></el-image>
              <span class="content-two-item-title-text">银行账号</span>
            </div>
            <div class="content-two-item-value">
              <div class="content-two-item-value-left"></div>
              <div class="content-two-item-value-main">
                {{ props?.alldata?.oaPaymentFlowInfoVO?.bankNo }}
              </div>
            </div>
          </div>
          <div class="content-item-right"></div>
        </div>
        <div class="content-item content-two">
          <div class="content-item-top"></div>
          <div class="content-two-item">
            <div class="content-two-item-title">
              <div class="content-two-item-title-left"></div>
              <span class="content-two-item-title-text">开户行</span>
              <el-image class="content-two-item-title-image" :src="PrintBgPng"></el-image>
            </div>

            <div class="content-two-item-value">
              <div class="content-two-item-value-left"></div>
              <div class="content-two-item-value-main">
                {{ props?.alldata?.oaPaymentFlowInfoVO?.openingBank }}
              </div>
            </div>
          </div>
          <div class="content-two-item">
            <div class="content-two-item-title">
              <div class="content-two-item-title-left"></div>
              <el-image class="content-two-item-title-image" :src="PrintBgPng"></el-image>
              <span class="content-two-item-title-text"></span>
            </div>
            <div class="content-two-item-value">
              <div class="content-two-item-value-left"></div>
              <div class="content-two-item-value-main"></div>
            </div>
          </div>
          <div class="content-item-right"></div>
        </div>
      </template>

      <template v-if="props.showFile">
        <div class="content-item content-more">
          <div class="content-item-top"></div>
          <div class="content-more-title">
            <div class="content-more-title-left"></div>
            <el-image class="content-more-title-image" :src="PrintBgPng"></el-image>
            <span class="content-more-title-text">附件</span>
          </div>
          <div class="content-more-value">
            <div class="content-more-value-left"></div>
            <template
              v-for="(item, index) in props?.alldata?.oaPaymentFlowInfoVO?.attachmentList"
            >
              <div class="content-more-value-item">
                <div class="content-more-value-item-top" v-if="index !== 0"></div>
                <div class="content-more-value-item-text">
                  {{ item.name }}
                </div>
              </div>
            </template>
          </div>
          <div class="content-item-right"></div>
        </div>
      </template>

      <div class="content-item content-title">
        <div class="content-item-top"></div>
        <div class="content-item-left"></div>
        <div class="content-title-text">审批记录</div>
        <div class="content-item-right"></div>
      </div>
      <div class="content-item content-one">
        <div class="content-item-top"></div>
        <div class="content-one-title">
          <div class="content-one-title-left"></div>
          <el-image class="content-one-title-image" :src="PrintBgPng"></el-image>
          <span class="content-one-title-text">发起人</span>
        </div>
        <div class="content-one-value">
          <div class="content-one-value-left"></div>
          <div class="content-one-value-main">
            {{ props?.alldata?.creatorName }} / 发起审批
            <span v-if="props?.alldata?.createTime"
              >（{{ props?.alldata?.createTime }}）</span
            >
          </div>
        </div>
        <div class="content-item-right"></div>
      </div>
      <div class="content-item content-more">
        <div class="content-item-top"></div>
        <div class="content-more-title">
          <div class="content-more-title-left"></div>
          <el-image class="content-more-title-image" :src="PrintBgPng"></el-image>
          <span class="content-more-title-text">审批人</span>
        </div>
        <div class="content-more-value">
          <div class="content-more-value-left"></div>
          <template v-for="(item, index) in props?.alldata?.oaFlowPersonVOS">
            <div class="content-more-value-item">
              <div class="content-more-value-item-top" v-if="index !== 0"></div>
              <div class="content-more-value-item-text">
                {{ item?.userName }} /
                {{ getDictLabel(appStore.dictList, "flow_status", item.status) }}
                <span v-if="item?.completeTime">（{{ item?.completeTime }}）</span>
              </div>
            </div>
          </template>
        </div>
        <div class="content-item-right"></div>
      </div>
      <div class="content-item content-one">
        <div class="content-item-top"></div>
        <div class="content-one-title">
          <div class="content-one-title-left"></div>
          <el-image class="content-one-title-image" :src="PrintBgPng"></el-image>
          <span class="content-one-title-text">抄送人</span>
        </div>
        <div class="content-one-value">
          <div class="content-one-value-left"></div>
          <div class="content-one-value-main">
            {{
              props?.alldata?.oaFlowPersonVOSCS
                .map((item) => {
                  return item.userName;
                })
                .join("、")
            }}
          </div>
        </div>
        <div class="content-item-right"></div>
      </div>
      <template v-if="props.showReply && state?.comments?.length > 0">
        <div class="content-item content-title">
          <div class="content-item-top"></div>
          <div class="content-item-left"></div>
          <div class="content-title-text">回复</div>
          <div class="content-item-right"></div>
        </div>
        <template v-for="(item, index) in state.comments">
          <div class="content-item content-emptytwo">
            <div class="content-item-top"></div>
            <div class="content-emptytwo-item">
              <div class="content-emptytwo-item-left"></div>
              {{ item?.userName }}：{{ item?.remark }}
              <span v-for="(ele, index) in item?.oaFlowPersonVOS">
                @{{ ele?.userName }}
              </span>
              <span>（{{ item?.createTime }}）</span>
            </div>
            <div class="content-emptytwo-item">
              <div class="content-emptytwo-item-left"></div>

              <template v-if="item?.attachmentList?.length > 0">
                附件：{{ item?.attachmentList?.map((ele) => ele.name).join(";") }}
              </template>
            </div>
            <div class="content-item-left"></div>
            <div class="content-item-right"></div>
          </div>
        </template>
      </template>

      <div class="content-item-bottom"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import PrintBgPng from "@/assets/print_bg.png";
import { useUserStore } from "@/store/modules/user";
import dayjs from "dayjs";
import { getDictLabel } from "@/utils/tool";
import { useAppStore } from "@/store/modules/app";
import { reactive } from "vue";
import { getFlowComment } from "@/api/workbench";
import { onMounted } from "vue";
const userStore = useUserStore();
const appStore = useAppStore();

interface IProps {
  showDetail?: boolean;
  showFile?: boolean;
  showReply?: boolean;
  alldata?: object;
}

const props = withDefaults(defineProps<IProps>(), {
  showDetail: false,
  showFile: false,
  showReply: false,
  alldata: () => {
    return {};
  },
});

interface IState {
  comments: any;
}

const state = reactive<IState>({
  comments: [],
});

onMounted(() => {
  getUserComment();
});

const getUserComment = () => {
  getFlowComment(props.alldata.id).then((res) => {
    if (res.code === 0) {
      state.comments = res.data;
    }
  });
};
</script>

<style lang="scss" scoped>
@import "@/assets/css/print.scss";
</style>
