<template>
  <el-popover width="400" placement="bottom" trigger="click">
    <template #reference>
      <el-badge :value="unreadCount" :hidden="unreadCount == 0" :max="99" class="message_badge">
        <svg-icon icon="icon-bell" @click="getList"> </svg-icon>
      </el-badge>
    </template>
    <el-tabs v-model="activeName">
      <el-tab-pane label="我的站内信" name="notice">
        <el-scrollbar class="message-list">
          <template v-for="item in list" :key="item.id">
            <div class="message-item">
              <div class="icon-box">
                <span class="unread" v-if="item.readStatus!==true"></span>
                <template v-if="item.templateCode==='daka_tixing'">
                  <img alt="" class="message-icon" v-if="item.clockType==='1'" src="@/assets/image/notice/to_work.png" />
                  <img alt="" class="message-icon" v-if="item.clockType==='2'" src="@/assets/image/notice/off_work.png" />
                </template>
                <template v-else>
                  <img alt="" class="message-icon" v-if="item.avatar" :src="item.avatar" />
                  <img alt="" class="message-icon" v-else src="@/assets/avatar.png" />
                </template>
              </div>
              <div class="message-content">
                <span class="message-title">
                  {{ item.templateNickname }}：{{ item.templateContent }}
                </span>
                <span class="message-date">
                  {{ formatDate(item.createTime) }}
                </span>
              </div>
            </div>
          </template>
        </el-scrollbar>
      </el-tab-pane>
    </el-tabs>
    <!-- 更多 -->
    <div style="margin-top: 10px; text-align: right">
      <el-button type="primary" :icon="View" @click="goMyList">查看全部</el-button>
    </div>
  </el-popover>
</template>
<script lang="ts" setup>
import { formatDate } from "@/utils/formatTime";
import * as NotifyMessageApi from "@/api/message/notifyMessage";
import { useRouter } from "vue-router";
import { ref, onMounted } from "vue";
import { RefSymbol } from "@vue/reactivity";
import { View } from "@element-plus/icons-vue";
import $bus from '@/utils/bus'

defineOptions({ name: "Message" });

const { push } = useRouter();
const activeName = ref("notice");
const unreadCount = ref(0); // 未读消息数量
const list = ref<any[]>([]); // 消息列表

// 获得消息列表
const getList = async () => {
  await NotifyMessageApi.getUnreadNotifyMessageList({ pageNo: 1, pageSize: 10 }).then(
    (res) => {
      console.info(res);
      Object.assign(list.value, res.data.list);
      list.value.map(item=>{
        if(item.templateCode==="daka_tixing"){
          if(item.templateContent.indexOf("上班")>-1){
            item.clockType="1"
          }else if(item.templateContent.indexOf("下班")>-1){
            item.clockType="2"
          }
        }
      })
    }
  );
  // 强制设置 unreadCount 为 0，避免小红点因为轮询太慢，不消除
  // unreadCount.value = 0;
};

// // 获得未读消息数
const getUnreadCount = async () => {
  NotifyMessageApi.getUnreadNotifyMessageCount().then((res) => {
    unreadCount.value = res.data;
  });
};
 
// 跳转我的站内信
const goMyList = () => {
  push("/message/notify/list");
};

// ========== 初始化 =========
onMounted(() => {
  $bus.on('read',(value)=>{
    getUnreadCount()
  })
  // 首次加载小红点
  getUnreadCount()
  // 轮询刷新小红点
  setInterval(
    () => {
      getUnreadCount()
    },
    1000 * 10
  )
});
</script>

<style lang="scss" scoped>
.message_badge {
  :deep(.el-badge__content.is-fixed) {
    right: 15px;
    top: 15px;
  }
}

.message-list {
  display: flex;
  height: 400px;
  flex-direction: column;

  .message-item {
    display: flex;
    align-items: center;
    padding: 20px 0;
    border-bottom: 1px solid var(--el-border-color-light);
    .icon-box{
      position: relative;
    }
    .unread{
      position:absolute;
      left:35px;
      top:0;
      z-index: 10;
      background: #F56C6C;
      width:12px;
      height:12px;
      border-radius: 50%;
    }

    &:last-child {
      border: none;
    }

    .message-icon {
      width: 40px;
      height: 40px;
      margin: 0 20px 0 5px;
    }

    .message-content {
      display: flex;
      flex-direction: column;

      .message-title {
        margin-bottom: 5px;
      }

      .message-date {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }
    }
  }
}
</style>
