.demo-index-root {
	min-width: 1200px;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.demo-signer-root {
	min-width: 1200px;
	height: 100%;
	display: flex;
	flex-direction: column;
	overflow: auto;
}

.demo-page-wrapper {
	margin: 0 auto;
	width: 1200px;
}

.demo-table {
	margin: 0 10px;
	width: 100%;
	min-height: 400px;
}

.demo-index-main {
	position: relative;
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow-y: auto;
	overflow-x: hidden;
	background: #eef0f4;
}

.demo-index-board {
	background: #ffffff;
	border-radius: 4px;
	margin: 10px auto 10px;
	padding: 30px 20px;
	position: relative;
	flex: 1;
}

.demo-signer-main {
	display: flex;
	flex-direction: column;
	flex: 1;
	overflow: auto;
	background: #eef0f4;
}

.demo-signer-board {
	height: 100%;
	width: 100%;
	box-sizing: border-box;
	background: #ffffff;
	border-radius: 4px;
	/* margin: 10px auto 80px; */
	padding: 30px 20px 0;
	flex: 1;
}
