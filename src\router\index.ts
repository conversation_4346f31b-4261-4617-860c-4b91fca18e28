import { i18n } from '@/i18n'
import { useAppStore } from '@/store/modules/app'
import { useRouterStore } from '@/store/modules/router'
import { useUserStore } from '@/store/modules/user'
import { isExternalLink, pathToCamel } from '@/utils/tool'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import { RouteRecordRaw, createRouter, createWebHashHistory } from 'vue-router'

NProgress.configure({ showSpinner: false })

const constantRoutes: RouteRecordRaw[] = [
	{
		path: '/redirect',
		component: () => import('../layout/index.vue'),
		children: [
			{
				path: '/redirect/:path(.*)',
				component: () => import('../layout/components/Router/Redirect.vue')
			}
		]
	},
	{
		path: '/iframe/:query?',
		component: () => import('../layout/components/Router/Iframe.vue')
	},
	{
		path: '/login',
		component: () => import('../views/login/login/index.vue')
	},
	{
		path: '/register',
		component: () => import('../views/login/register/index.vue')
	},
	{
		path: '/404',
		component: () => import('../views/404.vue')
	},
	{
		path: '/registerPay',
		component: () => import('../views/login/registerPay/index.vue')
	},
	{
		path: '/registerScan',
		component: () => import('../views/login/registerPay/scan.vue')
	},
	{
		path: '/registerPayOk',
		component: () => import('../views/login/registerPay/payOk.vue')
	},
]

const asyncRoutes: RouteRecordRaw = {
	path: '/',
	component: () => import('../layout/index.vue'),
	redirect: '/home',
	children: [
		{
			path: '/home',
			name: 'Home',
			fullPath: '/home',
			component: () => import('../views/home/<USER>'),
			meta: {
				title: i18n.global.t('router.home'),
				affix: true
			}
		},
		{
			path: '/profile',
			name: 'ProfileIndex',
			component: () => import('../views/profile/index.vue'),
			meta: {
				title: i18n.global.t('router.profile'),
				cache: true
			}
		},
		{
			path: 'notifyMssage',
			component: () => import('@/views/message/notify/notifyMessage/index.vue'),
			name: 'MyNotifyMessage',
			meta: {
				canTo: true,
				hidden: true,
				noTagsView: false,
				title: '站内信消息'
			}
		},
		{
			path: '/purchase/project/action',
			name: 'purchase-project-action',
			component: () => import('../views/purchase/project/action.vue'),
			meta: {
				title: '操作项目',
				cache: true
			}
		},
		{
			path: '/purchase/plan/action',
			name: 'purchase-plan-action',
			component: () => import('../views/purchase/plan/action.vue'),
			meta: {
				title: '操作采购计划',
				cache: true
			}
		},
		{
			path: '/purchase/notice/action',
			name: 'purchase-notice-action',
			component: () => import('../views/purchase/notice/action.vue'),
			meta: {
				title: '采购公告编辑',
				cache: true
			}
		},
		{
			path: '/moreUse/action',
			name: 'moreUse-action',
			component: () => import('../views/moreUse/action.vue'),
			meta: {
				title: '公告详情',
				cache: true
			}
		},
		{
			path: '/purchase/changenotice/action',
			name: 'purchase-changenotice-action',
			component: () => import('../views/purchase/changenotice/action.vue'),
			meta: {
				title: '变更公告编辑',
				cache: true
			}
		},
		{
			path: '/purchase/invation/action',
			name: 'purchase-invation-action',
			component: () => import('../views/purchase/invation/action.vue'),
			meta: {
				title: '邀请函编辑',
				cache: true
			}
		},
		{
			path: '/sppurchase/makeoffer/offer',
			name: 'sppurchase-makeoffer-offer',
			component: () => import('../views/sppurchase/makeoffer/offer.vue'),
			meta: {
				title: '报价',
				cache: true
			}
		},
		{
			path: '/sppurchase/bidcharge/pay',
			name: 'sppurchase-bidcharge-pay',
			component: () => import('../views/sppurchase/bidcharge/pay.vue'),
			meta: {
				title: '中标服务费支付',
				cache: true
			}
		},
		{
			path: '/supplier/manage/action',
			name: 'supplier-manage-action',
			component: () => import('../views/supplier/manage/action.vue'),
			meta: {
				title: '操作供应商',
				cache: true
			}
		},
		{
			path: '/picketage/enter/action',
			name: 'picketage-enter-action',
			component: () => import('../views/picketage/enter/action.vue'),
			meta: {
				title: '中标人录入',
				cache: true
			}
		},
		{
			path: '/picketage/notice/action',
			name: 'picketage-notice-action',
			component: () => import('../views/picketage/notice/action.vue'),
			meta: {
				title: '新增中标结果通知书',
				cache: true
			}
		},
		{
			path: '/picketage/announcement/action',
			name: 'picketage-announcement-action',
			component: () => import('../views/picketage/announcement/action.vue'),
			meta: {
				title: '新增中标结果公示',
				cache: true
			}
		},
		{
			path: '/purchase/plan/quotation',
			name: 'purchase-plan-quotation',
			component: () => import('../views/purchase/plan/quotation.vue'),
			meta: {
				title: '报价信息',
				cache: true
			}
		},
		{
			path: '/purchase/contract/view',
			name: 'purchase-contract-view',
			component: () => import('../views/sppurchase/contract/view.vue'),
			meta: {
				title: '合同查看',
				cache: true
			}
		},
		{
			path: '/purchase/contract/addorupdate',
			name: 'purchase-contract-addorupdate',
			component: () => import('../views/sppurchase/contract/addorupdate.vue'),
			meta: {
				title: '合同编辑',
				cache: true
			}
		},
		{
			path: '/purchase/plan/track',
			name: 'purchase-plan-track',
			component: () => import('../views/purchase/plan/track.vue'),
			meta: {
				title: '计划追踪',
				cache: true
			}
		},
		{
			path: '/employee/info/detail',
			name: 'employee-info-detail',
			component: () => import('../views/employee/info/detail.vue'),
			meta: {
				title: '信息详情',
				cache: true
			}
		},
		{
			path: '/moreUse/index',
			name: 'moreUse-index',
			component: () => import('../views/moreUse/index.vue'),
			meta: {
				title: '公告',
				cache: true
			}
		},
		{
			path: '/contractLib/index',
			name: 'contractLib-index',
			component: () => import('../views/contract/contractLib/index.vue'),
			meta: {
				title: '我的合同',
				cache: true
			}
		},
		{
			path: '/contractSupplier/index',
			name: 'contractSupplier-index',
			component: () => import('../views/contract/contractSupplier/index.vue'),
			meta: {
				title: '供应商合同',
				cache: true
			}
		},
		{
			path: '/contractSignature/index',
			name: 'contractSignature-index',
			component: () => import('../views/contract/contractSignature/index.vue'),
			meta: {
				title: '签章合同管理',
				cache: true
			}
		},
		{
			path: '/contractSignatureCw/index',
			name: 'contractSignatureCw-index',
			component: () => import('../views/contract/contractSignatureCw/index.vue'),
			meta: {
				title: '合同签章管理',
				cache: true
			}
		},
		{
			path: '/contractLib/lookContract',
			name: 'contractLib-lookContract',
			component: () => import('../views/contract/contractLib/lookContract.vue'),
			meta: {
				title: '查看合同',
				cache: true
			}
		},
		{
			path: '/message/notify/list',
			name: 'message-notify-list',
			component: () => import('../views/message/notify/list/index.vue'),
			meta: {
				title: '站内信消息',
				cache: true
			}
		},
	]
}

// 配置常量菜单
export const constantMenu = [
	{
		id: 1000,
		name: 'Demo',
		url: null,
		openStyle: 0,
		icon: 'icon-windows',
		children: [
			{
				id: 1001,
				name: 'Icon 图标',
				url: 'demo/icons/index',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			},
			{
				id: 1002,
				name: '表单设计器',
				url: 'demo/formDesign/form',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			},
			{
				id: 1003,
				name: '表单生成器',
				url: 'demo/formDesign/generate',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			},
			{
				id: 1004,
				name: '二维码生成',
				url: 'demo/qrcode/index',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			},
			{
				id: 1005,
				name: '页面打印',
				url: 'demo/printJs/index',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			},
			{
				id: 1006,
				name: '图片裁剪',
				url: 'demo/cropper/index',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			},
			{
				id: 1007,
				name: '富文本编辑器',
				url: 'demo/wangeditor/index',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			},
			{
				id: 1008,
				name: 'SSE消息推送',
				url: 'demo/sse/index',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			},
			{
				id: 1009,
				name: 'ECharts图表',
				url: 'demo/echarts/index',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			},
			{
				id: 1010,
				name: 'map',
				url: 'demo/tx-map/index',
				openStyle: 0,
				icon: 'icon-unorderedlist'
			}
		]
	}
]

export const errorRoute: RouteRecordRaw = {
	path: '/:pathMatch(.*)',
	redirect: '/404'
}

export const router = createRouter({
	history: createWebHashHistory(),
	routes: constantRoutes
})

// 白名单列表
const whiteList = ['/login', '/register','/registerPay','/registerScan','/registerPayOk']

// 路由跳转前
router.beforeEach(async (to, from, next) => {
	NProgress.start()

	const appStore = useAppStore()
	const userStore = useUserStore()
	const routerStore = useRouterStore()

	// token存在的情况
	if (userStore.token) {
		if (to.path === '/login' || to.path === '/register') {
			next('/home')
		} else {
			// 用户信息不存在，则重新拉取
			if (!userStore.user.id) {
				try {
					await userStore.getUserInfoAction()
					await userStore.getAuthorityListAction()
					await appStore.getDictListAction()
				} catch (error) {
					// 请求异常，则跳转到登录页
					userStore?.setToken('')
					next('/login')
					return Promise.reject(error)
				}

				// 动态菜单+常量菜单
				const menuRoutes = await routerStore.getMenuRoutes()

				// 获取扁平化路由，将多级路由转换成一级路由
				const keepAliveRoutes = getKeepAliveRoutes(menuRoutes, [])

				// 添加菜单路由
				asyncRoutes.children?.push(...keepAliveRoutes)
				router.addRoute(asyncRoutes)

				// 错误路由
				router.addRoute(errorRoute)

				// 保存路由数据
				routerStore.setRoutes(constantRoutes.concat(asyncRoutes))

				// 搜索菜单需要使用
				routerStore.setSearchMenu(keepAliveRoutes)

				next({ ...to, replace: true })
			} else {
				next()
			}
		}
	} else {
		// 没有token的情况下，可以进入白名单
		if (whiteList.indexOf(to.path) > -1) {
			next()
		} else {
			next('/login')
		}
	}
})

// 路由加载后
router.afterEach(() => {
	NProgress.done()
})

// 获取扁平化路由，将多级路由转换成一级路由
export const getKeepAliveRoutes = (rs: RouteRecordRaw[], breadcrumb: string[]): RouteRecordRaw[] => {
	const routerList: RouteRecordRaw[] = []

	rs.forEach((item: any) => {
		if (item.meta.title) {
			breadcrumb.push(item.meta.title)
		}

		if (item.children && item.children.length > 0) {
			routerList.push(...getKeepAliveRoutes(item.children, breadcrumb))
		} else {
			item.meta.breadcrumb.push(...breadcrumb)
			routerList.push(item)
		}

		breadcrumb.pop()
	})
	return routerList
}

// 加载vue组件
const layoutModules = import.meta.glob('/src/views/**/*.vue')

// 根据路径，动态获取vue组件
const getDynamicComponent = (path: string): any => {
	return layoutModules[`/src/views/${path}.vue`]
}

// 根据菜单列表，生成路由数据
export const generateRoutes = (menuList: any): RouteRecordRaw[] => {
	const routerList: RouteRecordRaw[] = []

	menuList.forEach((menu: any) => {
		let component
		let path
		if (menu.children && menu.children.length > 0) {
			component = () => import('@/layout/index.vue')
			path = '/p/' + menu.id
		} else {
			// 判断是否iframe
			if (isIframeUrl(menu)) {
				component = () => import('@/layout/components/Router/Iframe.vue')
				path = '/iframe/' + menu.id
			} else {
				component = getDynamicComponent(menu.url)
				path = '/' + menu.url
			}
		}
		const route: RouteRecordRaw = {
			path: path,
			name: pathToCamel(path),
			component: component,
			children: [],
			meta: {
				title: menu.name,
				icon: menu.icon,
				id: '' + menu.id,
				url: menu.url,
				cache: true,
				newOpen: menu.openStyle === 1,
				breadcrumb: []
			}
		}

		// 有子菜单的情况
		if (menu.children && menu.children.length > 0) {
			route.children?.push(...generateRoutes(menu.children))
		}

		routerList.push(route)
	})

	return routerList
}

// 判断是否iframe
const isIframeUrl = (menu: any): boolean => {
	// 如果是新页面打开，则不用iframe
	if (menu.openStyle === 1) {
		return false
	}

	// 是否外部链接
	return isExternalLink(menu.url)
}
