<template>
  <el-card>
    <div class="service">
      <div class="service-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="采购计划编号"
              clearable
              v-model="state.queryForm.packageNo"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="采购计划名称"
              clearable
              v-model="state.queryForm.packageName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="中标人"
              clearable
              v-model="state.queryForm.winBidder"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item>
            <el-date-picker
              v-model="selectDate"
              type="daterange"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="onChangeSelectDate"
            />
          </el-form-item> -->
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onResetSearch()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="service-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="winBidder"
            label="中标人"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="serviceCharge"
            label="缴纳金额（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <!-- <el-table-column
            prop="materialSpec"
            label="缴费时间"
            header-align="center"
            align="center"
          ></el-table-column> -->
          <fast-table-column
            prop="orderStatus"
            label="缴费状态"
            dict-type="order_status"
          ></fast-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-button type="primary" link @click="onClickDetail(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="service-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <Detail :show="detail.show" :id="detail.id" @close="onCloseDetail"></Detail>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import dayjs from "dayjs";
import { reactive, ref } from "vue";
import Detail from "./components/Detail.vue";

const state = reactive<IHooksOptions>({
  queryForm: {
    sendStatus: "01",
    packageNo: "",
    packageName: "",
    winBidder: "",
    startTime: "",
    endTime: "",
  },
  dataListUrl: "/purchase/winBidder/page",
});

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

const selectDate = ref([]);

const onChangeSelectDate = () => {
  if (selectDate.value && selectDate.value.length === 2) {
    state.queryForm.startTime = dayjs(selectDate.value[0]).format("YYYY-MM-DD");
    state.queryForm.endTime = dayjs(selectDate.value[1]).format("YYYY-MM-DD");
  } else {
    state.queryForm.startTime = void 0;
    state.queryForm.endTime = void 0;
  }
};

const onResetSearch = () => {
  state.queryForm.packageNo = "";
  state.queryForm.packageName = "";
  state.queryForm.winBidder = "";
  state.queryForm.startTime = void 0;
  state.queryForm.endTime = void 0;
  selectDate.value = [];
  state.pageNo = 1;
  getDataList();
};

interface IDetail {
  show: boolean;
  id?: number;
}

const detail = reactive<IDetail>({
  show: false,
  id: void 0,
});

const onClickDetail = (row: any) => {
  detail.id = row.id;
  detail.show = true;
};

const onCloseDetail = () => {
  detail.show = false;
  detail.id = void 0;
};
</script>
<style lang="scss" scoped></style>
