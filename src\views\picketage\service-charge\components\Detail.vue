<template>
  <el-drawer
    v-model="state.show"
    title="中标服务费"
    :size="850"
    class="picketage_enter_detail_drawer"
    @close="onClickClose"
  >
    <el-card>
      <div class="detail">
        <div class="detail-info">
          <div class="detail-info-title">基本信息</div>
          <div class="detail-info-content">
            <el-form
              ref="baseInfoRef"
              :model="state.baseInfo"
              label-width="140px"
              class="detail-form"
            >
              <el-form-item prop="packageNo" label="采购计划编号">
                <div class="detail-form-value">
                  {{ state.baseInfo.packageNo }}
                </div>
              </el-form-item>
              <el-form-item prop="packageName" label="采购计划名称">
                <div class="detail-form-value">
                  {{ state.baseInfo.packageName }}
                </div>
              </el-form-item>
              <el-form-item prop="serviceCharge" label="缴纳金额（元）">
                <div class="detail-form-value">
                  {{ state.baseInfo.serviceCharge }}
                </div>
              </el-form-item>
              <el-form-item prop="orderStatus" label="状态">
                <div class="detail-form-value">
                  <span
                    v-html="getDictLabelList('order_status', state.baseInfo.orderStatus)"
                  ></span>
                </div>
              </el-form-item>
              <el-form-item
                prop="winBidder"
                label="缴纳方式"
                v-if="state.baseInfo.orderStatus == '01'"
              >
                <div class="detail-form-value">
                  <span
                    v-html="getDictLabelList('pay_type', state.baseInfo.payType)"
                  ></span>
                </div>
              </el-form-item>
              <el-form-item
                prop="winBidder"
                label="缴纳时间"
                v-if="state.baseInfo.orderStatus == '01'"
              >
                <div class="detail-form-value">
                  {{ state.baseInfo.paySuccTime }}
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="detail-invoice" v-if="state.baseInfo.orderStatus == '01'">
          <div class="detail-invoice-title">发票信息</div>
          <div class="detail-invoice-content">
            <el-form
              ref="invoiceInfoRef"
              :model="state.invoiceInfo"
              label-width="140px"
              class="detail-form"
            >
              <el-form-item prop="invoiceHeader" label="发票抬头">
                <div class="detail-form-value">
                  {{ state.invoiceInfo.invoiceHeader }}
                </div>
              </el-form-item>
              <el-form-item prop="creditCode" label="统一社会信用代码">
                <div class="detail-form-value">
                  {{ state.invoiceInfo.creditCode }}
                </div>
              </el-form-item>
              <el-form-item prop="registerAddress" label="单位地址">
                <div class="detail-form-value">
                  {{ state.invoiceInfo.registerAddress }}
                </div>
              </el-form-item>
              <el-form-item prop="registerPhone" label="电话">
                <div class="detail-form-value">
                  {{ state.invoiceInfo.registerPhone }}
                </div>
              </el-form-item>
              <el-form-item prop="bankDeposit" label="开户银行">
                <div class="detail-form-value">
                  {{ state.invoiceInfo.bankDeposit }}
                </div>
              </el-form-item>
              <el-form-item prop="basicAccount" label="银行账号">
                <div class="detail-form-value">
                  {{ state.invoiceInfo.basicAccount }}
                </div>
              </el-form-item>
              <el-form-item prop="invoiceType" label="发票类型">
                <div class="detail-form-value">
                  <span
                    v-html="
                      getDictLabelList('invoice_type', state.invoiceInfo.invoiceType)
                    "
                  ></span>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </el-card>
  </el-drawer>
</template>
<script setup lang="ts">
import { reactive, watch } from "vue";
import {
  getPayGoods,
  getWinBidderInfo,
  getInvoiceHeader,
} from "@/api/picketage/service-charge";
import { getDictLabelList } from "@/utils/tool";

interface IProps {
  id?: number;
  show: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
});

interface IBaseInfo {
  packageNo: string;
  packageName: string;
  serviceCharge: string;
  orderStatus: string;
  payType: string;
  paySuccTime: string;
}

interface IInvoiceInfo {
  invoiceHeader: string;
  creditCode: string;
  registerAddress: string;
  registerPhone: string;
  bankDeposit: string;
  basicAccount: string;
  invoiceType: string;
}

interface IState {
  show: boolean;
  baseInfo: IBaseInfo;
  invoiceInfo: IInvoiceInfo;
}

const state = reactive<IState>({
  show: false,
  baseInfo: {
    packageNo: "",
    packageName: "",
    serviceCharge: "",
    orderStatus: "",
    payType: "",
    paySuccTime: "",
  },
  invoiceInfo: {
    invoiceHeader: "",
    creditCode: "",
    registerAddress: "",
    registerPhone: "",
    bankDeposit: "",
    basicAccount: "",
    invoiceType: "00",
  },
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      state.show = true;
      if (props.id) {
        GetWinBidderInfo(props.id);
      }
    }
  }
);

// onMounted(() => {
//   if (props.id) {
//     GetWinBidderInfo(props.id);
//   }
// });

const GetWinBidderInfo = (id: number) => {
  Promise.all([getWinBidderInfo(id), getPayGoods(id)]).then((res) => {
    let infoRes: any = res[0];
    let goodsRes: any = res[1];
    let headerId = void 0;
    let newBaseInfo = {
      packageNo: "",
      packageName: "",
      serviceCharge: "",
      orderStatus: "",
      payType: "",
      paySuccTime: "",
    };
    if (infoRes.code === 0) {
      newBaseInfo.packageNo = infoRes.data.packageNo;
      newBaseInfo.packageName = infoRes.data.packageName;
      newBaseInfo.serviceCharge = infoRes.data.serviceCharge;
      newBaseInfo.orderStatus = infoRes.data.orderStatus;
    }
    if (goodsRes.code === 0) {
      newBaseInfo.payType = goodsRes?.data?.payType;
      newBaseInfo.paySuccTime = goodsRes?.data?.paySuccTime;
      headerId = goodsRes?.data?.headerId;
    }
    state.baseInfo = newBaseInfo;
    if (headerId) {
      getInvoiceHeader(headerId).then((resInvoice: any) => {
        if (resInvoice.code === 0) {
          let newInvoiceInfo = {
            invoiceHeader: resInvoice.data.invoiceHeader,
            creditCode: resInvoice.data.creditCode,
            registerAddress: resInvoice.data.registerAddress,
            registerPhone: resInvoice.data.registerPhone,
            bankDeposit: resInvoice.data.bankDeposit,
            basicAccount: resInvoice.data.basicAccount,
            invoiceType: resInvoice.data.invoiceType,
          };
          state.invoiceInfo = newInvoiceInfo;
        }
      });
    }
  });
};

const emit = defineEmits<{
  (e: "on-close"): void;
}>();

const onClickClose = () => {
  state.show = false;
  emit("on-close");
};
</script>

<style lang="scss">
.picketage_enter_detail_drawer {
  .el-drawer__body {
    padding: 14px !important;
    background: #f5f6fa !important;
  }
}
</style>
<style lang="scss" scoped>
.detail {
  &-info {
    &-title {
      font-size: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e8e8e8;
    }
    &-content {
      margin-top: 16px;
    }
  }
  &-invoice {
    margin-top: 16px;
    &-title {
      font-size: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e8e8e8;
    }
    &-content {
      margin-top: 16px;
    }
  }
}
.detail-form-value {
  color: #000;
}
</style>
