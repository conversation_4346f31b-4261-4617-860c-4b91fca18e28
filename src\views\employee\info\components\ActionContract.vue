<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    draggable
  >
    <div class="action-certificate">
      <el-form :model="dataForm" :rules="dataRule" ref="dataFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="contractType" label="合同类型">
              <fast-select
                v-model="dataForm.contractType"
                dict-type="contract_type"
                placeholder="合同类型"
              ></fast-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="contractNo" label="合同编号">
              <el-input v-model="dataForm.contractNo" placeholder="合同编号"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="signStartTime" label="合同开始时间">
              <el-date-picker
                v-model="dataForm.signStartTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="合同开始时间"
                style="width: 100%"
                :disabled-date="disabledStartTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="signEndTime" label="合同结束时间">
              <el-date-picker
                v-model="dataForm.signEndTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="合同结束时间"
                style="width: 100%"
                :disabled-date="disabledEndTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="contractStatus" label="合同状态">
              <fast-select
                v-model="dataForm.contractStatus"
                dict-type="contract_status"
                placeholder="合同状态"
              ></fast-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="contractDepId" label="合同公司">
              <el-select
                v-model="dataForm.contractDepId"
                placeholder="合同公司"
                style="width: 100%"
              >
                <el-option
                  v-for="role in companyList"
                  :key="role.id"
                  :label="role.name"
                  :value="role.id"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="contractEffective" label="合同有效期">
              <div style="display: flex; align-items: center; width: 100%">
                <div style="flex: 1">
                  <el-input
                    v-model="dataForm.contractEffective"
                    placeholder="合同有效期"
                    style="width: 100%"
                  ></el-input>
                </div>
                <fast-select
                  v-model="dataForm.timeType"
                  dict-type="contract_time_type"
                  style="width: 60px; margin-left: 10px"
                ></fast-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="signTime" label="合同签订时间">
              <el-date-picker
                v-model="dataForm.signTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="合同签订时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="remark" label="附件">
              <!-- <el-upload
                :action="constant.uploadUrl"
                :headers="{ Authorization: cache.getToken() }"
                :before-upload="beforeUpload"
                :on-success="handleSuccess"
                :show-file-list="false"
              >
                <el-button type="primary">上传</el-button>
              </el-upload> -->
              <div class="list_file_upload">
                <div
                  class="list_file_upload_action"
                  style="display: flex; align-items: center"
                >
                  <div>
                    <el-upload
                      :action="constant.uploadUrl"
                      :headers="{ Authorization: cache.getToken() }"
                      :before-upload="beforeUpload"
                      :on-success="handleSuccess"
                      :show-file-list="false"
                    >
                      <el-button type="primary">上传</el-button>
                    </el-upload>
                  </div>
                </div>
                <div class="list_file_upload_list">
                  <div
                    class="list_file_upload_list_item"
                    v-for="(item, index) in dataForm.attachmentList"
                  >
                    <div
                      class="list_file_upload_list_item_text"
                      @click="onClickDownload(item)"
                    >
                      {{ item.name }}
                    </div>
                    <div class="list_file_upload_list_item_action">
                      <el-icon @click="onDeleteFile(index)"><DeleteFilled /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="remark" label="备注">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import {
  getCompanyList,
  useContractSubmitApi,
  getContractWorkInfo,
} from "@/api/employee/index";
import { ElMessage, UploadProps } from "element-plus";
import cache from "@/utils/cache";
import constant from "@/utils/constant";
import service from "@/utils/request";
import dayjs from "dayjs";
import { DeleteFilled } from "@element-plus/icons-vue";

const visible = ref(false);
const dataFormRef = ref();

const dataRule = ref({
  contractType: [{ required: true, message: "合同类型不能为空", trigger: "blur" }],
  contractNo: [{ required: true, message: "合同编号不能为空", trigger: "blur" }],
  contractStatus: [{ required: true, message: "合同状态不能为空", trigger: "blur" }],
  contractDepId: [{ required: true, message: "合同公司不能为空", trigger: "blur" }],
});

const dataForm = reactive({
  id: "",
  contractType: "",
  contractNo: "",
  signStartTime: "",
  signEndTime: "",
  contractStatus: "",
  contractDepId: "",
  contractEffective: "",
  signTime: "",
  remark: "",
  timeType: "年",
  attachmentList: [],
});

const companyList = ref([]);

const disabledStartTime = (time) => {
  if (time && dataForm.signEndTime) {
    return time.getTime() > new Date(dataForm.signEndTime).getTime();
  } else {
    return false;
  }
};

const disabledEndTime = (time) => {
  if (time && dataForm.signStartTime) {
    return time.getTime() < new Date(dataForm.signStartTime).getTime();
  } else {
    return false;
  }
};

const GetCompanyList = () => {
  getCompanyList().then((res) => {
    if (res.code === 0) {
      companyList.value = res.data;
    }
  });
};

onMounted(() => {
  GetCompanyList();
});

const init = (id) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  // id 存在则为修改
  if (id) {
    GetContractWorkInfo(id);
  }
};

const GetContractWorkInfo = (id) => {
  getContractWorkInfo(id).then((response) => {
    Object.assign(dataForm, response.data);
  });
};

const emit = defineEmits(["refreshDataList"]);

const submitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    useContractSubmitApi(dataForm).then(() => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        },
      });
    });
  });
};

const beforeUpload = () => {
  return true;
};
const handleSuccess: UploadProps["onSuccess"] = (res, file) => {
  if (res.code === 0) {
    let newAttachmentItem = {
      platform: res.data.platform,
      url: res.data.url,
      name: res.data.name,
      size: res.data.size,
    };
    let newAttachmentList = JSON.parse(JSON.stringify(dataForm.attachmentList));
    newAttachmentList.push(newAttachmentItem);
    dataForm.attachmentList = newAttachmentList;
  } else {
    ElMessage.error("上传失败：" + res.msg);
  }
};

const onClickDownload = async (item: any) => {
  const response = await service.get(item.url, {
    responseType: "blob", // important
  });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement("a");
  link.href = url;
  if (item.name) {
    link.setAttribute("download", item.name);
  } else {
    let fileUrlList = item.url.split(".");
    let fileType = fileUrlList[fileUrlList.length - 1];
    let newFileName = dayjs().format("YYYYMMDDHHmmsss") + "." + fileType;
    link.setAttribute("download", newFileName);
  }
  document.body.appendChild(link);
  link.click();
};

const onDeleteFile = (index: number) => {
  let newAttachmentList = JSON.parse(JSON.stringify(dataForm.attachmentList));
  newAttachmentList = newAttachmentList.filter((item: any, ele: number) => {
    return index !== ele;
  });
  dataForm.attachmentList = newAttachmentList;
};

defineExpose({
  init,
});
</script>
<style lang="scss" scoped>
.list_file {
  display: flex;
  align-items: center;
  &_upload {
    &_list {
      &_item {
        display: flex;
        align-items: center;
        &_text {
          color: #409eff;
          margin-right: 10px;
          cursor: pointer;
        }
        &_action {
          color: #545252;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
