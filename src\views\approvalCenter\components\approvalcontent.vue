<template>
  <div class="content">
    <div class="content-top">
      <div class="content-top-p1">审批编号：{{ props.alldata.flowNo }}</div>
      <div class="content-top-p2">
        {{ props.alldata.creatorName }} 提交的 {{ props.alldata.infoTypeLabel }}
      </div>
      <div class="content-top-p3">所在部门：{{ props.alldata.orgName }}</div>
      <svg-icon
        class="content-top-icon"
        size="80"
        icon="icon-yitongguo"
        v-show="props.alldata.auditStatus == 4"
      ></svg-icon>
      <svg-icon
        class="content-top-icon"
        size="80"
        icon="icon-yichexiao1"
        v-show="props.alldata.auditStatus == 6"
      ></svg-icon>
      <svg-icon
        class="content-top-icon"
        size="80"
        icon="icon-shenhezhong"
        v-show="props.alldata.auditStatus == 3"
      ></svg-icon>
      <svg-icon
        class="content-top-icon"
        size="80"
        icon="icon-weitongguo"
        v-show="props.alldata.auditStatus == 5"
      ></svg-icon>
      <svg-icon
        class="content-top-icon"
        size="80"
        icon="icon-daishenhe"
        v-show="props.alldata.auditStatus == 2"
      ></svg-icon>
    </div>
    <el-divider></el-divider>
    <div class="content-ctr">
      <slot name="center"></slot>
    </div>
    <el-divider></el-divider>
    <div class="content-btm">
      <div class="content-btm-title">审批流程</div>
      <div class="content-btm-timeline">
        <ul class="el-timeline">
          <li class="el-timeline-item">
            <div class="el-timeline-item__tail"></div>
            <div class="el-timeline-item__node">
              <el-avatar
                class="nodeimg"
                shape="circle"
                :size="40"
                :src="props.alldata.avatar ?? defaultAvatar"
              ></el-avatar>
              <!-- <svg-icon  class='nodeimg' icon="icon-morentouxiang" size="40"></svg-icon> -->
              <svg-icon icon="icon-tongguo" class="nodeimg-state" size="18"></svg-icon>
            </div>
            <div class="el-timeline-item__wrapper">
              <div class="el-timeline-item__content">
                <div class="timeline-user">
                  <div class="timeline-lft">
                    <p class="p1">发起人</p>
                    <p class="p2">
                      {{ props.alldata.creatorName }} <span class="tag">(发起人)</span>
                    </p>
                  </div>
                  <div class="timeline-time">{{ props.alldata.createTime }}</div>
                </div>
              </div>
            </div>
          </li>
          <li class="el-timeline-item" v-for="item in props.alldata.oaFlowPersonVOS">
            <div class="el-timeline-item__tail"></div>
            <div class="el-timeline-item__node">
              <el-avatar
                class="nodeimg"
                shape="circle"
                :size="40"
                :src="item.avatar ?? defaultAvatar"
              ></el-avatar>
              <svg-icon
                icon="icon-zhuanqian"
                class="nodeimg-state"
                size="18"
                v-show="item.replacePersonFlow"
              ></svg-icon>
              <svg-icon
                icon="icon-shenpizhong"
                class="nodeimg-state"
                size="18"
                v-show="item.status == 3 || item.status == 2"
              ></svg-icon>
              <svg-icon
                icon="icon-jujue"
                class="nodeimg-state"
                size="18"
                v-show="item.status == 5"
              ></svg-icon>
              <svg-icon
                icon="icon-chexiao"
                class="nodeimg-state"
                size="18"
                v-show="item.status == 6"
              ></svg-icon>
              <svg-icon
                icon="icon-tongguo"
                class="nodeimg-state"
                size="18"
                v-show="item.status == 4"
              ></svg-icon>
            </div>
            <div class="el-timeline-item__wrapper">
              <div class="el-timeline-item__content">
                <div class="timeline-user">
                  <div class="timeline-lft">
                    <p class="p1">{{ item.status == 6 ? "发起人" : "审批人" }}</p>
                    <p class="p2">
                      {{ item.userName }}
                      <span
                        class="tag"
                        v-if="
                          (props.alldata.auditStatus == 6 && item.status == 4) ||
                          props.alldata.auditStatus != 6
                        "
                        >({{
                          getDictLabel(appStore.dictList, "flow_status", item.status)
                        }})</span
                      >
                      <span class="tag1" v-if="item.status == 6">(撤销)</span>
                    </p>
                  </div>
                  <div class="timeline-time">{{ item.completeTime }}</div>
                </div>
                <div class="timeline-extra" v-if="item.flowRemark">
                  {{ item.flowRemark }}
                </div>
              </div>
            </div>
          </li>
          <li class="el-timeline-item">
            <div class="el-timeline-item__tail"></div>
            <div class="el-timeline-item__node">
              <svg-icon icon="icon-a-chaosong5" class="nodeimg" size="40"></svg-icon>
            </div>
            <div class="el-timeline-item__wrapper">
              <div class="el-timeline-item__content">
                <div class="timeline-user">
                  <div class="timeline-lft">
                    <p class="p1">抄送人</p>
                    <p class="p2">{{ state.chaosongname }}</p>
                  </div>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <el-divider></el-divider>
    <div class="content-reply">
      <div class="content-reply-title">审批评论</div>
      <div class="content-reply-ipt">
        <el-input
          v-model="state.input"
          type="textarea"
           @input="handleOpinionInput"
          :rows="4"
          placeholder="发表评论"
        />
        <div class="at_upload" v-if="state.originAtusers.length > 0">
          @的成员：
          <div class="atdiv" v-for="(item, index) in state.atusers">
            <el-link :underline="false" type="primary"> @{{ item.userName }} </el-link>
            <svg-icon
              icon="icon-close"
              color="#409eff"
              class="del"
              @click="atuserDel(index)"
            ></svg-icon>
          </div>
        </div>
        <div class="at_upload" v-if="state.fileList.length > 0">
          已上传附件：
          <div class="filediv" v-for="(item, index) in state.fileList">
            <el-link class="file" :underline="false" type="primary">{{
              item.name
            }}</el-link>
            <svg-icon
              icon="icon-close"
              color="#409eff"
              class="del"
              @click="filedel(index)"
            ></svg-icon>
          </div>
        </div>
        <div class="btn">
          <el-button @click="openSelectUser">@ 提醒关注</el-button>
          <el-upload
            class="upload"
            v-model:file-list="state.fileList"
            :headers="{ Authorization: cache.getToken() }"
            :action="constant.uploadUrl"
            :before-upload="beforeUpload"
            :on-success="handleSuccess"
            :show-file-list="false"
            accept=".jpeg,.jpg,.doc,.docx,.xls,.xlsx,.pdf,.png,.zip,.rar"
          >
            <el-button>上传附件</el-button>
          </el-upload>
          <el-button
            class="btn-rht"
            type="primary"
            :loading="state.comloading"
            @click="sendcomment"
            >发送</el-button
          >
        </div>
      </div>
      <div class="content-reply-chat">
        <ul class="chat-ul">
          <li class="chat-li" v-for="item in state.comments">
            <div class="chat-li-user">
              <el-avatar
                class="nodeimg"
                shape="circle"
                :size="40"
                :src="item.avatar ?? defaultAvatar"
              ></el-avatar>
            </div>
            <div class="chat-li-item">
              <div class="chat-li-item-1">
                <div class="chat-li-item-1-name">{{ item.userName }}</div>
                <div class="chat-li-item-1-time">{{ item.createTime }}</div>
              </div>
              <div class="chat-li-item-2">
                <span class="atuser" v-for="atuser in item.oaFlowPersonVOS"
                  >@{{ atuser.userName }}</span
                >
                {{ item.remark }}
              </div>
              <div class="chat-li-item-3">
                <block v-for="(ff, index) in item.attachmentList">
                  <block v-if="checkSuffixIsImg(ff.name)">
                    <img
                      class="img"
                      :src="ff.url"
                      alt="img"
                      @click="onPreviewImage(ff.url)"
                    />
                  </block>
                  <block v-else>
                    <el-link
                      class="file"
                      :underline="false"
                      type="primary"
                      @click="downloadFile(ff.url, ff.name)"
                      >{{ ff.name }}</el-link
                    >
                  </block>
                </block>
              </div>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- 选择成员 -->
  <select-user-tree
    ref="selectUserTreeRef"
    @select-user="getSelectUser"
  ></select-user-tree>
  <el-image-viewer
    v-if="preview.show"
    @close="onClosePreviewImage"
    :url-list="preview.url"
  >
  </el-image-viewer>
</template>

<script setup lang="ts">
import { getFlowComment, sendFlowComment } from "@/api/workbench";
import { useAppStore } from "@/store/modules/app";
import { useUserStore } from "@/store/modules/user";
import cache from "@/utils/cache";
import constant from "@/utils/constant";
import { downloadFile, getDictLabel } from "@/utils/tool";
import SelectUserTree from "@/views/workbench/components/selectUserTree.vue";
import { ElMessage } from "element-plus";
import { reactive, ref, watch } from "vue";
import { IView } from "../types";
import defaultAvatar from "@/assets/avatar.png";

const userStore = useUserStore();
const appStore = useAppStore();
const selectUserTreeRef = ref();
interface IProps {
  id: number;
  alldata: IView;
}
const props = withDefaults(defineProps<IProps>(), {
  id: 0,
  alldata: {},
});

const state = reactive({
  input: "",
  chaosongname: "",
  comloading: false,
  comments: [],
  atusers: [],
  fileList: [],
  originAtusers: [],
  formData: {
    flowId: 0,
    infoId: 0,
    infoType: 0,
    remark: "",
    oaFlowPersonVOS: [],
    attachmentList: [],
  },
});

watch(
  () => props.alldata,
  (val) => {
    state.input = "";
    state.chaosongname = "";
    state.atusers = [];
    state.originAtusers = [];
    state.fileList = [];
    val.oaFlowPersonVOSCS.map((item: any) => {
      state.chaosongname += item.userName + "、";
    });
    state.chaosongname = state.chaosongname.substring(0, state.chaosongname.length - 1);
    getComment();
  }
);

const openSelectUser = () => {
  selectUserTreeRef.value?.init(state.originAtusers);
};
const getSelectUser = (val) => {
  state.originAtusers = val;
  if (val.length <= 0) {
    state.atusers = [];
  } else {
    let arr = val.map((item) => ({
      type: 3,
      userId: item.id,
      userName: item.name,
      flowId: props.alldata.id,
      infoId: props.alldata.infoId,
      infoType: props.alldata.infoType,
    }));
    state.atusers = arr;
  }
};
const handleSuccess = (res, file) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  state.formData.attachmentList.push(res.data);
};
const filedel = (index) => {
  state.formData.attachmentList = state.fileList.splice(index, 1);
};
const checkSuffixIsImg = (filename) => {
  let fileNameList = filename.split(".");
  let fileType = fileNameList[fileNameList.length - 1];
  if (["jpg", "png", "jpeg"].indexOf(fileType) == -1) {
    return false;
  }
  return true;
};
const atuserDel = (index) => {
  state.atusers.splice(index, 1);
  state.originAtusers.splice(index, 1);
};
const sendcomment = () => {
  state.comloading = true;
  state.formData.flowId = props.alldata.id;
  state.formData.infoId = props.alldata.infoId;
  state.formData.infoType = props.alldata.infoType;
  state.formData.remark = state.input;
  state.formData.oaFlowPersonVOS = state.atusers;
  sendFlowComment(state.formData).then((res) => {
    if (res.code !== 0) {
      ElMessage.error(res.msg);
      return;
    } else {
      ElMessage.success("发送成功");
      state.fileList = [];
      state.atusers = [];
      state.originAtusers = [];
      state.input = "";
      state.formData.attachmentList=[]
      getComment();
    }
    state.comloading = false;
  });
};
const getComment = () => {
  getFlowComment(props.alldata.id).then((res) => {
    if (res.code !== 0) {
      ElMessage.error(res.msg);
      return;
    } else {
      state.comments = res.data;
    }
  });
};

const preview = reactive({
  show: false,
  url: [],
});

const onClosePreviewImage = () => {
  preview.show = false;
  preview.url = [];
};

const onPreviewImage = (url: string) => {
  let newUrl = [];
  newUrl.push(url);
  preview.url = newUrl;
  preview.show = true;
};

const handleOpinionInput = (value:string) =>{
  if(value.includes('@')){
    openSelectUser()
    state.input=value.replace('@', '')
  }
}


</script>

<style scoped lang="scss">
.content {
  &-top {
    position: relative;
    &-p1 {
      font-size: 14px;
      color: var(--cus-garytext);
    }
    &-p2 {
      font-size: 20px;
      color: #333;
      font-weight: bold;
      margin: 10px 0;
    }
    &-p3 {
      font-size: 14px;
      color: var(--cus-garytext);
    }
    &-icon {
      position: absolute;
      right: 15px;
      top: 10px;
    }
  }
}
.content-btm-title {
  margin-bottom: 20px;
}
.nodeimg {
  position: relative;
}
.nodeimg-state {
  position: absolute;
  right: -3px;
  bottom: -2px;
}
.el-timeline-item__wrapper {
  padding-left: 58px;
}
.el-timeline-item__tail {
  left: 20px;
  top: 32px;
}
.el-timeline-item__node {
}
.mult-node {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
}
.timeline-user {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .timeline-lft {
    margin-bottom: 10px;
    .p2 {
      font-size: 14px;
      color: #666;
      .tag {
        color: #409eff;
      }
      .tag1 {
        color: rgb(252, 55, 55);
      }
    }
  }
}
.timeline-time {
  font-size: 14px;
  color: #666;
}
.timeline-extra {
  border: 1px solid var(--el-border-color);
  border-radius: 5px;
  padding: 8px 10px;
}

.content-reply {
  &-title {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
  }
  &-ipt {
    margin: 10px 0;
    .at_upload {
      margin: 5px 0;
      .atdiv {
        display: inline-block;
        background-color: #eee;
        margin: 0 10px 0 0;
        padding: 2px 5px;
        .del {
          vertical-align: middle;
          margin-left: 10px;
          cursor: pointer;
        }
      }
      .filediv {
        display: block;
        line-height: 2;
        margin: 0 10px 3px 0;
        padding: 2px 5px;
        .del {
          vertical-align: middle;
          margin-left: 10px;
          cursor: pointer;
        }
      }
    }
    .btn {
      margin-top: 10px;
      clear: both;
      .btn-rht {
        float: right;
      }
      .upload {
        display: inline-block;
        margin-left: 10px;
      }
    }
  }
  &-chat {
    .chat-ul {
      margin: 14px;
      list-style: none;
      .chat-li {
        clear: both;
        margin-bottom: 12px;
        &::after {
          content: "";
          display: block;
          clear: both;
        }
        &-user {
          width: 40px;
          float: left;
          margin-right: 10px;
          text-align: center;
        }
        &-item {
          float: left;
          width: calc(100% - 50px);
          .atuser {
            color: #409eff;
            margin-right: 10px;
          }
          &-1 {
            display: flex;
            justify-content: space-between;
            margin: 10px 0 10px;
            &-name {
              font-size: 14px;
            }
            &-time {
              font-size: 14px;
              color: var(--cus-garytext);
            }
          }
          &-2 {
            font-size: 14px;
            margin-bottom: 10px;
          }
          &-3 {
            .img {
              width: 100px;
              display: inline-block;
              margin: 0 5px 5px 0;
            }
            .file {
              display: block;
              line-height: 1.8;
            }
          }
        }
      }
    }
  }
}
</style>
