<template>
	<el-row :gutter="6">
		<el-col :span="5">
			<el-card>
				<OrgTree @node-click="handleOrgClick" />
			</el-card>
		</el-col>
		<el-col :span="19">
			<el-card>
				<el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
					<el-form-item>
						<el-input v-model="state.queryForm.username" placeholder="用户名" clearable></el-input>
					</el-form-item>
					<el-form-item>
						<el-input v-model="state.queryForm.mobile" placeholder="手机号" clearable></el-input>
					</el-form-item>
					<!--					<el-form-item>-->
					<!--						<fast-select v-model="state.queryForm.gender" dict-type="user_gender" clearable placeholder="性别"></fast-select>-->
					<!--					</el-form-item>-->
					<el-form-item>
						<el-button @click="getDataList()">查询</el-button>
					</el-form-item>
					<el-form-item>
						<el-button v-auth="'sys:user:save'" type="primary" @click="addOrUpdateHandle()">新增</el-button>
					</el-form-item>
					<el-form-item>
						<el-button v-auth="'sys:user:delete'" type="danger" @click="deleteBatchHandle()">删除</el-button>
					</el-form-item>
					<el-form-item v-auth="'sys:user:import'">
						<el-upload :action="uploadUserExcelUrl" :before-upload="beforeUpload" :on-success="handleSuccess" :show-file-list="false">
							<el-button type="info">导入</el-button>
						</el-upload>
					</el-form-item>
					<el-form-item>
						<el-button v-auth="'sys:user:export'" type="success" @click="downloadExcel()">导出</el-button>
					</el-form-item>
				</el-form>
				<el-table
						v-loading="state.dataListLoading"
						show-overflow-tooltip
						:data="state.dataList"
						border
						style="width: 100%"
						@selection-change="selectionChangeHandle"
				>
					<el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
					<el-table-column prop="username" label="用户名" header-align="center" align="center"></el-table-column>
					<el-table-column prop="realName" label="姓名" header-align="center" align="center"></el-table-column>
					<fast-table-column prop="gender" label="性别" dict-type="user_gender"></fast-table-column>
					<el-table-column prop="mobile" label="手机号" header-align="center" align="center"></el-table-column>
					<el-table-column prop="orgName" label="所属机构" header-align="center" align="center"></el-table-column>
					<fast-table-column prop="status" label="状态" dict-type="user_status"></fast-table-column>
					<el-table-column prop="createTime" label="创建时间" header-align="center" align="center"></el-table-column>
					<el-table-column label="操作" fixed="right" header-align="center" align="center" width="120">
						<template #default="scope">
							<el-button v-auth="'sys:user:update'" type="primary" link @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
							<el-button v-auth="'sys:user:delete'" type="primary" link @click="deleteBatchHandle(scope.row.id)">删除</el-button>
						</template>
					</el-table-column>
				</el-table>
				<el-pagination
						:current-page="state.pageNo"
						:page-sizes="state.pageSizes"
						:page-size="state.pageSize"
						:total="state.total"
						layout="total, sizes, prev, pager, next, jumper"
						@size-change="sizeChangeHandle"
						@current-change="currentChangeHandle"
				>
				</el-pagination>
			</el-card>
		</el-col>
	</el-row>

	<!-- 弹窗, 新增 / 修改 -->
	<add-or-update ref="addOrUpdateRef" @refresh-data-list="getDataList"></add-or-update>
</template>

<script setup lang="ts" name="SysUserIndex">
	import { useCrud } from '@/hooks'
	import { reactive, ref } from 'vue'
	import AddOrUpdate from './add-or-update.vue'
	import { IHooksOptions } from '@/hooks/interface'
	import { ElMessage, UploadProps } from 'element-plus'
	import OrgTree from './org-tree.vue'
	import cache from '@/utils/cache'
	import constant from '@/utils/constant'

	const state: IHooksOptions = reactive({
		dataListUrl: '/sys/user/page',
		deleteUrl: '/sys/user',
		queryForm: {
			username: '',
			mobile: '',
			gender: '',
			orgId: ''
		}
	})

	const addOrUpdateRef = ref()
	const addOrUpdateHandle = (id?: number) => {
		addOrUpdateRef.value.init(id)
	}

	const handleOrgClick = (orgId: number) => {
		state.queryForm.orgId = orgId
		getDataList()
	}

	// 导入用户excel文件
	const uploadUserExcelUrl = constant.apiUrl + '/sys/user/import?access_token=' + cache.getToken()

	const downloadExcel = () => {
		downloadHandle('/sys/user/export')
	}

	const handleSuccess: UploadProps['onSuccess'] = (res, file) => {
		if (res.code !== 0) {
			ElMessage.error('上传失败：' + res.msg)
			return false
		}

		ElMessage.success({
			message: '上传成功',
			duration: 500,
			onClose: () => {
				getDataList()
			}
		})
	}

	const beforeUpload: UploadProps['beforeUpload'] = file => {
		if (file.size / 1024 / 1024 / 1024 / 1024 > 1) {
			ElMessage.error('文件大小不能超过100M')
			return false
		}
		return true
	}

	const { getDataList, selectionChangeHandle, sizeChangeHandle, currentChangeHandle, deleteBatchHandle, downloadHandle } = useCrud(state)
</script>
