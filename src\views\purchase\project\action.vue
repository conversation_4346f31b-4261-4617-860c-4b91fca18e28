<template>
  <el-card>
    <div class="action">
      <div class="action_base">
        <div class="action_base_title">
          <ContentTitle title="项目基本信息"></ContentTitle>
        </div>
        <div class="action_base_info">
          <BaseInfo
            :type="state.type"
            :baseInfo="state.baseInfo"
            @emit-ref="onEmitBaseInfoRef"
            @on-blur="onBlurBaseInfo"
            @upload-field="onUploadField"
          ></BaseInfo>
        </div>
      </div>
      <div class="action_inventory">
        <div class="action_inventory_title">
          <ContentTitle title="采购清单"></ContentTitle>
        </div>
        <div class="action_inventory_content">
          <PurchaseList
            :materialList="state.materialList"
            @on-confirm="onConfirmMaterial"
            @change-quantity="onChangeQuantity"
            @change-comment="onChangeComment"
            @on-delete="onDeleteMaterial"
          ></PurchaseList>
        </div>
      </div>
      <div class="action_btn">
        <el-button type="primary" @click="onSubmit">提交</el-button>
        <el-button @click="onCancel">取消</el-button>
      </div>
    </div>
  </el-card>
</template>
<script>
import ContentTitle from "./components/ContentTitle.vue";
import BaseInfo from "./components/BaseInfo.vue";
import PurchaseList from "./components/PurchaseList.vue";
import {
  getProjectInfo,
  saveProject,
  updateProject,
} from "@/api/purchase/project";
import { ElMessage } from "element-plus";
import { closeTab } from "@/utils/tabs";

export default {
  name: "ProjectAction",
  components: {
    ContentTitle,
    BaseInfo,
    PurchaseList,
  },
  data() {
    return {
      baseInfoRef: null,
      state: {
        type: "add",
        id: undefined,
        baseInfo: {
          projectName: "",
          projectNameModel: "",
          projectNo: "",
          budget: undefined,
          projectYear: "2024",
          orgId: null,
          projectContent: "",
          attachmentList: [],
        },
        materialList: [],
      },
    };
  },

  mounted() {
    console.log(this.$route.query);
    const { type, id } = this.$route.query;
    this.state.type = type;
    this.state.id = id;
    if (type === "update") {
      if (id) {
        getProjectInfo(id).then((res) => {
          console.log(res);
          if (res.code === 0) {
            let newBaseInfo = {
              projectName: res.data.projectName,
              projectNameModel: res.data.projectName,
              projectNo: res.data.projectNo,
              budget: res.data.budget,
              projectYear: res.data.projectYear,
              orgId: res.data.orgId,
              projectContent: res.data.projectContent,
              attachmentList: (res.data?.attachmentList || []).map((item) => {
                return {
                  name: item.name,
                  url: item.url,
                  platform: item.platform,
                  size: item.size,
                };
              }),
            };
            this.state.baseInfo = newBaseInfo;
            let newMaterialList = res.data.tbProjectMaterialVOS.map((item) => {
              return {
                id: item?.materialId ?? undefined,
                materialNo: item?.materialNo ?? undefined,
                materialName: item?.materialName ?? "",
                materialSpec: item?.materialSpec ?? "",
                materialType: item?.materialType ?? "",
                materialUnit: item?.materialUnit ?? "",
                materialQuantity: item.materialQuantity,
                comment: item?.comment ?? "",
              };
            });
            this.state.materialList = newMaterialList;
          }
        });
      }
    }
  },
  methods: {

    onDeleteMaterial(rowIndex) {
      let newMaterialList = JSON.parse(JSON.stringify(this.state.materialList));
      newMaterialList = newMaterialList.filter((item, index) => {
        return index !== rowIndex;
      });
      this.state.materialList = newMaterialList;
    },

    onConfirmMaterial(materialList) {
      let newMaterialList = JSON.parse(JSON.stringify(materialList));

      // materialList.forEach((item) => {
      //   if (newMaterialList.findIndex((ele) => ele.id === item.id) === -1) {
      //     newMaterialList.push(item);
      //   }
      // });
      this.state.materialList = newMaterialList;
    },

    onEmitBaseInfoRef(dataFormRef) {
      this.baseInfoRef = dataFormRef;
    },

    onBlurBaseInfo(field, value) {
      let newBaseInfo = JSON.parse(JSON.stringify(this.state.baseInfo));
      if (field === "projectName") {
        newBaseInfo.projectNameModel = value;
      }
      newBaseInfo[field] = value;
      this.state.baseInfo = newBaseInfo;
    },

    onUploadField(attachmentList) {
      let newBaseInfo = JSON.parse(JSON.stringify(this.state.baseInfo));
      newBaseInfo.attachmentList = attachmentList;
      this.state.baseInfo = newBaseInfo;
    },

    onChangeQuantity(rowIndex, value) {
      let newMaterialList = JSON.parse(JSON.stringify(this.state.materialList));
      // let index = newMaterialList.findIndex((item) => {
      //   return item.id === id;
      // });
      // if (index !== -1) {

      // }
      newMaterialList[rowIndex].materialQuantity = value;
      console.log(newMaterialList);
      this.state.materialList = newMaterialList;
    },

    onChangeComment(rowIndex, value) {
      let newMaterialList = JSON.parse(JSON.stringify(this.state.materialList));
      // let index = newMaterialList.findIndex((item) => {
      //   return item.id === id;
      // });
      // if (index !== -1) {
      //   newMaterialList[index].comment = value;
      // }
      newMaterialList[rowIndex].comment = value;
      this.state.materialList = newMaterialList;
    },

    onSubmit() {
      this.baseInfoRef.validate((valid) => {
        if (valid) {
          if (this.state.materialList.length > 0) {
            let reqData = {
              id: this.state.type === "update" ? this.state.id : undefined,
              projectName: this.state.baseInfo.projectName,
              projectNo: this.state.baseInfo.projectNo,
              budget: this.state.baseInfo.budget || 0,
              projectYear: this.state.baseInfo.projectYear,
              orgId: this.state.baseInfo.orgId,
              projectContent: this.state.baseInfo.projectContent,
              attachmentList: this.state.baseInfo.attachmentList.map((item) => {
                return {
                  name: item.name,
                  url: item.url,
                  platform: item.platform,
                  size: item.size,
                };
              }),
              tbProjectMaterialVOS: this.state.materialList.map((item) => {
                return {
                  materialId: item?.id ?? undefined,
                  materialNo: item?.materialNo ?? "",
                  materialName: item?.materialName ?? "",
                  materialSpec: item?.materialSpec ?? "",
                  materialType: item?.materialType ?? "",
                  materialUnit: item?.materialUnit ?? "",
                  materialQuantity: item.materialQuantity,
                  comment: item?.comment ?? "",
                };
              }),
            };
            (this.state.type === "update" ? updateProject(reqData) : saveProject(reqData)).then(
              (res) => {
                if (res.code === 0) {
                  closeTab(this.$router, this.$route);
                  this.$router.push("/purchase/project/index");
                  ElMessage.success("操作成功");
                }
              }
            );
          } else {
            ElMessage.error("请添加采购清单");
          }
        }
      });
    },

    onCancel() {
      closeTab(this.$router, this.$route);
      this.$router.push("/purchase/project/index");
    },
  },
};
</script>
<style lang="scss" scoped>
.action {
  &_base {
    &_info {
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  &_inventory {
    &_content {
      margin-top: 20px;
    }
  }
  &_btn {
    margin-top: 60px;
    display: flex;
    justify-content: center;
  }
}
</style>
