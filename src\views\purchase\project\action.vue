<template>
  <el-card>
    <div class="action">
      <div class="action_base">
        <div class="action_base_title">
          <ContentTitle title="项目基本信息"></ContentTitle>
        </div>
        <div class="action_base_info">
          <BaseInfo
            :type="state.type"
            :baseInfo="state.baseInfo"
            @emit-ref="onEmitBaseInfoRef"
            @on-blur="onBlurBaseInfo"
            @upload-field="onUploadField"
          ></BaseInfo>
        </div>
      </div>
      <div class="action_inventory">
        <div class="action_inventory_title">
          <ContentTitle title="采购清单"></ContentTitle>
        </div>
        <div class="action_inventory_content">
          <PurchaseList
            :materialList="state.materialList"
            @on-confirm="onConfirmMaterial"
            @change-quantity="onChangeQuantity"
            @change-comment="onChangeComment"
            @on-delete="onDeleteMaterial"
          ></PurchaseList>
        </div>
      </div>
      <div class="action_btn">
        <el-button type="primary" @click="onSubmit">提交</el-button>
        <el-button @click="onCancel">取消</el-button>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import ContentTitle from "./components/ContentTitle.vue";
import BaseInfo from "./components/BaseInfo.vue";
import PurchaseList from "./components/PurchaseList.vue";
import {
  getProjectInfo,
  IActionProject,
  saveProject,
  updateProject,
} from "@/api/purchase/project";
import { reactive, ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { closeTab } from "@/utils/tabs";

const router = useRouter();
const route = useRoute();
const baseInfoRef = ref();

export interface IAttachmentItem {
  name: string;
  url: string;
  platform: string;
  size: number;
}

export interface IBaseInfo {
  projectName: string;
  projectNameModel: string;
  projectNo: string;
  budget?: number;
  projectYear: string;
  orgId: any;
  projectContent: string;
  attachmentList: IAttachmentItem[];
}

export interface IMaterialItem {
  id: number;
  materialNo: string;
  materialName: string;
  materialSpec: string;
  materialType: string;
  materialUnit: string;
  materialQuantity?: number;
  comment: string;
}

interface IState {
  type: string;
  id?: number;
  baseInfo: IBaseInfo;
  materialList: IMaterialItem[];
}

const state = reactive<IState>({
  type: "add",
  id: void 0,
  baseInfo: {
    projectName: "",
    projectNameModel: "",
    projectNo: "",
    budget: void 0,
    projectYear: "2024",
    orgId:null,
    projectContent: "",
    attachmentList: [],
  },
  materialList: [],
});

interface IQuery {
  type: string;
  id?: number;
}

onMounted(() => {
  console.log(route.query);
  const { type, id } = (route.query as unknown) as IQuery;
  state.type = type;
  state.id = id;
  if (type === "update") {
    if (id) {
      getProjectInfo(id).then((res: any) => {
        console.log(res);
        if (res.code === 0) {
          let newBaseInfo = {
            projectName: res.data.projectName,
            projectNameModel: res.data.projectName,
            projectNo: res.data.projectNo,
            budget: res.data.budget,
            projectYear: res.data.projectYear,
            orgId: res.data.orgId,
            projectContent: res.data.projectContent,
            attachmentList: (res.data?.attachmentList || []).map((item: any) => {
              return {
                name: item.name,
                url: item.url,
                platform: item.platform,
                size: item.size,
              };
            }),
          };
          state.baseInfo = newBaseInfo;
          let newMaterialList = res.data.tbProjectMaterialVOS.map((item: any) => {
            return {
              id: item?.materialId ?? void 0,
              materialNo: item?.materialNo ?? void 0,
              materialName: item?.materialName ?? "",
              materialSpec: item?.materialSpec ?? "",
              materialType: item?.materialType ?? "",
              materialUnit: item?.materialUnit ?? "",
              materialQuantity: item.materialQuantity,
              comment: item?.comment ?? "",
            };
          });
          state.materialList = newMaterialList;
        }
      });
    }
  }
});

const onDeleteMaterial = (rowIndex: number) => {
  let newMaterialList = JSON.parse(JSON.stringify(state.materialList));
  newMaterialList = newMaterialList.filter((item: IMaterialItem, index: number) => {
    return index !== rowIndex;
  });
  state.materialList = newMaterialList;
};

const onConfirmMaterial = (materialList: any[]) => {
  let newMaterialList = JSON.parse(JSON.stringify(materialList));

  // materialList.forEach((item: any) => {
  //   if (newMaterialList.findIndex((ele: any) => ele.id === item.id) === -1) {
  //     newMaterialList.push(item);
  //   }
  // });
  state.materialList = newMaterialList;
};

const onEmitBaseInfoRef = (dataFormRef: any) => {
  baseInfoRef.value = dataFormRef;
};

const onBlurBaseInfo = (field: string, value: string) => {
  let newBaseInfo = JSON.parse(JSON.stringify(state.baseInfo));
  if (field === "projectName") {
    newBaseInfo.projectNameModel = value;
  }
  newBaseInfo[field] = value;
  state.baseInfo = newBaseInfo;
};

const onUploadField = (attachmentList: IAttachmentItem[]) => {
  let newBaseInfo = JSON.parse(JSON.stringify(state.baseInfo));
  newBaseInfo.attachmentList = attachmentList;
  state.baseInfo = newBaseInfo;
};

const onChangeQuantity = (rowIndex: number, value: string) => {
  let newMaterialList = JSON.parse(JSON.stringify(state.materialList));
  // let index = newMaterialList.findIndex((item: IMaterialItem) => {
  //   return item.id === id;
  // });
  // if (index !== -1) {

  // }
  newMaterialList[rowIndex].materialQuantity = value;
  console.log(newMaterialList);
  state.materialList = newMaterialList;
};

const onChangeComment = (rowIndex: number, value: string) => {
  let newMaterialList = JSON.parse(JSON.stringify(state.materialList));
  // let index = newMaterialList.findIndex((item: IMaterialItem) => {
  //   return item.id === id;
  // });
  // if (index !== -1) {
  //   newMaterialList[index].comment = value;
  // }
  newMaterialList[rowIndex].comment = value;
  state.materialList = newMaterialList;
};

const onSubmit = () => {
  baseInfoRef.value.validate((valid: Boolean) => {
    if (valid) {
      if (state.materialList.length > 0) {
        let reqData: IActionProject = {
          id: state.type === "update" ? state.id : void 0,
          projectName: state.baseInfo.projectName,
          projectNo: state.baseInfo.projectNo,
          budget: state.baseInfo.budget || 0,
          projectYear: state.baseInfo.projectYear,
          orgId: state.baseInfo.orgId,
          projectContent: state.baseInfo.projectContent,
          attachmentList: state.baseInfo.attachmentList.map((item) => {
            return {
              name: item.name,
              url: item.url,
              platform: item.platform,
              size: item.size,
            };
          }),
          tbProjectMaterialVOS: state.materialList.map((item) => {
            return {
              materialId: item?.id ?? void 0,
              materialNo: item?.materialNo ?? "",
              materialName: item?.materialName ?? "",
              materialSpec: item?.materialSpec ?? "",
              materialType: item?.materialType ?? "",
              materialUnit: item?.materialUnit ?? "",
              materialQuantity: item.materialQuantity,
              comment: item?.comment ?? "",
            };
          }),
        };
        (state.type === "update" ? updateProject(reqData) : saveProject(reqData)).then(
          (res: any) => {
            if (res.code === 0) {
              closeTab(router, route);
              router.push("/purchase/project/index");
              ElMessage.success("操作成功");
            }
          }
        );
      } else {
        ElMessage.error("请添加采购清单");
      }
    }
  });
};

const onCancel = () => {
  closeTab(router, route);
  router.push("/purchase/project/index");
};
</script>
<style lang="scss" scoped>
.action {
  &_base {
    &_info {
      margin-top: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  &_inventory {
    &_content {
      margin-top: 20px;
    }
  }
  &_btn {
    margin-top: 60px;
    display: flex;
    justify-content: center;
  }
}
</style>
