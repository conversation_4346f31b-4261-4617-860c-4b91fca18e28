<template>
  <el-card>
    <div class="archivist">
      <div class="archivist-project">
        <div class="archivist-project-title">项目信息</div>
        <div class="archivist-project-content">
          <el-descriptions :column="2" border>
            <el-descriptions-item label-align="right" width="25%">
              <template #label>
                <div class="cell-item">采购计划编号</div>
              </template>
            </el-descriptions-item>
            <el-descriptions-item label-align="right" width="25%">
              <template #label>
                <div class="cell-item">采购计划名称</div>
              </template>
            </el-descriptions-item>
            <el-descriptions-item label-align="right" width="25%">
              <template #label>
                <div class="cell-item">本次预算金额（元）</div>
              </template>
            </el-descriptions-item>
            <el-descriptions-item label-align="right" width="25%">
              <template #label>
                <div class="cell-item">开标时间</div>
              </template>
            </el-descriptions-item>
            <el-descriptions-item label-align="right" width="25%">
              <template #label>
                <div class="cell-item">项目归档状态</div>
              </template>
            </el-descriptions-item>
            <el-descriptions-item label-align="right" width="25%">
              <template #label>
                <div class="cell-item"></div>
              </template>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts"></script>

<style lang="scss" scoped>
.archivist {
  &-project {
    &-title {
      font-size: 18px;
      color: #030303;
      font-weight: 700;
    }
    &-content {
      margin-top: 16px;
    }
  }
}
</style>
