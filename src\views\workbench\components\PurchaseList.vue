<template>
  <div class="list">
    <div class="list_content">
      <vxe-table
        :data="state.materialList"
        border
        :scroll-y="{ enabled: true, gt: 20 }"
        max-height="500px"
      >
        <vxe-column type="seq" width="60" title="序号"></vxe-column>
        <vxe-column field="materialName" title="产品名称"></vxe-column>
        <vxe-column field="materialType" title="产品型号（规格参数）"></vxe-column>
        <vxe-column field="materialUnit" title="计量单位"></vxe-column>
        <vxe-column field="materialQuantity" title="采购数量">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.materialQuantity"
              placeholder="采购数量"
              @blur="onBlurQuantity($event, scope.$rowIndex)"
            ></el-input-number>
          </template>
        </vxe-column>
        <vxe-column field="comment" title="备注">
          <template #default="scope">
            <el-input
              v-model="scope.row.comment"
              placeholder="备注"
              @blur="onBlurComment($event, scope.$rowIndex)"
            ></el-input>
          </template>
        </vxe-column>
        <vxe-column field="action" title="操作">
          <template #default="scope">
            <el-button type="primary" link @click="onClickDelete(scope.$rowIndex)">
              删除
            </el-button>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <div class="list_file" style="margin-top: 20px">
      <el-form ref="dataFormRef" label-width="80px">
        <el-row>
          <el-col :span="24">
            <!-- <el-form-item label="附件">
              <el-upload
                class="upload"
                v-model:file-list="state.fileList"
                :headers="{ Authorization: cache.getToken() }"
                :action="constant.uploadUrl"
                :before-upload="beforeUpload"
                :on-success="handleSuccess"
                :on-remove="handleRemove"
                accept='.jpeg,.jpg,.doc,.docx,.xls,.xlsx,.pdf,.png,.zip,.rar'
              >
                <el-button type="primary" v-if="state.fileList.length < 30">上传附件</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    最多支持上传30个文件
                  </div>
                </template>
              </el-upload>
            </el-form-item> -->
            <el-form-item prop="dictValue" label="附件">
              <div class="list_file_upload">
                <div class="list_file_upload_action">
                  <div>
                    <el-upload
                      :action="constant.uploadUrl"
                      :headers="{ Authorization: cache.getToken() }"
                      :before-upload="beforeUpload"
                      :on-success="handleSuccess"
                      :show-file-list="false"
                    >
                      <el-button type="primary">上传</el-button>
                    </el-upload>
                  </div>
                  <div class="list_file_upload_desc" style="margin-left: 10px"></div>
                </div>
                <div class="list_file_upload_list">
                  <div
                    class="list_file_upload_list_item"
                    v-for="(item, index) in state.attachmentList"
                  >
                    <div
                      class="list_file_upload_list_item_text"
                      @click="onClickDownload(item)"
                    >
                      {{ item.name }}
                    </div>
                    <div class="list_file_upload_list_item_action">
                      <el-icon @click="onDeleteFile(index)"><DeleteFilled /></el-icon>
                    </div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>
<script setup lang="ts">
import { downloadFile } from "@/utils/tool";
import { DeleteFilled } from "@element-plus/icons-vue";
import { ElMessage, UploadProps } from "element-plus";
import { reactive, watch } from "vue";
import { IAttachmentItem, IMaterialItem } from "./purchase.vue";
import cache from "@/utils/cache";
import constant from "@/utils/constant";

interface IProps {
  materialList: IMaterialItem[];
  attachmentList: IAttachmentItem[];
}

const props = withDefaults(defineProps<IProps>(), {
  materialList: () => {
    return [];
  },
  attachmentList: () => {
    return [];
  },
});

watch(
  () => props.materialList,
  () => {
    state.materialList = props.materialList;
  }
);

watch(
  () => props.attachmentList,
  () => {
    state.attachmentList = props.attachmentList;
  }
);

interface IState {
  uploadUrl: string;
  materialList: IMaterialItem[];
  attachmentList: IAttachmentItem[];
}

const state = reactive<IState>({
  uploadUrl: "",
  materialList: [],
  attachmentList: [],
});

const emit = defineEmits<{
  (e: "on-delete", id: number): void;
  (e: "change-quantity", id: number, value: string): void;
  (e: "change-comment", id: number, value: string): void;
  (e: "upload-field", attachmentList: IAttachmentItem[]): void;
}>();

const onBlurQuantity = (event: any, rowIndex: number) => {
  let quantity = event.target.value;
  let strNum = quantity.toString();
  let decimalIndex = strNum.indexOf(".");

  if (decimalIndex !== -1) {
    // 如果有小数部分，则保留三位小数
    strNum = strNum.slice(0, decimalIndex + 4);
  }
  emit("change-quantity", rowIndex, strNum);
};

const onBlurComment = (event: any, rowIndex: number) => {
  emit("change-comment", rowIndex, event.target.value);
};

const onClickDelete = (rowIndex: number) => {
  emit("on-delete", rowIndex);
};

const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  console.log(file);
  let fileNameList = file.name.split(".");
  let fileType = fileNameList[fileNameList.length - 1];
  if (
    [
      "jpg",
      "png",
      "jpeg",
      "doc",
      "docx",
      "xls",
      "pdf",
      "xlsx",
      "gif",
      "zip",
      "rar",
      "docx",
      "xlsx",
    ].indexOf(fileType) == -1
  ) {
    ElMessage.error("请上传正确的文件格式");
    return false;
  }
  return true;
};

const handleSuccess: UploadProps["onSuccess"] = (res, file) => {
  if (res.code === 0) {
    let newAttachmentItem = {
      platform: res.data.platform,
      url: res.data.url,
      name: res.data.name,
      size: res.data.size,
    };
    let newAttachmentList = JSON.parse(JSON.stringify(state.attachmentList));
    newAttachmentList.push(newAttachmentItem);
    emit("upload-field", newAttachmentList);
  } else {
    ElMessage.error("上传失败：" + res.msg);
  }
};

const onDeleteFile = (index: number) => {
  let newAttachmentList = JSON.parse(JSON.stringify(state.attachmentList));
  newAttachmentList = newAttachmentList.filter((item: IAttachmentItem, ele: number) => {
    return index !== ele;
  });
  console.log(newAttachmentList);
  emit("upload-field", newAttachmentList);
};

const onClickDownload = (item: IAttachmentItem) => {
  downloadFile(item.url, item.name);
};
</script>
<style lang="scss" scoped>
.list {
  margin-top: 10px;
  &_content {
    margin-top: 16px;
  }
  &_file {
    display: flex;
    align-items: center;
    &_upload {
      &_list {
        &_item {
          display: flex;
          align-items: center;
          &_text {
            color: #409eff;
            margin-right: 10px;
            cursor: pointer;
          }
          &_action {
            color: #545252;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>
