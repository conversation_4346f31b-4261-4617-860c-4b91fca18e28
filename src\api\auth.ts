import service from '@/utils/request'

export const useCaptchaEnabledApi = () => {
	return service.get('/sys/auth/captcha/enabled')
}

export const useCaptchaApi = () => {
	return service.get('/sys/auth/captcha')
}

export const useSendCodeApi = (mobile: string) => {
	return service.post('/sys/auth/send/code?mobile=' + mobile)
}

export const useAccountLoginApi = (data: any) => {
	return service.post('/sys/auth/login', data)
}

export const useMobileLoginApi = (data: any) => {
	return service.post('/sys/auth/mobile', data)
}

export const useThirdLoginApi = (data: any) => {
	return service.post('/sys/auth/third', data)
}

export const useLogoutApi = () => {
	return service.post('/sys/auth/logout')
}

//用户注册
interface IAttachmentItem {
	name: string;
	url: string;
	size: number;
	platform: string;
}
export interface IUserRegister {
	subjectType: string
	companyName?: string
	username: string
	password: string
	contactName: string
	contactPhone: string
	email: string
	attachmentList: IAttachmentItem[],
  qualificationOne:any
}
export const userRegister = (reqData: IUserRegister) => {
	return service.post('/sys/auth/register', reqData)
}


//获取用户是否认证
export const getUserAuthentication = () => {
	return service.get('/sys/purchaserBidder/authentication')
}

// 注册下单
export const registerScanCodeBuyApi = (reqData: IUserRegister) => {
	return service.post('/work/record/registerScanCodeBuy', reqData)
}

//支付方式-字典
export const getByCodeApi = (code:any) => {
	return service.get(`/sys/dict/type/getByCode/${code}`)
}

//注册支付状态
export const getPayStatusApi = (goodsId:any) => {
	return service.get(`/pay/goodsOrder/getPayStatus/${goodsId}`)
}