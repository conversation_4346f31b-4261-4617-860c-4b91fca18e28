<template>
  <div class="other">
    <div class="other-title">其他附件</div>
    <div class="other-action">
      <el-form
        :model="state.dataForm"
        :rules="state.dataRules"
        label-position="top"
        ref="otherFileRef"
      >
        <el-form-item prop="otherFileList">
          <template #label>
            <div class="other-action-label">
              <div class="label-text">
                <span class="label-text-required">*</span>
                <span>平台服务费缴纳凭证 </span>
                <span class="label-text-desc"
                  >(请上传平台服务费缴纳凭证，支持 .jpg·png·pdf格式)</span
                >
              </div>
              <div class="label-action">
                <el-upload
                  :action="constant.uploadUrl"
                  :headers="{ Authorization: cache.getToken() }"
                  :before-upload="beforeUpload"
                  :on-success="handleSuccess"
                  :show-file-list="false"
                  v-if="props.action === 'edit'"
                >
                  <el-button type="primary">上传</el-button>
                </el-upload>
              </div>
            </div>
          </template>
          <div class="other-action-list">
            <el-table show-overflow-tooltip :data="state.dataForm.otherFileList" border>
              <el-table-column
                type="index"
                label="序号"
                header-align="center"
                align="center"
                width="70"
              ></el-table-column>
              <el-table-column
                prop="name"
                label="附件名称"
                header-align="center"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="action"
                label="操作"
                header-align="center"
                align="center"
              >
                <template #default="scope">
                  <!-- <el-button type="primary" link> 预览 </el-button> -->
                  <el-button type="primary" link @click="onClickDownload(scope.row)">
                    下载
                  </el-button>
                  <el-button type="primary" link @click="onClickPreview(scope.row)">
                    预览
                  </el-button>
                  <el-button
                    type="primary"
                    link
                    @click="onClickDelete(scope.$index)"
                    v-if="props.action === 'edit'"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
      </el-form>
      <el-image-viewer
        v-if="preview.show"
        @close="onClosePreviewImage"
        :url-list="preview.url"
      >
      </el-image-viewer>
    </div>
  </div>
</template>
<script setup lang="ts">
import service from "@/utils/request";
import { ElMessage, FormRules, UploadProps } from "element-plus";
import { nextTick, onMounted, reactive, ref, watch } from "vue";
import constant from "@/utils/constant";
import cache from "@/utils/cache";

interface IProps {
  action: string;
  fileList?: any;
}

const props = withDefaults(defineProps<IProps>(), {
  action: "edit",
});

const otherFileRef = ref();

const emit = defineEmits<{
  (e: "emit-ref", otherFileRef: any): void;
  (e: "on-change-value", type: string, field: string, value: any): void;
}>();

interface IDataForm {
  otherFileList: string;
}

interface IState {
  dataForm: IDataForm;
  dataRules: FormRules;
}

const validatorOtherFileList = (rule: any, value: any, callback: any) => {
  if ((state.dataForm.otherFileList ?? []).length === 0) {
    callback(new Error("请上传平台服务费缴纳凭证"));
  } else {
    callback();
  }
};

const state = reactive<IState>({
  dataForm: {
    otherFileList: "",
  },
  dataRules: {
    otherFileList: [
      {
        validator: validatorOtherFileList,
        trigger: "change",
      },
    ],
  },
});

watch(
  () => props.fileList,
  () => {
    let newFileList = JSON.parse(JSON.stringify(props.fileList));
    state.dataForm.otherFileList = newFileList;
  },
  { immediate: true }
);

onMounted(() => {
  nextTick(() => {
    emit("emit-ref", otherFileRef.value);
  });
});

const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  let fileNameList = file.name.split(".");
  let fileType = fileNameList[fileNameList.length - 1];
  if (["jpg", "png", "pdf", "jpeg"].indexOf(fileType) == -1) {
    ElMessage.error("请上传正确的文件格式");
    return false;
  }
  return true;
};

const handleSuccess: UploadProps["onSuccess"] = (res, file) => {
  if (res.code === 0) {
    let newAttachmentItem = {
      type: "FWFPZ",
      platform: res.data.platform,
      url: res.data.url,
      name: res.data.name,
      size: res.data.size,
    };
    let newDataList = JSON.parse(JSON.stringify(state.dataForm.otherFileList));
    newDataList.push(newAttachmentItem);

    emit("on-change-value", "otherFileList", "otherFileList", newDataList);
  } else {
    ElMessage.error("上传失败：" + res.msg);
  }
};

const onClickDownload = async (item: any) => {
  const response = await service.get(item.url, {
    responseType: "blob", // important
  });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", item.name);
  document.body.appendChild(link);
  link.click();
};

const onClickDelete = (index: number) => {
  let newDataList = JSON.parse(JSON.stringify(state.dataForm.otherFileList));
  newDataList = newDataList.filter((item: any, fileIndex: number) => {
    return index !== fileIndex;
  });
  emit("on-change-value", "otherFileList", "otherFileList", newDataList);
};

interface IPreview {
  show: boolean;
  url: string[];
}

const preview = reactive<IPreview>({
  show: false,
  url: [],
});

const onClickPreview = (item: any) => {
  let newUrl = [];
  newUrl.push(item.url);
  preview.url = newUrl;
  preview.show = true;
};

const onClosePreviewImage = () => {
  preview.show = false;
  preview.url = [];
};
</script>
<style lang="scss" scoped>
.other {
  &-title {
    padding-bottom: 12px;
    padding-left: 8px;
    border-bottom: 1px solid #e4e7ed;
    font-size: 16px;
    font-weight: 700;
    color: #333333;
  }
  &-action {
    margin-top: 16px;
    :deep(.el-form-item__label) {
      padding: 0px;
    }
    &-label {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .label-text {
        &-required {
          color: #f56c6c;
          margin-right: 6px;
          margin-left: 10px;
        }
        &-desc {
          color: #f56c6c;
          margin-left: 6px;
          font-size: 12px;
        }
      }
    }
    &-list {
      width: 100%;
    }
  }
}
</style>
