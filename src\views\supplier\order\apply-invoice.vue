<template>
  <el-dialog title="发票申请" width="900px" v-model="state.visible" @closed="onClosed">
    <div class="apply">
      <div class="apply-warn">
        <div class="apply-warn-title">注意：</div>
        <div class="apply-warn-content">1.供应商主体为企业时，默认开具专票。</div>
      </div>
      <div class="apply-empty" v-if="invoice?.dataList?.length == 0">
        <el-image :src="emptyPng" class="apply-empty-img"></el-image>
        <div class="apply-empty-text">暂无发票信息</div>
      </div>
      <div class="apply-content" v-else>
        <template v-for="item in invoice.dataList">
          <div
            :class="['item', `${state.checked == item.id ? 'item-checked' : ''}`]"
            @click="onCheckInvoice(item.id)"
          >
            <div class="item-item">
              <div class="item-item-label">发票抬头</div>
              <div class="item-item-value">{{ item.titleName }}</div>
            </div>
            <div class="item-item">
              <div class="item-item-label">发票类型</div>
              <div class="item-item-value">
                <span v-html="getDictLabelList('invoice_type1', item.invoiceType)"></span>
              </div>
            </div>
            <div class="item-item" v-if="(item?.invoiceCode ?? '') !== ''">
              <div class="item-item-label">纳税识别号</div>
              <div class="item-item-value">{{ item.invoiceCode }}</div>
            </div>
            <div class="checked">
              <div class="checked-bg"></div>
              <div class="checked-icon">
                <el-icon style="color: #fff" class="checked-icon"><Check /></el-icon>
              </div>
            </div>
          </div>
        </template>
      </div>
      <AddOrUpdate
        :show="action.show"
        :id="action.id"
        :companyName="action.companyName"
        :email="action.email"
        :mobile="action.mobile"
        @close="onCloseAction"
        @refresh="getDataList"
      ></AddOrUpdate>
    </div>
    <template #footer>
      <div class="apply-footer">
        <div class="apply-footer-left">
          <el-button type="primary" link @click="onClickAction()">新增开票信息</el-button>
        </div>
        <div class="apply-footer-right">
          <el-button @click="onClickClose">关闭</el-button>
          <el-button type="primary" @click="onClickSubmit()">确定</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { useUserInfoApi } from "@/api/sys/user";
import emptyPng from "@/assets/empty.png";
import { onMounted, reactive, watch } from "vue";
import AddOrUpdate from "../invoice/add-or-update.vue";
import { Check } from "@element-plus/icons-vue";
import { IHooksOptions } from "@/hooks/interface";
import { useCrud } from "@/hooks";
import { getDictLabelList } from "@/utils/tool";
import { ElMessage } from "element-plus";
import { applyInvoice } from "@/api/supplier/order";

interface IProps {
  show: boolean;
  id: string;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  id: "",
});

interface IState {
  visible: boolean;
  checked: string;
}

const state = reactive<IState>({
  visible: false,
  checked: "",
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      state.visible = props.show;
    }
  }
);

const emit = defineEmits<{
  (e: "submit"): void;
  (e: "close"): void;
}>();

const invoice = reactive<IHooksOptions>({
  pageSize: 999,
  dataListUrl: "/work/info/page",
});

const { getDataList } = useCrud(invoice);

const onCheckInvoice = (id: string) => {
  state.checked = id;
};

const onClickSubmit = () => {
  if (state.checked == "") {
    ElMessage.warning("请选择开票信息");
    return;
  }
  let reqData = {
    id: props.id,
    invoiceId: state.checked,
  };
  applyInvoice(reqData).then((res) => {
    if (res.code == 0) {
      state.visible = false;
      ElMessage.success("提交成功");
      emit("submit");
    }
  });
};

const onClickClose = () => {
  state.visible = false;
};

const onClosed = () => {
  state.checked = "";
  emit("close");
};

//#region 新增开票信息
onMounted(() => {
  getUserDetail();
});
interface IAction {
  show: boolean;
  id: string;
  companyName: string;
  email: string;
  mobile: string;
}

const action = reactive<IAction>({
  show: false,
  id: "",
  companyName: "",
  email: "",
  mobile: "",
});

const onClickAction = () => {
  action.show = true;
};

const onCloseAction = () => {
  action.show = false;
  action.id = "";
};

const getUserDetail = () => {
  useUserInfoApi().then((res) => {
    if (res.code == 0) {
      action.companyName = res.data?.userDetailsVO?.companyName ?? "";
      action.email = res.data?.userDetailsVO?.email ?? "";
      action.mobile = res.data?.userDetailsVO?.contactPhone ?? "";
    }
  });
};

//#endregion
</script>
<style lang="scss" scoped>
.apply {
  &-warn {
    border: 1px solid rgba(45, 120, 244, 1);
    background-color: rgba(233, 241, 253, 1);
    padding: 6px 10px;
  }
  &-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
    margin-bottom: 60px;
    &-img {
      width: 200px;
      height: 200px;
    }
    &-text {
      margin-top: 6px;
      font-size: 14px;
      color: #999999;
    }
  }
  &-content {
    margin-top: 20px;
    margin-bottom: 40px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    .item {
      border: 1px solid rgba(221, 221, 221, 1);
      padding: 10px;
      width: 400px;
      height: 80px;
      margin-bottom: 20px;
      cursor: pointer;
      position: relative;
      &-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        &-label {
          width: 100px;
          font-size: 14px;
          color: #666666;
          display: flex;
          justify-content: flex-end;
          margin-right: 6px;
        }
        &-value {
          font-size: 14px;
          color: #333333;
        }
      }
      &-item:last-child {
        margin-bottom: 0;
      }
      .checked {
        display: none;
      }
    }
    .item-checked {
      border: 1px solid rgba(45, 120, 244, 1);
      .checked {
        display: block;
        position: absolute;
        right: 0;
        top: 0;
        &-bg {
          width: 0;
          height: 0;
          border-bottom: 24px solid transparent;
          border-right: 24px solid rgba(45, 120, 244, 1);
        }
        &-icon {
          position: absolute;
          right: 0;
          top: 0;
        }
      }
    }
  }
  &-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
