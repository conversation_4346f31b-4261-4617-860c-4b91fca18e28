<template>
  <el-dialog
    v-model="state.show"
    :title="!props.id ? '新增' : '修改'"
    :width="800"
    :close-on-click-modal="false"
    draggable
    @close="onClickClose"
  >
    <el-form
      ref="dataFormRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="80px"
      @keyup.enter="onClickSubmit"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="categoryId" label="物料分类">
            <el-tree-select
              v-model="state.dataForm.categoryId"
              :data="state.categoryList"
              value-key="id"
              placeholder="请选择物料分类"
              check-strictly
              :render-after-expand="false"
              :props="{ label: 'name', children: 'children' }"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="materialNo" label="物料编码">
            <el-input
              v-model="state.dataForm.materialNo"
              placeholder="物料编码"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="materialName" label="物料名称">
            <el-input
              v-model="state.dataForm.materialName"
              placeholder="物料名称"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="materialType" label="型号">
            <el-input v-model="state.dataForm.materialType" placeholder="型号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="materialSpec" label="规格">
            <el-input v-model="state.dataForm.materialSpec" placeholder="规格"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="materialUnit" label="计量单位">
            <el-input
              v-model="state.dataForm.materialUnit"
              placeholder="计量单位"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="comment" label="备注">
            <el-input
              v-model="state.dataForm.comment"
              placeholder="备注"
              :rows="3"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="onClickClose">取消</el-button>
      <el-button type="primary" @click="onClickSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage, FormRules } from "element-plus";
import { reactive, ref, watch, onMounted, nextTick } from "vue";
import { getCategoryList } from "@/api/material/category";
import {
  IActionMaterial,
  addCategory,
  updateCategory,
  getMaterialInfo,
} from "@/api/material/manage";

const dataFormRef = ref();

interface IProps {
  show: boolean;
  id?: number;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
});

interface IDataForm {
  categoryId?: number;
  materialNo: string;
  materialName: string;
  materialType: string;
  materialSpec: string;
  materialUnit: string;
  comment: string;
}

interface ICategoryItem {
  id: number;
  name: string;
  pid?: number;
  parentName?: string;
  children: ICategoryItem[];
  disabled: boolean;
}

interface IState {
  show: boolean;
  dataForm: IDataForm;
  dataRules: FormRules;
  categoryList: ICategoryItem[];
}

const state = reactive<IState>({
  show: false,
  dataForm: {
    categoryId: void 0,
    materialNo: "",
    materialName: "",
    materialType: "",
    materialSpec: "",
    materialUnit: "",
    comment: "",
  },
  dataRules: {
    categoryId: [{ required: true, message: "请选择物料分类", trigger: "change" }],
    materialNo: [{ required: true, message: "请输入物料编码", trigger: "blur" }],
    materialName: [{ required: true, message: "请输入物料名称", trigger: "blur" }],
    materialType: [{ required: true, message: "请输入型号", trigger: "blur" }],
    materialSpec: [{ required: true, message: "请输入规格", trigger: "blur" }],
    materialUnit: [{ required: true, message: "请输入计量单位", trigger: "blur" }],
  },
  categoryList: [],
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      state.show = props.show;
      nextTick(() => {
        dataFormRef.value.resetFields();
      });
      if (props.id) {
        getMaterialInfo(props.id).then((res: any) => {
          if (res.code === 0) {
            let newDataForm = {
              categoryId: res.data.categoryId,
              materialNo: res.data.materialNo,
              materialName: res.data.materialName,
              materialType: res.data.materialType,
              materialSpec: res.data.materialSpec,
              materialUnit: res.data.materialUnit,
              comment: res.data.comment,
            };
            state.dataForm = newDataForm;
          }
        });
      } else {
        let newDataForm = {
          categoryId: void 0,
          materialNo: "",
          materialName: "",
          materialType: "",
          materialSpec: "",
          materialUnit: "",
          comment: "",
        };
        state.dataForm = newDataForm;
      }
    }
  }
);

const emit = defineEmits<{
  (e: "on-close"): void;
  (e: "on-submit"): void;
}>();

onMounted(() => {
  GetCategoryList();
});

const GetCategoryList = () => {
  getCategoryList().then((res) => {
    console.log(res);
    state.categoryList = res.data.map((item: ICategoryItem) => {
      return {
        id: item.id,
        name: item.name,
        disabled: true,
        children: item.children.map((ele: ICategoryItem) => {
          return {
            id: ele.id,
            name: ele.name,
            disabled: false,
          };
        }),
      };
    });
  });
};

const onClickSubmit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      let reqData: IActionMaterial = {
        id: props.id,
        categoryId: state.dataForm.categoryId,
        materialNo: state.dataForm.materialNo,
        materialName: state.dataForm.materialName,
        materialType: state.dataForm.materialType,
        materialSpec: state.dataForm.materialSpec,
        materialUnit: state.dataForm.materialUnit,
        comment: state.dataForm.comment,
      };
      (props.id ? updateCategory(reqData) : addCategory(reqData)).then((res: any) => {
        if (res.code === 0) {
          ElMessage.success("操作成功!");
          state.show = false;
          emit("on-submit");
        } else {
          ElMessage.error(res.msg);
        }
      });
    }
  });
};

const onClickClose = () => {
  state.show = false;
  emit("on-close");
};
</script>
