<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-01 10:19:02
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-06-03 15:54:23
-->
<template>
  <el-card>
    <div class="action">
      <div class="action_base">
        <div class="info">
          <div class="action_title">
            <ContentTitle title="查看公告" v-if="state.nowType === 'look'"></ContentTitle>
            <ContentTitle
              title="编辑公告"
              v-else-if="state.nowType === 'edit'"
            ></ContentTitle>
            <ContentTitle title="发布公告" v-else></ContentTitle>
          </div>
          <el-form
            ref="baseInfoRef"
            :model="state.dataForm"
            :rules="state.dataRules"
            label-width="170px"
            class="normalform"
          >
            <el-form-item prop="title" label="公告标题">
              <el-input
                v-model="state.dataForm.title"
                placeholder="请输入"
                :disabled="state.nowType === 'look'"
              ></el-input>
            </el-form-item>
            <el-form-item prop="creatorFw" label="可见范围">
              <el-input
                readonly
                v-model="state.dataForm.creatorFw"
                @click="showFwClick"
                placeholder="请选择"
                :disabled="state.nowType === 'look'"
              ></el-input>
            </el-form-item>
            <el-form-item prop="dictValue" label="附件">
              <el-upload
                class="upload"
                v-model:file-list="state.attachmentList"
                :headers="{ Authorization: cache.getToken() }"
                :action="constant.uploadUrl"
                :before-upload="beforeUpload"
                :on-success="handleSuccess"
                :on-remove="handleRemove"
                accept=".jpeg,.jpg,.doc,.docx,.xls,.xlsx,.pdf,.png,.zip,.rar"
              >
                <el-button type="primary" :disabled="state.nowType === 'look'"
                  >上传附件</el-button
                >
                <template #tip>
                  <div class="el-upload__tip">最多支持上传30个文件</div>
                </template>
              </el-upload>
            </el-form-item>
            <el-form-item label="公告内容" prop="remark">
              <div v-html="state.dataForm.remark" v-if="state.nowType === 'look'"></div>
              <WangEditor v-model="state.dataForm.remark" v-else></WangEditor>
            </el-form-item>
            <el-form-item label="">
              <el-checkbox
                v-model="state.dataForm.isTop"
                :true-label="1"
                :false-label="0"
                :disabled="state.nowType === 'look'"
                >设为置顶</el-checkbox
              >
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="action_btn">
        <el-button
          type="primary"
          v-if="state.nowType !== 'look'"
          @click="saveSubmit(true, 1)"
          >发布</el-button
        >
        <el-button
          type="primary"
          v-if="state.nowType !== 'look'"
          plain
          @click="saveSubmit(true, 0)"
          >暂存</el-button
        >
        <el-button @click="closentab" v-if="state.nowType === 'look'">关闭</el-button>
        <el-button @click="closentab" v-else>取消</el-button>
      </div>
    </div>
  </el-card>
  <select-user-tree
    ref="selectUserTreeRef"
    @select-user="getSelectUser"
  ></select-user-tree>
</template>
<script setup lang="ts">
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import { FormRules, UploadProps, ElMessage } from "element-plus";
import WangEditor from "@/components/wang-editor/index.vue";
import selectUserTree from "./components/selectUserTree.vue";
import { closeTab } from "@/utils/tabs";
import constant from "@/utils/constant";
import cache from "@/utils/cache";
import { moreUseSave, moreUseUpdate, moreUseGetInfo } from "@/api/pnotice";
import { useRoute, useRouter } from "vue-router";
import { provide, reactive, ref, onMounted } from "vue";
import { downloadFile } from "@/utils/tool";

const route = useRoute();
const router = useRouter();
const baseInfoRef = ref();
const state = reactive({
  dataForm: {
    title: "",
    creatorFw: "",
    makeType: "",
    remark: "",
    isTop: 0,
    attachmentList: [],
  },
  dataRules: {
    title: [{ required: true, message: "必填项", trigger: "blur" }],
    creatorFw: [{ required: true, message: "必填项", trigger: "change" }],
    remark: [{ required: true, message: "必填项", trigger: "blur" }],
  },
  saveFlag: false, // false 暂存 true 提交
  dataListLoading: false,
  attachmentList: [],
  userList: [],
  nowId: "",
  nowType: "",
});
provide("dataForm", state.dataForm);
provide("dataRules", state.dataRules);
export interface IAttachmentItem {
  name: string;
  url: string;
  platform: string;
  size: number;
}
export interface IMaterialItem {
  id: number;
  materialNo: string;
  materialName: string;
  materialSpec: string;
  materialType: string;
  materialUnit: string;
  materialQuantity?: number;
  surplusQuantity?: number;
  comment: string;
}
const selectUserTreeRef = ref<InstanceType<typeof selectUserTree>>();
onMounted(() => {
  if (route.query.id) {
    state.nowId = route.query.id;
    state.nowType = route.query.type;
    getDetail();
  }
});
// 点击可见范围
const showFwClick = () => {
  selectUserTreeRef.value?.init(state.userList);
};
// 可见范围人员
const getSelectUser = (arr: any) => {
  let userName = "";
  for (let item of arr) {
    userName += item.name + ",";
  }
  state.dataForm.creatorFw = userName;
  state.userList = arr;
};
// 返回
const closentab = () => {
  closeTab(router, route);
};
// 提交 暂存
const saveSubmit = (saveFlag: any, status: any) => {
  for (let item of state.userList) {
    if (item.className === state.dataForm.className) {
      state.dataForm.classId = item.id;
    }
  }
  let oaBulletinPersonVOS = [];
  for (let item of state.userList) {
    let obj = {
      status: 0,
      orgId: item.pid,
      orgName: item.parentName,
      sendUserId: item.id,
      sendUserName: item.name,
    };
    oaBulletinPersonVOS.push(obj);
  }
  let obj = {
    ...state.dataForm,
    oaBulletinPersonVOS,
    status,
  };
  baseInfoRef.value.validate((valid: any) => {
    if (!valid) return;
    if (state.dataForm.remark === "<p><br></p>") {
      return ElMessage.error("请填写公告内容！");
    }
    if (state.nowType === "") {
      moreUseSave(obj).then((res) => {
        if (res.code == 0) {
          ElMessage.success({
            message: "保存成功",
            duration: 500,
            onClose: () => {
              closentab();
            },
          });
        }
      });
    } else {
      moreUseUpdate(obj).then((res) => {
        if (res.code == 0) {
          ElMessage.success({
            message: "保存成功",
            duration: 500,
            onClose: () => {
              closentab();
            },
          });
        }
      });
    }
  });
};
// 查看
const getDetail = () => {
  moreUseGetInfo(state.nowId).then((res) => {
    if (res.code == 0) {
      Object.assign(state.dataForm, res.data);
      if (res.data.attachmentList) {
        state.attachmentList = res.data.attachmentList;
      }
      let userName = "";
      for (let item of res.data.oaBulletinPersonVOS) {
        userName += item.sendUserName + ",";
        (item.pid = item.orgId),
          (item.parentName = item.orgName),
          (item.id = item.sendUserId),
          (item.name = item.sendUserName);
      }
      state.userList = res.data.oaBulletinPersonVOS;
      state.dataForm.creatorFw = userName;
    }
  });
};

const handleSuccess = (res, file) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  state.dataForm.attachmentList.push(res.data);
};
const beforeUpload = (file) => {
  let fileType=""
  if(file.name){
    let nameArray=file.name.split(".");
    fileType=nameArray[nameArray.length-1];
  }
  if (
    [
      "jpg",
      "png",
      "jpeg",
      "doc",
      "docx",
      "xls",
      "pdf",
      "xlsx",
      "gif",
      "zip",
      "rar",
    ].indexOf(fileType) == -1
  ) {
    ElMessage.error("格式不支持");
    return false;
  } else if (file.size / 1024 / 1024 > 10) {
    ElMessage.error("文件大小不超过10M");
    return false;
  }
  return true;
};
const handleRemove = (file, files) => {
  let uploadList = [];
  for (let i of files) {
    if ("response" in i) {
      uploadList.push(i.response.data);
    } else {
      uploadList.push(i);
    }
  }
  state.dataForm.attachmentList = uploadList;
  // Object.assign(state.dataForm.attachmentList, uploadList);
};
const onClickDownload = (item: IAttachmentItem) => {
  downloadFile(item.url, item.name);
};
</script>
<style lang="scss" scoped>
.action {
  .info {
    width: 100%;
    margin-top: 10px;
  }
  .action_title {
    margin-bottom: 10px;
  }
}
</style>
