<template>
  <div class="statistics">
    <div class="statistics-title">
      <HomeTitle icon="icon-tongji1" title="采购计划统计">
        <template #right>
          <el-select v-model="state.year" style="width: 120px" @change="onChangeYear">
            <el-option
              v-for="item in state.yearList"
              :key="item"
              :label="item"
              :value="item"
            ></el-option>
          </el-select>
        </template>
      </HomeTitle>
    </div>
    <div class="statistics-desc">
      <div class="statistics-desc-number">
        <div class="number-name">采购计划</div>
        <div class="number-value">
          <div class="number-value-label">计划总数</div>
          <div class="number-value-number">{{ state.curYearPackNum }}</div>
        </div>
        <div class="number-ratio">
          <div class="number-ratio-label">环比</div>
          <div class="number-ratio-number">
            {{ Math.abs(state.numProcent) }}%
            <svgIcon
              icon="icon-icon_huanbishangsheng"
              class-name="svg-size"
              v-if="state.numProcent >= 0"
            ></svgIcon>
            <svgIcon
              icon="icon-icon_huanbixiajiang"
              class-name="svg-size"
              v-else
            ></svgIcon>
          </div>
        </div>
      </div>
      <div class="statistics-desc-money">
        <div class="money-name">采购计划总额</div>
        <div class="money-value">
          <div class="money-value-label">中标总额</div>
          <div class="money-value-number">{{ state.bidAmountCur }}</div>
        </div>
        <div class="money-ratio">
          <div class="money-ratio-label">环比</div>
          <div class="money-ratio-number">
            {{ Math.abs(state.amountProcent) }}%
            <svgIcon
              icon="icon-icon_huanbishangsheng"
              class-name="svg-size"
              v-if="state.amountProcent >= 0"
            ></svgIcon>
            <svgIcon
              icon="icon-icon_huanbixiajiang"
              class-name="svg-size"
              v-else
            ></svgIcon>
          </div>
        </div>
      </div>
    </div>
    <div class="statistics-content">
      <div class="statistics-content-chart" id="chart"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import HomeTitle from "./HomeTitle.vue";
import * as echarts from "echarts/core";
import {
  ToolboxComponent,
  ToolboxComponentOption,
  TooltipComponent,
  TooltipComponentOption,
  GridComponent,
  GridComponentOption,
  LegendComponent,
  LegendComponentOption,
} from "echarts/components";
import { BarChart, BarSeriesOption, LineChart, LineSeriesOption } from "echarts/charts";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
import { onMounted, onUnmounted, reactive, shallowRef } from "vue";
import dayjs from "dayjs";
import { getPlanStatistics } from "@/api/home";
import svgIcon from "@/components/svg-icon";
echarts.use([
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
]);

type EChartsOption = echarts.ComposeOption<
  | ToolboxComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | LegendComponentOption
  | BarSeriesOption
  | LineSeriesOption
>;

interface IState {
  year: string;
  yearList: string[];
  amountProcent: number;
  bidAmountCur: number;
  curYearPackNum: number;
  numProcent: number;
}

const state = reactive<IState>({
  year: dayjs().format("YYYY"),
  yearList: [dayjs().format("YYYY")],
  amountProcent: 0,
  bidAmountCur: 0,
  curYearPackNum: 0,
  numProcent: 0,
});

const barEcharts = shallowRef(null);

onMounted(() => {
  const chartDom = document.getElementById("chart");
  barEcharts.value = echarts.init(chartDom);
  let currentYear = Number(dayjs().format("YYYY"));
  let newYearList = [];
  for (let i = 10; i >= 0; i--) {
    newYearList.push(currentYear - i + "");
  }
  state.yearList = newYearList;
  GetPlanStatistics();
});

const GetPlanStatistics = () => {
  getPlanStatistics(state.year).then((res: any) => {
    if (res.code === 0) {
      state.amountProcent = res.data.amountProcent;
      state.bidAmountCur = res.data.bidAmountCur;
      state.curYearPackNum = res.data.curYearPackNum;
      state.numProcent = res.data.numProcent;
      initEcharts(res.data.numYearMonthResList, res.data.amountYearMonthResList);
    }
  });
};

const initEcharts = (numYearMonthResList: number[], amountYearMonthResList: number[]) => {
  let option: EChartsOption = {
    type: "category",
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      bottom: 10,
    },
    xAxis: [
      {
        type: "category",
        data: [
          "1月",
          "2月",
          "3月",
          "4月",
          "5月",
          "6月",
          "7月",
          "8月",
          "9月",
          "10月",
          "11月",
          "12月",
        ],
        axisPointer: {
          type: "shadow",
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "采购计划数",
      },
      {
        type: "value",
        name: "中标金额",
      },
    ],
    series: [
      {
        name: "采购计划数",
        type: "bar",
        tooltip: {
          valueFormatter: function (value) {
            return (value as number) + "";
          },
        },
        itemStyle: {
          color: "#399FFD",
        },
        data: numYearMonthResList,
      },
      {
        name: "中标金额",
        type: "line",
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return (value as number) + "";
          },
        },
        itemStyle: {
          color: "#00C29A",
        },
        data: amountYearMonthResList,
      },
    ],
  };
  option && barEcharts.value.setOption(option);
};

const onChangeYear = () => {
  GetPlanStatistics();
};

onUnmounted(() => {
  if (barEcharts.value) {
    barEcharts.value.dispose();
  }
});
</script>
<style lang="scss" scoped>
.statistics {
  background-color: #ffffff;
  padding: 16px 10px;
  border-radius: 4px;
  &-desc {
    display: flex;
    align-items: center;
    margin: 0 32px;
    margin-top: 16px;
    &-number {
      flex: 1;
      display: flex;
      align-items: center;
      position: relative;
      background-image: url("@/assets/image/home/<USER>");
      background-size: 100% 100%;
      height: 40px;
      margin-right: 16px;
      padding: 0 16px;
      .number-name {
        position: relative;
        z-index: 2;
        font-size: 12px;
        color: rgb(26, 144, 254);
      }
      .number-value {
        display: flex;
        align-items: center;
        position: relative;
        z-index: 2;
        margin-left: 16px;
        &-label {
          font-size: 12px;
          color: #333333;
        }
        &-number {
          font-weight: 700;
          font-size: 16px;
          color: #333333;
          margin-left: 8px;
        }
      }
      .number-ratio {
        display: flex;
        align-items: center;
        position: relative;
        z-index: 2;
        margin-left: 16px;
        &-label {
          font-size: 12px;
          color: #333333;
        }
        &-number {
          margin-left: 8px;
          font-weight: 700;
          font-size: 16px;
          color: #333333;
        }
      }
    }
    &-money {
      flex: 1;
      display: flex;
      align-items: center;
      position: relative;
      background-image: url("@/assets/image/home/<USER>");
      background-size: 100% 100%;
      height: 40px;
      padding: 0 16px;
      .money-name {
        position: relative;
        z-index: 2;
        font-size: 12px;
        color: #00c29a;
      }
      .money-value {
        display: flex;
        align-items: center;
        margin-left: 16px;
        &-label {
          font-size: 12px;
          color: #333333;
        }
        &-number {
          font-weight: 700;
          font-size: 16px;
          color: #333333;
          margin-left: 8px;
        }
      }
      .money-ratio {
        display: flex;
        align-items: center;
        margin-left: 16px;
        &-label {
          font-size: 12px;
          color: #333333;
        }
        &-number {
          margin-left: 8px;
          font-weight: 700;
          font-size: 16px;
          color: #333333;
        }
      }
    }
  }
  &-content {
    &-chart {
      height: 400px;
    }
  }
}
</style>
