<template>
  <el-card>
    <div class="invoice">
      <div class="invoice-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="供应商名称"
              clearable
              v-model="state.queryForm.companyName"
              style="width: 250px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <fast-select
              v-model="state.queryForm.titleType"
              dict-type="title_type"
              placeholder="抬头类型"
              clearable
              style="width: 250px"
            >
            </fast-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="payTime"
              type="daterange"
              clearable
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="onChangeDate"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onClickReset()">重置</el-button>
            <el-button @click="onClickExport()" type="primary" :loading="exportLoading">
              导出
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="invoice-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="companyName"
            label="供应商名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="contactName"
            label="联系人"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="titleType"
            label="抬头类型"
            header-align="center"
            align="center"
            dict-type="title_type"
          ></fast-table-column>
          <el-table-column
            prop="titleName"
            label="发票抬头"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="invoiceCode"
            label="纳税识别号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="linkTel"
            label="联系电话"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="emailCode"
            label="电子邮箱"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="isInvoice"
            label="开票状态"
            header-align="center"
            align="center"
            dict-type="is_invoice"
          ></fast-table-column>
          <el-table-column
            prop="applyInvoiceTime"
            label="申请开票时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="200"
          >
            <template #default="scope">
              <el-button type="primary" link @click="onClickDetail(scope.row.id)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="invoice-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <Detail
        :show="detail.show"
        :id="detail.id"
        from="purchase-invoice"
        @on-close="onCloseDetail"
      ></Detail>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { reactive, ref } from "vue";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import dayjs from "dayjs";
import Detail from "../../../supplier/order/detail.vue";
import { exportOrderData } from "@/api/supplier/order";

const payTime = ref([]);
const exportLoading = ref(false);

const state = reactive<IHooksOptions>({
  queryForm: {
    isInvoice: "1",
    queryType: "1",
    companyName: "",
    titleType: "",
    payType: "",
    applyInvoiceTimeStart: "",
    applyInvoiceTimeEnd: "",
  },
  dataListUrl: "/work/record/page",
});

const { getDataList, sizeChangeHandle, currentChangeHandle, deleteHandle } = useCrud(
  state
);

const onClickReset = () => {
  state.queryForm.companyName = "";
  state.queryForm.titleType = "";
  state.queryForm.payType = "";
  state.queryForm.applyInvoiceTimeStart = "";
  state.queryForm.applyInvoiceTimeEnd = "";
  payTime.value = [];
  getDataList();
};

const onChangeDate = (value: any) => {
  if (value && value.length === 2) {
    state.queryForm.applyInvoiceTimeStart = dayjs(value[0]).format("YYYY-MM-DD");
    state.queryForm.applyInvoiceTimeEnd = dayjs(value[1]).format("YYYY-MM-DD");
  } else {
    state.queryForm.applyInvoiceTimeStart = "";
    state.queryForm.applyInvoiceTimeEnd = "";
  }
};

//#region 查看详情
interface IDetail {
  show: boolean;
  id: string;
}
const detail = reactive<IDetail>({
  show: false,
  id: "",
});

const onClickDetail = (id: string) => {
  detail.id = id;
  detail.show = true;
};

const onCloseDetail = () => {
  detail.show = false;
  detail.id = "";
};

//#endregion

const onClickExport = () => {
  exportLoading.value = true;
  exportOrderData(state.queryForm)
    .then((res) => {
      exportLoading.value = false;
      const url = window.URL.createObjectURL(new Blob([res.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "发票申请记录.xlsx");
      document.body.appendChild(link);
      link.click();
    })
    .catch(() => {
      exportLoading.value = false;
    });
};
</script>
