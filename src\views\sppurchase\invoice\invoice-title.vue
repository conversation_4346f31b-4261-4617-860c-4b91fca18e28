<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-15 09:13:55
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-15 17:33:00
-->
<template>
  <el-form :inline="true" :model="state.queryForm">
    <el-form-item>
      <el-button :icon="Plus" type="primary" @click="addOrUpdateHandle()">新增</el-button>
    </el-form-item>
    <el-tag type="warning">增值税普通发票和专用发票抬头只能各设置一个</el-tag>
  </el-form>
  <el-table
    v-loading="state.dataListLoading"
    :data="state.dataList"
    border
    style="width: 100%"
    stripe
  >
    <el-table-column
      type="index"
      label="序号"
      header-align="center"
      align="center"
      width="60"
    ></el-table-column>
    <el-table-column
      prop="invoiceHeader"
      label="发票抬头"
      header-align="center"
      align="center"
    ></el-table-column>
    <el-table-column
      prop="creditCode"
      label="税号"
      header-align="center"
      align="center"
    ></el-table-column>
    <el-table-column
      prop="invoiceType"
      label="发票类型"
      header-align="center"
      align="center"
    >
      <template #default="scope">
        <span>{{ scope.row.invoiceType === "00" ? "普票" : "专票" }}</span>
      </template>
    </el-table-column>
    <el-table-column
      prop="createTime"
      label="创建日期"
      header-align="center"
      align="center"
    ></el-table-column>
    <el-table-column prop="operation" label="操作" header-align="center" align="center">
      <template #default="scope">
        <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)"
          >编辑</el-button
        >
        <el-button type="primary" link @click="deleteBatchHandle(scope.row.id)"
          >删除</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    :current-page="state.pageNo"
    :page-sizes="state.pageSizes"
    :page-size="state.pageSize"
    :total="state.total"
    layout="total, prev, pager, next"
    @size-change="sizeChangeHandle"
    @current-change="currentChangeHandle"
  >
  </el-pagination>

  <!-- 新增 / 修改 -->
  <title-addorupdate
    ref="titleAddorupdateRef"
    v-if="state.titleAddorupdateShow"
    @refresh-data-list="refreshData"
  ></title-addorupdate>
</template>

<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { Plus } from "@element-plus/icons-vue";
import { nextTick, reactive, ref } from "vue";
import titleAddorupdate from "./title-addorupdate.vue";

const state: IHooksOptions = reactive({
  dataListUrl: "/sys/invoiceHeader/page",
  deleteUrl: "/sys/invoiceHeader",
  queryForm: {},
  titleAddorupdateShow: false,
});
const titleAddorupdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  state.titleAddorupdateShow = true;
  nextTick(() => {
    titleAddorupdateRef.value.init(id);
  });
};

const refreshData = () => {
  getDataList();
};
const { getDataList, sizeChangeHandle, currentChangeHandle, deleteBatchHandle } = useCrud(
  state
);
</script>
