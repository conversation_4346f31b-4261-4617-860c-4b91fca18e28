<template>
  <el-dialog
    v-model="dialog.show"
    title="查看历史报价"
    @close="onClickClose"
    width="1200px"
  >
    <div class="history">
      <el-tabs v-model="dialog.activeName" type="card" @tab-change="onTabChange">
        <el-tab-pane
          v-for="item in dialog.tabList"
          :key="item.name"
          :label="item.label"
          :name="item.name"
        >
        </el-tab-pane>
      </el-tabs>
      <div class="history-content">
        <div class="history-content-table">
          <el-table
            v-loading="state.dataListLoading"
            :data="state.dataList"
            border
            :max-height="450"
          >
            <el-table-column
              type="index"
              label="序号"
              header-align="center"
              align="center"
              width="70"
            ></el-table-column>
            <el-table-column
              prop="bidderName"
              label="供应商"
              header-align="center"
              align="center"
            >
              <template #default="{ row, $index }">
                <div class="bidder-name">
                  <span style="margin-right: 10px">{{ row?.bidderName }}</span>
                  <el-tag
                    type="success"
                    v-if="$index === 0 && state.dataList && state.dataList?.length > 1"
                    >最低</el-tag
                  >
                  <el-tag
                    type="danger"
                    v-if="
                      state.dataList &&
                      $index === state.dataList?.length - 1 &&
                      state.dataList?.length > 1
                    "
                    >最高</el-tag
                  >
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="quotationPrice"
              label="总报价（元）"
              dict-type="user_gender"
            ></el-table-column>
            <el-table-column
              prop="deliveryTime"
              label="交货时间"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="createTime"
              label="报价时间"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="attachmentList"
              label="附件"
              header-align="center"
              align="center"
            >
              <template #default="{ row }">
                <div class="file-list">
                  <div class="file-list-item" v-for="item in row?.attachmentList">
                    <div class="file-list-item-text" @click="onClickDownload(item)">
                      {{ item.name }}
                    </div>
                    <div class="file-list-item-action">
                      <el-icon class="action-icon" @click="onClickDownload(item)">
                        <Download />
                      </el-icon>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>

            <el-table-column
              prop="comment"
              label="操作"
              header-align="center"
              align="center"
            >
              <template #default="{ row }">
                <div>
                  <el-button type="primary" link @click="onClickDetail(row)"
                    >查看报价明细</el-button
                  >
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="history-content-page">
          <el-pagination
            :current-page="state.pageNo"
            :page-sizes="state.pageSizes"
            :page-size="state.pageSize"
            :total="state.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="sizeChangeHandle"
            @current-change="currentChangeHandle"
          >
          </el-pagination>
        </div>
      </div>
      <QuotationDetail
        :show="detail.show"
        :id="detail.id"
        :bidderName="detail.bidderName"
        :quotationPrice="detail.quotationPrice"
        @on-close="onCloseDetail"
      ></QuotationDetail>
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, watch } from "vue";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { Download } from "@element-plus/icons-vue";
import service from "@/utils/request";
import QuotationDetail from "./QuotationDetail.vue";

//#region IProps
interface IProps {
  show: boolean;
  packageId?: number;
  roundNo?: number;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  roundNo: 1,
});

//#endregion

interface ITabItem {
  label: string;
  name: number;
}

interface IDialog {
  show: boolean;
  activeName: number;
  tabList: ITabItem[];
}

const dialog = reactive<IDialog>({
  show: false,
  activeName: 1,
  tabList: [],
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      dialog.show = true;
      let newTabList = [];
      for (let i = 1; i <= props.roundNo; i++) {
        newTabList.push({
          label: `第${i}轮`,
          name: i,
        });
      }
      dialog.tabList = newTabList;
      if (props.packageId) {
        state.queryForm.roundNum = 1;
        state.queryForm.packageId = props.packageId;
        state.page = 1;
        getDataList();
      }
    }
  }
);

const state = reactive<IHooksOptions>({
  createdIsNeed: false,
  queryForm: {
    packageId: void 0,
    roundNum: 1,
  },
  dataListUrl: "/purchase/quotation/page",
});

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

const onTabChange = () => {
  state.page = 1;
  state.queryForm.roundNum = dialog.activeName;
  getDataList();
};

const emit = defineEmits<{
  (e: "on-close"): void;
}>();

const onClickClose = () => {
  dialog.show = false;
  emit("on-close");
};

//#region 报价明细

interface IDetail {
  show: boolean;
  id?: number;
  bidderName: string;
  quotationPrice?: number;
}

const detail = reactive<IDetail>({
  show: false,
  id: void 0,
  bidderName: "",
  quotationPrice: 0,
});

const onClickDetail = (row: any) => {
  detail.id = row.id;
  detail.bidderName = row.bidderName;
  detail.quotationPrice = row.quotationPrice;
  detail.show = true;
};

const onCloseDetail = () => {
  detail.show = false;
  detail.id = void 0;
  detail.bidderName = "";
  detail.quotationPrice = 0;
};

//#endregion

const onClickDownload = async (item: any) => {
  const response = await service.get(item.url, {
    responseType: "blob", // important
  });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", item.name);
  document.body.appendChild(link);
  link.click();
};
</script>
<style lang="scss" scoped>
.history {
  &-content {
    min-height: 400px;
  }
}
.file-list {
  &-item {
    display: flex;
    align-items: center;
    &-text {
      color: #409eff;
      cursor: pointer;
    }
    &-action {
      flex-shrink: 0;
      color: #545252;
      cursor: pointer;
      .action-icon {
        margin-left: 4px;
      }
    }
  }
}
</style>
