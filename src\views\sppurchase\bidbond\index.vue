<template>
  <el-card>
    <div class="notice">
      <div class="notice-search">
        <el-form
          ref="elFormRef"
          :inline="true"
          :model="state.queryForm"
          @keyup.enter="getDataList()"
        >
          <el-form-item prop="packageName">
            <el-input
              v-model="state.queryForm.packageName"
              placeholder="采购计划名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="depositStatus">
            <fast-select
              v-model="state.queryForm.depositStatus"
              dict-type="deposit_pay_status"
              clearable
              placeholder="状态"
            ></fast-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="notice-list">
        <el-table v-loading="state.dataListLoading" :data="state.dataList" border>
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageDeposit"
            label="保证金金额（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="depositStatus"
            label="退款状态"
            header-align="center"
            align="center"
            dict-type="deposit_pay_status"
          ></fast-table-column>
          <el-table-column
            prop="title"
            label="退款时间"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              <span v-if="scope.row.depositStatus == 2">{{ scope.row.updateTime }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="notice-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";

const state: IHooksOptions = reactive({
  dataListUrl: "/purchase/deposit/pageSupplier",
  queryForm: {
    packageName: "",
    depositStatus: "",
  },
});
const router = useRouter();
const elFormRef = ref();

const resetForm = () => {
  elFormRef.value.resetFields();
  getDataList(1);
};

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);
</script>
<style lang="scss" scoped>
.notice {
  &-list {
    margin-top: 16px;
  }
}
</style>
