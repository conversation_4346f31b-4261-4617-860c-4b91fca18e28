<template>
  <el-card>
    <div class="notice">
      <div class="notice-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="采购计划编号"
              clearable
              v-model="state.queryForm.packageNo"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="采购计划名称"
              clearable
              v-model="state.queryForm.packageName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="中标人名称"
              clearable
              v-model="state.queryForm.winBidder"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onResetSearch()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="notice-action">
        <el-button type="primary" @click="addOrUpdateHandle(false)">新增</el-button>
      </div>
      <div class="notice-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="title"
            label="通知书标题"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="winBidder"
            label="中标人"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="status"
            label="状态"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              <el-tag type="primary" v-if="scope.row.status == '00'">暂存</el-tag>
              <el-tag type="success" v-if="scope.row.status == '01'">已发送</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="onClickDetail(scope.row)"
                v-auth="'purchase:winResultNotice:info'"
                v-if="scope.row.status == '01'"
              >
                查看
              </el-button>
              <el-button
                type="primary"
                link
                @click="addOrUpdateHandle(true, scope.row.id)"
                v-if="scope.row.status == '00'"
                v-auth="'purchase:winResultNotice:update'"
              >
                编辑
              </el-button>
              <el-button
                type="primary"
                link
                @click="deleteBatchHandle(scope.row.id)"
                v-if="scope.row.status == '00'"
                v-auth="'purchase:winResultNotice:delete'"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="notice-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <Detail :show="detail.show" :id="detail.id" @on-close="onCloseDetail"></Detail>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive } from "vue";
import { useRouter } from "vue-router";
import Detail from "./components/Detail.vue";
const router = useRouter();

const state = reactive<IHooksOptions>({
  queryForm: {
    packageNo: "",
    packageName: "",
    winBidder: "",
  },
  dataListUrl: "/purchase/winResultNotice/page",
  deleteUrl: "/purchase/winResultNotice",
});

const { getDataList, sizeChangeHandle, currentChangeHandle, deleteBatchHandle } = useCrud(
  state
);

const onResetSearch = () => {
  state.queryForm.packageNo = "";
  state.queryForm.packageName = "";
  state.queryForm.winBidder = "";
  state.pageNo = 1;
  getDataList();
};

const addOrUpdateHandle = (isUpdate: boolean, id?: number) => {
  router.push({
    path: "/picketage/notice/action",
    query: { type: isUpdate ? "update" : "add", id },
  });
};

interface IDetail {
  show: boolean;
  id?: number;
}

const detail = reactive<IDetail>({
  show: false,
  id: void 0,
});

const onClickDetail = (row: any) => {
  detail.id = row.id;
  detail.show = true;
};

const onCloseDetail = () => {
  detail.show = false;
  detail.id = void 0;
};
</script>
<style lang="scss" scoped>
.notice {
  &-list {
    margin-top: 16px;
  }
}
</style>
