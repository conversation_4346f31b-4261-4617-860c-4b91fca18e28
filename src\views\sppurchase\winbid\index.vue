<template>
  <el-card>
    <div class="notice">
      <div class="notice-search">
        <el-form
          ref="elFormRef"
          :inline="true"
          :model="state.queryForm"
          @keyup.enter="getDataList()"
        >
          <el-form-item prop="packageName">
            <el-input
              v-model="state.queryForm.packageName"
              placeholder="采购计划名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="time">
            <el-date-picker
              v-model="state.queryForm.time"
              type="daterange"
              value-format="YYYY-MM-DD"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="queryTime"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="notice-list">
        <el-table v-loading="state.dataListLoading" :data="state.dataList" border>
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="title"
            label="中标通知书"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="createTime"
            label="发送时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="130"
          >
            <template #default="scope">
              <el-button type="primary" link @click="viewHandle(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="notice-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
    </div>
  </el-card>
  <!-- 查看 -->
  <view-vue ref="viewVueRef" v-if="state.viewVueShow"></view-vue>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { nextTick, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import viewVue from "./view.vue";

const state: IHooksOptions = reactive({
  dataListUrl: "/purchase/winResultNotice/bidderPage",
  queryForm: {
    packageName: "",
    startTime: "",
    endTime: "",
    time: "",
  },
  viewVueShow: false,
});
const router = useRouter();
const elFormRef = ref();
const viewVueRef = ref();

// 查看
const viewHandle = (row) => {
  state.viewVueShow = true;
  nextTick(() => {
    viewVueRef.value.init(row);
  });
};
const queryTime = (tt) => {
  if (tt instanceof Array) {
    state.queryForm.startTime = tt[0];
    state.queryForm.endTime = tt[1];
  } else {
    state.queryForm.startTime = state.queryForm.endTime = "";
  }
};
const resetForm = () => {
  elFormRef.value.resetFields();
  getDataList(1);
};

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);
</script>
<style lang="scss" scoped>
.notice {
  &-list {
    margin-top: 16px;
  }
}
</style>
