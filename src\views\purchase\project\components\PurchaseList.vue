<template>
  <div class="list">
    <div class="list_action" style="display: flex; align-items: center">
      <el-upload
        :action="state.uploadUrl"
        :headers="{ Authorization: cache.getToken() }"
        :before-upload="beforeUpload"
        :on-success="handleSuccess"
        :show-file-list="false"
      >
        <el-button type="primary">导入</el-button>
      </el-upload>
      <el-button type="primary" link style="margin-left: 8px" @click="onClickDownload"
        >采购清单模板</el-button
      >
    </div>
    <div class="list_content">
      <vxe-table
        :data="state.materialList"
        border
        :scroll-y="{ enabled: true, gt: 20 }"
        max-height="500px"
      >
        <vxe-column type="seq" width="60" title="序号"></vxe-column>
        <vxe-column field="materialName" title="产品名称"></vxe-column>
        <vxe-column field="materialType" title="产品型号（规格参数）"></vxe-column>
        <vxe-column field="materialUnit" title="计量单位"></vxe-column>
        <vxe-column field="materialQuantity" title="采购数量">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.materialQuantity"
              placeholder="采购数量"
              @blur="onBlurQuantity($event, scope.$rowIndex)"
            ></el-input-number>
          </template>
        </vxe-column>
        <vxe-column field="comment" title="备注">
          <template #default="scope">
            <el-input
              v-model="scope.row.comment"
              placeholder="备注"
              @blur="onBlurComment(scope.row)"
            ></el-input>
          </template>
        </vxe-column>
        <vxe-column field="action" title="操作">
          <template #default="scope">
            <el-button type="primary" link @click="onClickDelete(scope.$rowIndex)">
              删除
            </el-button>
          </template>
        </vxe-column>
      </vxe-table>
    </div>
    <MaterialSelect
      :show="state.action.show"
      @on-close="onCloseAddMaterial"
      @on-confirm="onConfirmAddMaterial"
    ></MaterialSelect>
  </div>
</template>
<script setup lang="ts">
import { reactive, watch } from "vue";
import MaterialSelect from "./MaterialSelect.vue";
import { IMaterialItem } from "../action.vue";
import constant from "@/utils/constant";
import cache from "@/utils/cache";
import { ElMessage, UploadProps } from "element-plus";

interface IProps {
  materialList: IMaterialItem[];
}

const props = withDefaults(defineProps<IProps>(), {
  materialList: () => {
    return [];
  },
});

watch(
  () => props.materialList,
  () => {
    state.materialList = props.materialList;
  }
);

interface IAction {
  show: boolean;
}

interface IState {
  uploadUrl: string;
  materialList: IMaterialItem[];
  action: IAction;
}

const state = reactive<IState>({
  uploadUrl: import.meta.env.VITE_API_URL + "/purchase/projectMaterial/import",
  materialList: [],
  action: {
    show: false,
  },
});

const emit = defineEmits<{
  (e: "on-confirm", materialList: any[]): void;
  (e: "on-delete", rowIndex: number): void;
  (e: "change-quantity", rowIndex: number, value: string): void;
  (e: "change-comment", rowIndex: number, value: string): void;
}>();

const onClickAddMaterial = () => {
  state.action.show = true;
};

const onCloseAddMaterial = () => {
  state.action.show = false;
};

const onConfirmAddMaterial = (selectedData: any[]) => {
  state.action.show = false;
  console.log(selectedData);
  emit("on-confirm", selectedData);
};

const onClickDelete = (rowIndex: number) => {
  emit("on-delete", rowIndex);
};

const onBlurQuantity = (event: any, rowIndex: number) => {
  console.log(event);
  let quantity = event.target.value;
  let strNum = quantity.toString();
  let decimalIndex = strNum.indexOf(".");

  if (decimalIndex !== -1) {
    // 如果有小数部分，则保留三位小数
    strNum = strNum.slice(0, decimalIndex + 4);
  }
  emit("change-quantity", rowIndex, strNum);
};

const onBlurComment = (scope: any) => {
  emit("change-comment", scope.$rowIndex, scope.row.comment);
};

const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  console.log(file);
  let fileNameList = file.name.split(".");
  let fileType = fileNameList[fileNameList.length - 1];
  if (["xlsx"].indexOf(fileType) == -1) {
    ElMessage.error("只允许上传xlsx类型文件");
    return false;
  }
  return true;
};

const handleSuccess: UploadProps["onSuccess"] = (res, file) => {
  if (res.code === 0) {
    let newMaterialList = JSON.parse(JSON.stringify(state.materialList));
    (res?.data ?? []).forEach((item: any) => {
      newMaterialList.push({
        id: void 0,
        materialNo: "",
        materialName: item?.materialName ?? "",
        materialType: item?.materialType ?? "",
        materialUnit: item?.materialUnit ?? "",
        materialQuantity: item?.materialQuantity ?? void 0,
        comment: item?.comment ?? "",
      });
    });
    console.log(newMaterialList);
    emit("on-confirm", newMaterialList);
  } else {
    ElMessage.error("导入失败：" + res.msg);
  }
};

const onClickDownload = async () => {
  location.href = constant.apiUrl + '/purchase/projectMaterial/downloadExcel?access_token=' + cache.getToken()
  // const docxFilePath = "/project_list.xlsx";
  // const link = document.createElement("a");
  // link.href = docxFilePath;
  // link.setAttribute("download", "采购清单模板");
  // document.body.appendChild(link);
  // link.click();
};
</script>
<style lang="scss" scoped>
.list {
  &_content {
    margin-top: 16px;
  }
}
</style>
