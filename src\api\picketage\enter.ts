import service from '@/utils/request'

export const getBidderList = (packageId: number) => {
  return service.get(`/purchase/registration/getBidderList/${packageId}`)
}

export const getBidderInfo = (id: number) => {
  return service.get(`/purchase/winBidder/${id}`)
}

//保存中标人
export interface IActionWinBidder {
  packageId?: number
  packageNo: string
  packageName: string
  winBidderId?: number
  winBidder: string
  contactName: string
  contactPhone: string
  winPrice?: number
  serviceCharge?: number
  notes: string
}
export const saveWinBidder = (reqData: IActionWinBidder) => {
  return service.post('/purchase/winBidder', reqData)
}