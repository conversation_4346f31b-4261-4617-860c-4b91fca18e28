<template>
  <el-dialog
    v-model="state.visible"
    title="支付二维码"
    width="400px"
    :close-on-click-modal="false"
  >
    <div class="qrcod" v-if="state.value">
      <qrcode-vue :value="state.value" :size="state.size" :foreground="state.color" level="H" />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="getPayStatus">确认支付</el-button>
        <el-button @click="state.visible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive } from "vue"
import { useOrderGetPayStatusApi } from '@/api/pnotice'
import QrcodeVue from 'qrcode.vue'
import { ElMessage } from 'element-plus'

const state = reactive({
  visible: false,
  value:'',
  size:200,
  color:'#03a203',
  codeStr:{},
  flag:false,
  nowID:''
})

const emit = defineEmits(['callbk'])

const init = (codeStr,id)=>{
  state.visible = true
  state.codeStr = codeStr
  state.nowID = id
  if(codeStr.urlCode){
    state.value = codeStr.urlCode
  }
}
const callbk = ()=>{
  emit('callbk')
}

// 获取支付状态
const getPayStatus = ()=>{
  useOrderGetPayStatusApi(state.nowID).then((res) => {
    if(res.code == 0){
      if(res.data == 0){
        ElMessage.error('还未支付')
      }else{
        ElMessage.success('支付成功')
        state.visible = false
        callbk()
      }
    }
  })
}


defineExpose({
	init
})
</script>
<style lang="scss" scoped>
.qrcod{text-align: center;margin: 20px 0;}
</style>
