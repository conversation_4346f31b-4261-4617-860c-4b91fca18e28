<template>
  <el-drawer
    v-model="drawer.show"
    title="报价明细"
    :size="1200"
    class="offer-detail-drawer"
    @close="onClickClose"
  >
    <div class="detail">
      <div class="detail-title">{{ props.bidderName }}报价详情</div>
      <div class="detail-table">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="materialName"
            label="产品名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="materialSpec"
            label="产品型号（规格参数）"
            header-align="center"
            align="center"
            width="200"
          ></el-table-column>
          <el-table-column
            prop="materialUnit"
            label="单位"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="materialQuantity"
            label="数量"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="unitPrice"
            label="单价（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="totalPrice"
            label="总价（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="remark"
            label="备注"
            header-align="center"
            align="center"
          ></el-table-column>
        </el-table>
      </div>
      <div class="detail-page">
        <div class="detail-page-left">总报价（元）：{{ props.quotationPrice }}</div>
        <div class="detail-page-right">
          <el-pagination
            :current-page="state.pageNo"
            :page-sizes="state.pageSizes"
            :page-size="state.pageSize"
            :total="state.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="sizeChangeHandle"
            @current-change="currentChangeHandle"
          >
          </el-pagination>
        </div>
      </div>
      <div class="detail-file"></div>
    </div>
  </el-drawer>
</template>
<script setup lang="ts">
import { reactive, watch } from "vue";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";

//#region props相关
interface IProps {
  show: boolean;
  id?: number;
  bidderName: string;
  quotationPrice?: number;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  bidderName: "",
  quotationPrice: 0,
});
//#endregion

interface IDrawer {
  show: boolean;
}

const drawer = reactive<IDrawer>({
  show: false,
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      drawer.show = true;
      if (props.id) {
        state.queryForm.quotationId = props.id;
        state.page = 1;
        getDataList();
      } else {
        state.page = 1;
        state.total = 0;
        state.dataList = [];
      }
    }
  }
);

const state = reactive<IHooksOptions>({
  createdIsNeed: false,
  queryForm: {
    quotationId: void 0,
  },
  dataListUrl: "/purchase/detail/page",
});

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

const emit = defineEmits<{
  (e: "on-close"): void;
}>();

const onClickClose = () => {
  drawer.show = false;
  emit("on-close");
};
</script>

<style lang="scss" scoped>
.detail {
  &-title {
    font-size: 16px;
    font-weight: 650;
  }
  &-table {
    margin-top: 16px;
  }
  &-page {
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-left {
      margin-top: 16px;
      font-weight: 700;
    }
  }
}
</style>
