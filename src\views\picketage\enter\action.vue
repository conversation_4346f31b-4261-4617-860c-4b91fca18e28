<template>
  <el-card>
    <div class="action">
      <div class="action-title">确定中标人</div>
      <div class="action-content">
        <el-form
          ref="dataFormRef"
          :model="state.dataForm"
          :rules="state.dataRules"
          label-width="140px"
          class="action-content-form"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="packageName" label="采购计划">
                <div style="display: flex; align-items: center; width: 100%">
                  <el-input
                    readonly
                    v-model="state.dataForm.packageName"
                    placeholder="采购计划"
                    @click="onSelectPlan"
                  ></el-input>
                  <el-button
                    type="primary"
                    style="margin-left: 10px"
                    @click="onSelectPlan"
                  >
                    选择
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="winBidderId" label="中标单位">
                <el-select
                  placeholder="中标单位"
                  style="width: 100%"
                  v-model="state.dataForm.winBidderId"
                  @change="onSelectBidder"
                >
                  <el-option
                    :label="item.bidderName"
                    :value="item.bidderId"
                    v-for="item in state.companyList"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="contactName" label="中标单位联系人">
                <el-input
                  v-model="state.dataForm.contactName"
                  placeholder="中标单位联系人"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="contactPhone" label="中标单位联系电话">
                <el-input
                  v-model="state.dataForm.contactPhone"
                  placeholder="中标单位联系电话"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                prop="winPrice"
                label="中标金额（元）"
                class="action-content-form-number"
              >
                <el-input-number
                  v-model="state.dataForm.winPrice"
                  placeholder="中标金额（元）"
                  controls-position="right"
                  style="width: 100%"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="state.dataForm.packageMode != '3'">
              <el-form-item
                prop="serviceCharge"
                label="合同额"
                class="action-content-form-number"
              >
                <el-input-number
                  v-model="state.dataForm.serviceCharge"
                  placeholder="合同额"
                  controls-position="right"
                  style="width: 100%"
                ></el-input-number>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item prop="notes" label="备注">
                <el-input
                  v-model="state.dataForm.notes"
                  placeholder="备注"
                  type="textarea"
                  :rows="4"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="action-content-action">
          <el-button type="primary" @click="onSubmit">提交</el-button>
          <el-button @click="onClickReturn">返回</el-button>
        </div>
      </div>
      <PlanSelect
        :show="state.planSelect.show"
        from="enter"
        @on-select="onSelectedPlan"
        @on-close="onClosePlan"
      ></PlanSelect>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { reactive, ref } from "vue";
import PlanSelect from "@/views/picketage/components/PlanSelect.vue";
import { ElMessage, FormRules } from "element-plus";
import { getBidderList, IActionWinBidder, saveWinBidder } from "@/api/picketage/enter";
import { closeTab } from "@/utils/tabs";
import { useRoute, useRouter } from "vue-router";

const router = useRouter();
const route = useRoute();

interface IDataForm {
  packageId?: number;
  packageNo: string;
  packageName: string;
  packageMode: string;
  winBidderId?: number;
  winBidder: string;
  contactName: string;
  contactPhone: string;
  winPrice?: number;
  serviceCharge?: number;
  notes: string;
}

interface ICompanyItem {
  bidderId: number;
  bidderName: string;
  contactName: string;
  contactPhone: string;
  finalQuotation?: number;
  serviceCharge?: number;
}

interface IState {
  dataForm: IDataForm;
  dataRules: FormRules;
  planSelect: {
    show: boolean;
  };
  companyList: ICompanyItem[];
}

const state = reactive<IState>({
  dataForm: {
    packageId: void 0,
    packageNo: "",
    packageName: "",
    packageMode: "",
    winBidderId: void 0,
    winBidder: "",
    contactName: "",
    contactPhone: "",
    winPrice: void 0,
    serviceCharge: void 0,
    notes: "",
  },
  dataRules: {
    packageName: [
      { required: true, message: "请选择采购计划", trigger: ["change", "blur"] },
    ],
    winBidderId: [{ required: true, message: "请选择中标单位", trigger: "change" }],
    contactName: [{ required: true, message: "请输入联系人", trigger: "blur" }],
    contactPhone: [{ required: true, message: "请输入联系电话", trigger: "blur" }],
    winPrice: [{ required: true, message: "请输入中标金额", trigger: "blur" }],
    serviceCharge: [{ required: true, message: "请输入合同额", trigger: "blur" }],
  },
  planSelect: {
    show: false,
  },
  companyList: [],
});

const dataFormRef = ref();

const onSelectPlan = () => {
  state.planSelect.show = true;
};

const onSelectedPlan = (row: any) => {
  state.dataForm.packageId = row.id;
  state.dataForm.packageNo = row.packageNo;
  state.dataForm.packageName = row.packageName;
  state.dataForm.packageMode = row.packageMode;
  GetBidderList();
  state.planSelect.show = false;
};

const onClosePlan = () => {
  state.planSelect.show = false;
};

const GetBidderList = () => {
  if (state.dataForm.packageId) {
    getBidderList(state.dataForm.packageId).then((res: any) => {
      if (res.code === 0) {
        let newCompanyList: ICompanyItem[] = [];
        res.data.forEach((item: ICompanyItem) => {
          newCompanyList.push({
            bidderId: item.bidderId,
            bidderName: item.bidderName,
            contactName: item.contactName,
            contactPhone: item.contactPhone,
            finalQuotation: item.finalQuotation,
            serviceCharge: item.serviceCharge,
          });
        });
        state.companyList = newCompanyList;
      }
    });
  }
};

const onSelectBidder = () => {
  let index = state.companyList.findIndex((item: ICompanyItem) => {
    return item.bidderId === state.dataForm.winBidderId;
  });
  if (index !== -1) {
    state.dataForm.winBidder = state.companyList[index].bidderName;
    state.dataForm.contactName = state.companyList[index].contactName;
    state.dataForm.contactPhone = state.companyList[index].contactPhone;
    state.dataForm.winPrice = state.companyList[index].finalQuotation;
  } else {
    state.dataForm.winBidder = "";
    state.dataForm.contactName = "";
    state.dataForm.contactPhone = "";
    state.dataForm.winPrice = void 0;
    state.dataForm.serviceCharge = void 0;
  }
};

const onSubmit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      let reqData: IActionWinBidder = {
        packageId: state.dataForm.packageId,
        packageNo: state.dataForm.packageNo,
        packageName: state.dataForm.packageName,
        winBidderId: state.dataForm.winBidderId,
        winBidder: state.dataForm.winBidder,
        contactName: state.dataForm.contactName,
        contactPhone: state.dataForm.contactPhone,
        winPrice: state.dataForm.winPrice,
        serviceCharge: state.dataForm.serviceCharge,
        notes: state.dataForm.notes,
      };
      saveWinBidder(reqData).then((res: any) => {
        if (res.code === 0) {
          closeTab(router, route);
          ElMessage.success("操作成功");
        }
      });
    }
  });
};

const onClickReturn = () => {
  closeTab(router, route);
};
</script>
<style lang="scss" scoped>
.action {
  &-title {
    font-size: 16px;
    font-weight: 700;
  }
  &-content {
    margin: 0 100px;
    margin-top: 16px;
    &-form {
      &-number {
        :deep(.el-input__inner) {
          text-align: left;
        }
      }
    }
    &-action {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
