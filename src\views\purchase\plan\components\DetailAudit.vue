<template>
  <div class="audit">
    <el-form
      ref="dataFormRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="120px"
    >
      <el-form-item prop="auditResult" label="审核结果">
        <el-radio-group v-model="state.dataForm.auditResult">
          <el-radio key="01" label="01">通过</el-radio>
          <el-radio key="00" label="00">不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        prop="opinion"
        label="审核意见"
        v-if="state.dataForm.auditResult === '01'"
      >
        <el-input
          v-model="state.dataForm.opinion"
          placeholder="审核意见"
          type="textarea"
          :rows="4"
        ></el-input>
      </el-form-item>
      <el-form-item prop="opinion1" label="审核意见" v-else>
        <el-input
          v-model="state.dataForm.opinion1"
          placeholder="审核意见"
          type="textarea"
          :rows="4"
        ></el-input>
      </el-form-item>
      <el-form-item
        prop="nextReviewedBy"
        label="添加审核人"
        v-if="state.dataForm.auditResult === '01'"
      >
        <div style="display: flex; align-items: center; width: 100%">
          <el-input
            v-model="state.dataForm.nextReviewedBy"
            readonly
            placeholder="审核人"
            style="width: 250px"
            @click="onSelectUser"
          ></el-input>
          <el-button type="primary" style="margin-left: 10px" @click="onSelectUser">
            选择
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    <div class="audit_action">
      <el-button type="primary" @click="onClickAudit()">审核</el-button>
    </div>
    <UserSelect
      :show="state.userAction.show"
      :title="state.userAction.title"
      @on-close="onCloseSelectUser"
      @on-select="onConfirmSelectUser"
    ></UserSelect>
  </div>
</template>
<script setup lang="ts">
import { auditProjectPlan, IAuditProjectPlan } from "@/api/purchase/plan";
import { FormRules } from "element-plus";
import { reactive, ref } from "vue";
import UserSelect from "./UserSelect.vue";

const dataFormRef = ref();

interface IProps {
  id?: number;
}

const props = withDefaults(defineProps<IProps>(), {});

interface IDataForm {
  auditResult: string;
  opinion: string;
  opinion1: string;
  nextReviewedById?: number;
  nextReviewedBy: string;
}

interface IUserAction {
  show: boolean;
  title: string;
}

interface IState {
  dataForm: IDataForm;
  dataRules: FormRules;
  userAction: IUserAction;
}

const state = reactive<IState>({
  dataForm: {
    auditResult: "01",
    opinion: "",
    opinion1: "",
    nextReviewedById: void 0,
    nextReviewedBy: "",
  },
  dataRules: {
    auditResult: [{ required: true, message: "请选择审核结果", trigger: "change" }],
    opinion1: [{ required: true, message: "请输入审核意见", trigger: "blur" }],
  },
  userAction: {
    show: false,
    title: "选择审核人",
  },
});

const onSelectUser = () => {
  state.userAction.show = true;
};

const onCloseSelectUser = () => {
  state.userAction.show = false;
};

const onConfirmSelectUser = (row: any) => {
  let newDataForm = JSON.parse(JSON.stringify(state.dataForm));
  newDataForm.nextReviewedById = row.id;
  newDataForm.nextReviewedBy = row.username;
  state.dataForm = newDataForm;
};

const emit = defineEmits<{
  (e: "audit-success"): void;
}>();

const onClickAudit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      let reqData: IAuditProjectPlan = {
        bizMark: props.id,
        bizType: "net.yuan.purchase.vo.TbPackageVO",
        auditResult: state.dataForm.auditResult,
        opinion: (state.dataForm.auditResult === '01'?state.dataForm.opinion:state.dataForm.opinion1),
        nextReviewedById: state.dataForm.nextReviewedById,
        nextReviewedBy: state.dataForm.nextReviewedBy,
      };
      auditProjectPlan(reqData).then((res: any) => {
        if (res.code === 0) {
          emit("audit-success");
        }
      });
    }
  });
};
</script>
<style lang="scss" scoped>
.audit {
  &_action {
    display: flex;
    justify-content: center;
  }
}
</style>
