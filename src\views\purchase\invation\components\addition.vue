<template>
  <el-dialog
    v-model="innerState.visible"
    title="追加供应商"
    width="60%"
    :close-on-click-modal="false"
  >
    <div class="notice">
      <div class="notice-search">
        <el-button type="primary" @click="additionSupplier()">追加供应商</el-button>
      </div>
      <div class="notice-list">
        <el-table
          :data="innerState.invitationBidders"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="bidderName"
            label="供应商名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="contactName"
            label="联系人"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="contactPhone"
            label="联系电话"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="agreeStatusLabel"
            label="接受状态"
            header-align="center"
            align="center"
            dict-type="audit_status"
          ></fast-table-column>
        </el-table>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="innerState.visible = false">取消</el-button>
        <el-button type="primary">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 选择供应商 -->
  <supplier-list v-if='innerState.supplierListShow' ref="supplierListRef" @select-result='selectBidres'></supplier-list>
</template>
<script setup lang="ts">
import { reactive, ref, nextTick } from "vue";
import { useGetListByPackIdApi,useInvitationBidderBatchApi } from '@/api/pnotice'
import supplierList from './supplierList.vue'

const supplierListRef = ref()
const innerState = reactive({
  supplierListShow: false,
  visible:false,
  invitationBidders:[],
  packageId:''
});

const additionSupplier = (id) => {
  innerState.supplierListShow = true
  nextTick(()=>{
    supplierListRef.value.init()
  })
}
// 列表
const getSupplier = (packid)=>{
  useGetListByPackIdApi(packid).then((res) => {
    if(res.code == 0){
      innerState.invitationBidders = res.data
    }
  })
} 
// 追加
const selectBidres = (res)=>{
  let bidders = []
  for(let rr of res){
    bidders.push({
      packageId:innerState.packageId,
      bidderId: rr.userDetailsId,
      bidderName: rr.userDetailsName,
      contactPhone: rr.contactPhone,
      contactName: rr.contactName
    })
  }
  useInvitationBidderBatchApi(bidders).then((res) => {
    if(res.code == 0){
      getSupplier(innerState.packageId)
    }
  })
}
const init = (packid)=>{
  innerState.visible = true
  innerState.packageId = packid
  getSupplier(packid)
}


defineExpose({
	init
})
</script>
<style lang="scss" scoped>
.notice {
  &-list {
    margin-top: 16px;
  }
}
</style>
