<template>
  <el-card>
    <div class="search">
      <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
        <el-form-item>
          <el-input
            v-model="state.queryForm.customerName"
            placeholder="客户名称"
            clearable
            style="width: 200px"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <fast-select
            style="width: 200px"
            v-model="state.queryForm.customerType"
            dict-type="customer_type"
            clearable
            placeholder="客户类型"
          ></fast-select>
        </el-form-item>
        <el-form-item>
          <fast-select
            style="width: 200px"
            v-model="state.queryForm.customerTags"
            dict-type="customer_tags"
            clearable
            placeholder="客户标签"
          ></fast-select>
        </el-form-item>
        <el-form-item>
          <fast-select
            style="width: 200px"
            v-model="state.queryForm.customerSource"
            dict-type="customer_source"
            clearable
            placeholder="客户来源"
          ></fast-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            @change="changeDateRange"
            v-model="state.rangeTime"
            start-placeholder="创建时间"
            range-separator="至"
            end-placeholder="创建时间"
            type="daterange"
            value-format="YYYY-MM-DD"
            style="width: 250px"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item v-if="props.pageSource==='subordinateCustomer'">
          <el-input
            v-model="state.queryForm.headerName"
            placeholder="负责人"
            clearable
            style="width: 200px"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList()">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="onClickReset()">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </el-card>
  <el-card style="margin-top: 10px">
    <div style="margin-bottom: 10px">
      <el-button
        v-if="props.pageSource==='myCustomer'"
        type="primary"
        :icon="Plus"
        @click="addOrUpdateHandle()"
        v-auth="'work:customer:save'"
      >
        新建客户
      </el-button>
      <el-button type="success" v-if="props.pageSource==='myCustomer'" @click="transferHandle(1)" v-auth="'work:customer:transferHeader'"
        >转移给他人跟进</el-button>
      <el-button type="success" v-if="props.pageSource==='subordinateCustomer'" @click="transferHandle(1)" v-auth="'work:jgcustomer:transferHeader'"
        >转移给他人跟进</el-button>
    </div>
    <el-table
      v-loading="state.dataListLoading"
      :data="state.dataList"
      border
      style="width: 100%"
      @selection-change="selectionChangeHandle1"
    >
      <el-table-column
        type="selection"
        header-align="center"
        align="center"
        width="50"
      ></el-table-column>
      <el-table-column
        type="index"
        label="序号"
        header-align="center"
        align="center"
        width="70"
      ></el-table-column>
      <el-table-column
        prop="customerName"
        label="客户名称"
        min-width="200"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="customerType"
        label="客户类型"
        header-align="center"
        align="center"
      >
        <template #default="scope">
          {{ scope.row.customerType?getDictLabel(appStore.dictList, "customer_type", scope.row.customerType):'-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="customerTags"
        label="客户标签"
        header-align="center"
        align="center"
      >
        <template #default="scope">
          {{ scope.row.customerTags?getDictLabel(appStore.dictList, "customer_tags", scope.row.customerTags):'-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="followStatus"
        label="跟进状态"
        header-align="center"
        align="center"
      >
        <template #default="scope">
          {{ scope.row.followStatus?getDictLabel(appStore.dictList, "follow_status", scope.row.followStatus):'-' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="linkMan"
        label="联系人"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="linkPhone"
        label="电话"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="customerSource"
        label="客户来源"
        header-align="center"
        align="center"
      >
        <template #default="scope">
          {{
            scope.row.customerSource?getDictLabel(appStore.dictList, "customer_source", scope.row.customerSource):'-'
          }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="props.pageSource==='subordinateCustomer'"
        prop="headerName"
        label="负责人"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        header-align="center"
        align="center"
        width="150"
      >
        <template #default="scope">
          <el-button link type="primary" @click="detailHandle(scope.row)">查看</el-button>
          <el-button v-if="props.pageSource==='myCustomer'" v-auth="'work:customer:delete'" link type="primary" @click="deleteBatchHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="state.pageNo"
      :page-sizes="state.pageSizes"
      :page-size="state.pageSize"
      :total="state.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    >
    </el-pagination>
  </el-card>
  <!-- 弹窗, 新增 / 修改 -->
  <add-or-update ref="addOrUpdateRef" @refresh-data-list="getDataListAdd"></add-or-update>
  <transfer-dialog
    ref="transferRef"
    @refresh-data-list="refreshDataBefore('transfer')"
    :idList="state.dataListSelections"
  ></transfer-dialog>
  <detail
    ref="detailRef"
    from="custom"
    :pageSource="props.pageSource"
    @refresh-data-list="getDataList(state.pageNo)"
  ></detail>
</template>

<script setup lang="ts">
import { useCrud } from "@/hooks";
import { reactive, ref,onMounted } from "vue";
import AddOrUpdate from "./add-or-update.vue";
import detail from "./detail.vue";
import { ElMessage } from "element-plus";
import transferDialog from "./transfer-dialog.vue";
// 字典渲染
import { getDictLabel } from "@/utils/tool";
import { useAppStore } from "@/store/modules/app";
import { Plus } from "@element-plus/icons-vue";
const appStore = useAppStore();
const props=defineProps({
  pageSource:{
    default:'',
    type:String
  }
})
const state = reactive({
  createdIsNeed:false,
  dataListUrl: "/work/customer/page",
  deleteUrl: "/work/customer",
  queryForm: {
    customerName: "",
    customerType: "",
    customerTags: "",
    customerSource: "",
    startDate: "",
    endDate: "",
  },
  rangeTime: [],
  dataListSelections: [],
});

const init=()=>{
  if(props.pageSource==='myCustomer'){
    state.dataListUrl = "/work/customer/page";
  }else if(props.pageSource==='subordinateCustomer'){
    state.dataListUrl = "/work/customer/findPage";
  }
  getDataList()
}

const onClickReset = () => {
  state.queryForm.customerName = "";
  state.queryForm.customerType = "";
  state.queryForm.customerTags = "";
  state.queryForm.customerSource = "";
  state.queryForm.startDate = "";
  state.queryForm.endDate = "";
  state.rangeTime = [];
  getDataList();
};

// 多选
const selectionChangeHandle1 = (selections) => {
  state.dataListSelections = selections.map((item) => {
    return {
      id: item.id
    };
  });
};
const transferRef = ref();
const transferHandle = () => {
  if (state.dataListSelections.length===0) {
    ElMessage.error("请您先勾选数据！");
  } else {
    transferRef.value.init();
  }
};
const addOrUpdateRef = ref();
const addOrUpdateHandle = (id) => {
  addOrUpdateRef.value.init(id);
};
const detailRef = ref();
const detailHandle = (row) => {
  detailRef.value.init(row);
};
const changeDateRange = (val) => {
  if (!val) {
    state.queryForm.startDate = "";
    state.queryForm.endDate = "";
  } else {
    state.queryForm.startDate = val[0];
    state.queryForm.endDate = val[1];
  }
};

const getDataListAdd = (setPage: any) => {
  if (setPage && setPage === "1") {
    getDataList(state.pageNo);
  } else {
    getDataList();
  }
};

const refreshDataBefore = (type: string) => {
  if (detailRef.value && detailRef.value.visible === true) {
    // 详情窗口已打开
    if (type === "transfer") {
      // 关闭弹框
      detailRef.value.setVisible();
    }
  }
  getDataList(state.pageNo);
};

const {
  getDataList,
  selectionChangeHandle,
  sizeChangeHandle,
  currentChangeHandle,
  deleteBatchHandle,
} = useCrud(state);

defineExpose({
  init
});
</script>
<style lang="scss" scoped>
</style>
