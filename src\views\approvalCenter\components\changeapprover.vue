<template>
  <el-dialog
    v-model="state.show"
    title="更换审批人"
    width="700"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <div class="dia_label">审批人</div>
    <block v-if="state.isMultiple">
      <div class="user-div">
        <div class="user-box" v-for="(item, index) in state.checkedNodes">
          <div class="user">
            <el-avatar
              shape="circle"
              :size="30"
              :src="item.avatar ?? defaultAvatar"
            ></el-avatar>
            <!-- <svg-icon v-else icon="icon-morentouxiang" size="30"></svg-icon> -->
            <div>{{ item.name }}</div>
          </div>
          <svg-icon icon="icon-doubleright" class="user-next"></svg-icon>
        </div>
        <div class="plus" @click="openSelectUser">
          <svg-icon icon="icon-plus"></svg-icon>
        </div>
      </div>
    </block>
    <block v-else>
      <div class="dia_txt">
        <el-tree-select
          ref="treeSelectRef"
          v-model="state.user"
          :data="treeData"
          check-strictly
          node-key="id"
          show-checkbox
          :props="defaultProps"
          :render-after-expand="false"
        />
      </div>
      <div class="dia_label">理由</div>
      <div class="dia_txt">
        <el-input
          type="textarea"
          v-model="state.reason"
          placeholder="请输入理由"
          :rows="7"
        ></el-input>
      </div>
    </block>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.show = false">取消</el-button>
        <el-button type="primary" @click="changeFlowperson">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 选择成员 -->
  <select-user-tree
    ref="selectUserTreeRef"
    @select-user="getSelectUser"
  ></select-user-tree>
</template>

<script setup lang="ts">
import {
  auditFlowPerson,
  changeFlowPersonBatch,
  getlistOrgUserTree,
  getlistOrgUserTreeWithStatus,
} from "@/api/workbench";
import { ElMessage } from "element-plus";
import { reactive, ref } from "vue";
import selectUserTree from "./selectUserTree.vue";
import defaultAvatar from "@/assets/avatar.png";
interface IState {
  show: boolean;
  isMultiple: boolean;
  id: number;
  user: string;
  reason: string;
  checkedLen: number;
  detailInfo: Object;
  checkedCurrent: any;
  checkedNodes: Array<Tree>;
}
interface Tree {
  [key: string]: any;
}
const treeSelectRef = ref();
const selectUserTreeRef = ref();
const state = reactive<IState>({
  show: false,
  id: 0,
  detailInfo: {},
  reason: "",
  user: "",
  isMultiple: false,
  checkedNodes: [],
  checkedCurrent: null,
  checkedLen: 0,
});
const treeData = ref<Tree[]>([]);
const defaultProps = {
  children: "children",
  label: "name",
  disabled: "status",
};
const emit = defineEmits(["refresh"]);

const init = (obj, val) => {
  state.show = true;
  state.id = obj.id;
  state.detailInfo = obj;
  state.reason = "";
  state.checkedNodes = [];
  state.checkedLen = 0;
  state.checkedCurrent = null;
  state.isMultiple = val;
  if (!state.isMultiple) getUserTreeSingle();
};

const openSelectUser = () => {
  selectUserTreeRef.value?.init(state.id, state.checkedNodes);
};
const getSelectUser = (arr) => {
  state.checkedNodes = arr;
  state.checkedLen = arr.length;
};
const getUserTreeSingle = () => {
  getlistOrgUserTreeWithStatus(state.id).then((res) => {
    if (res.code == 0) {
      treeData.value = res.data;
    }
  });
};
const getCheckedNodesHd = () => {
  let nodes = treeSelectRef.value!.getCheckedNodes();
  state.checkedCurrent = nodes[0];
};
const changeFlowperson = () => {
  if (state.isMultiple) {
    if (state.checkedLen > 0) {
      let pding = state.checkedNodes.find((item) => !item.status);
      let nodes = state.checkedNodes.map((item, index) => {
        return {
          id: item.nodeId ? item.nodeId : null,
          flowId: state.detailInfo.id,
          infoId: state.detailInfo.infoId,
          infoType: state.detailInfo.infoType,
          userId: item.id,
          userName: item.name,
          type: 1,
          status: item.status ? 4 : item.id == pding.id ? 3 : 2,
        };
      });
      changeFlowPersonBatch(nodes).then((res) => {
        if (res.code == 0) {
          ElMessage.success("提交成功");
          state.show = false;
          emit("refresh");
        }
      });
    }
  } else {
    getCheckedNodesHd();
    let params = {
      flowId: state.id,
      replaceRemark: state.reason,
      status: 3,
      userId: state.checkedCurrent.id,
      userName: state.checkedCurrent.name,
    };
    auditFlowPerson(params).then((res) => {
      if (res.code == 0) {
        ElMessage.success("提交成功");
        state.show = false;
        emit("refresh");
      }
    });
  }
};

defineExpose({
  init,
});
</script>

<style scoped lang="scss">
.dia_label,
.dia_txt {
  margin: 8px 0;
}
.user-div {
  display: flex;
  align-items: center;
}
.user-box {
  display: flex;
  align-items: center;
  margin: 0 10px;
}
.user {
  padding: 0 10px;
  position: relative;
  text-align: center;
  .user-del {
    position: absolute;
    right: 8px;
    top: -6px;
  }
  .user-next {
    margin: 0 10px;
  }
}
.plus {
  color: #409eff;
  padding: 10px;
  height: 16px;
  width: 16px;
  margin-left: 6px;
  border: 1px dashed #409eff;
  border-radius: 3px;
  cursor: pointer;
}
</style>
