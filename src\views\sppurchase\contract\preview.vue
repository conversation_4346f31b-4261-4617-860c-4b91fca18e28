<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-02 16:25:21
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-16 17:27:29
-->
<template>
  <el-scrollbar height="75vh">
    <vue-office-pdf 
      :src="state.pdfurl"
      @rendered="renderedHandler"
      @error="errorHandler"
    />
  </el-scrollbar>
</template>
<script setup lang="ts">
import { reactive } from "vue"
import { ElMessage } from 'element-plus'
import VueOfficePdf from '@vue-office/pdf'

const state = reactive({
  visible: false,
  nowId:'',
  pdfurl:'https://501351981.github.io/vue-office/examples/dist/static/test-files/test.pdf'
})

const init = (id)=>{
  state.visible = true
  // state.nowId = id
}

const renderedHandler = ()=> {
  console.log("渲染完成")
}
const errorHandler = () => {
  console.log("渲染失败")
}

defineExpose({
	init
})
</script>
<style lang="scss" scoped>
.action_title{margin: 20px 0;}
.titled{ margin: 20px 0 0;}
</style>
