<template>
  <div class="upload">
    <div class="upload-title">
      <div class="upload-title-left">
        <div class="upload-title-left-text">{{ props.title }}</div>
        <div class="upload-title-left-desc" v-if="props.showDesc">
          (仅支持jpg/png/pdf/zip格式)
        </div>
      </div>
      <div class="upload-title-right">
        <el-upload
          :action="constant.uploadUrl"
          :headers="{ Authorization: cache.getToken() }"
          :before-upload="beforeUpload"
          :on-success="handleSuccess"
          :show-file-list="false"
          v-if="props.action === 'edit'"
        >
          <el-button type="primary">上传</el-button>
        </el-upload>
      </div>
    </div>
    <div class="upload-list">
      <el-table show-overflow-tooltip :data="state.dataList" border>
        <el-table-column
          type="index"
          label="序号"
          header-align="center"
          align="center"
          width="70"
        ></el-table-column>
        <el-table-column
          prop="name"
          label="附件名称"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column prop="action" label="操作" header-align="center" align="center">
          <template #default="scope">
            <!-- <el-button type="primary" link> 预览 </el-button> -->
            <el-button type="primary" link @click="onClickDownload(scope.row)">
              下载
            </el-button>
            <el-button
              type="primary"
              link
              @click="onClickDelete(scope.$index)"
              v-if="props.action === 'edit'"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive, watch } from "vue";
import constant from "@/utils/constant";
import cache from "@/utils/cache";
import { ElMessage, UploadProps } from "element-plus";
import service from "@/utils/request";

export interface IAttachmentItem {
  name: string;
  url: string;
  size: number;
  type: string;
  platform: string;
}

interface IProps {
  action: string;
  title: string;
  showDesc?: boolean;
  fileList: IAttachmentItem[];
  acceptFileType?: string[];
  type: string;
}

const props = withDefaults(defineProps<IProps>(), {
  action: "edit",
  title: "标题",
  showDesc: true,
  fileList: () => {
    return [];
  },
  acceptFileType: () => {
    return [];
  },
  type: "QYZZ",
});

interface IState {
  dataList: IAttachmentItem[];
}

const state = reactive<IState>({
  dataList: [],
});

watch(
  () => props.fileList,
  () => {
    let newFileList = JSON.parse(JSON.stringify(props.fileList));
    state.dataList = newFileList;
  },
  { immediate: true }
);

const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  console.log(file);
  let fileNameList = file.name.split(".");
  let fileType = fileNameList[fileNameList.length - 1];
  if (props.acceptFileType.length > 0 && props.acceptFileType.indexOf(fileType) == -1) {
    ElMessage.error("请上传正确的文件格式");
    return false;
  }
  // if (file.size / 1024 / 1024 / 1024 / 1024 > 1) {
  // 	ElMessage.error('文件大小不能超过100M')
  // 	return false
  // }
  return true;
};

const emit = defineEmits<{
  (e: "on-change-value", type: string, field: string, value: any): void;
}>();

const handleSuccess: UploadProps["onSuccess"] = (res, file) => {
  if (res.code === 0) {
    let newAttachmentItem = {
      type: props.type,
      platform: res.data.platform,
      url: res.data.url,
      name: res.data.name,
      size: res.data.size,
    };
    let newDataList = JSON.parse(JSON.stringify(state.dataList));
    newDataList.push(newAttachmentItem);
    let field = "";
    let type = "";
    switch (props.type) {
      case "QTZZ":
      case "QYZZ":
        type = "certificationInfo";
        field = "certificationInfo";
        break;
      case "ALXX":
        type = "caseInfo";
        field = "caseInfo";
        break;
      case "QYRY":
        type = "honorInfo";
        field = "honorInfo";
        break;
      default:
        break;
    }
    emit("on-change-value", type, field, newDataList);
  } else {
    ElMessage.error("上传失败：" + res.msg);
  }
};

const onClickDelete = (index: number) => {
  console.log(index);
  let newDataList = JSON.parse(JSON.stringify(state.dataList));
  newDataList = newDataList.filter((item: any, fileIndex: number) => {
    return index !== fileIndex;
  });
  let field = "";
  let type = "";
  switch (props.type) {
    case "QTZZ":
    case "QYZZ":
      type = "certificationInfo";
      field = "certificationInfo";
      break;
    case "ALXX":
      type = "caseInfo";
      field = "caseInfo";
      break;
    case "QYRY":
      type = "honorInfo";
      field = "honorInfo";
      break;
    default:
      break;
  }
  emit("on-change-value", type, field, newDataList);
};

const onClickDownload = async (item: any) => {
  const response = await service.get(item.url, {
    responseType: "blob", // important
  });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", item.name);
  document.body.appendChild(link);
  link.click();
};
</script>
<style lang="scss" scoped>
.upload {
  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 12px;
    padding-left: 8px;
    border-bottom: 1px solid #e4e7ed;
    &-left {
      display: flex;
      align-items: center;
      &-text {
        font-size: 16px;
        font-weight: 700;
        color: #333333;
      }
      &-desc {
        margin-left: 8px;
        font-size: 12px;
        color: #f56c6c;
      }
    }
  }
  &-list {
    margin-top: 16px;
  }
}
</style>
