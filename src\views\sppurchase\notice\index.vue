<template>
  <el-card>
    <div class="notice-tabs">
      <el-tabs v-model="state.activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane label="未参与" name="first">
          <div class="notice">
            <div class="notice-search">
              <el-form
                ref="elFormRef"
                :inline="true"
                :model="state.queryForm"
                @keyup.enter="getDataList()"
              >
                <el-form-item prop="packageNo">
                  <el-input
                    v-model="state.queryForm.packageNo"
                    placeholder="采购计划编号"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item prop="packageName">
                  <el-input
                    v-model="state.queryForm.packageName"
                    placeholder="采购计划名称"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item prop="time">
                  <el-date-picker
                    v-model="state.queryForm.time"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                    range-separator="~"
                    start-placeholder="报名开始时间"
                    end-placeholder="报名截止时间"
                    @change="queryTime"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button @click="getDataList()" type="primary">查询</el-button>
                  <el-button @click="resetForm()">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="notice-list">
              <el-table v-loading="state.dataListLoading" :data="state.dataList" border>
                <el-table-column
                  type="index"
                  label="序号"
                  header-align="center"
                  align="center"
                  width="70"
                ></el-table-column>
                <el-table-column
                  prop="packageNo"
                  label="采购计划编号"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="packageName"
                  label="采购计划名称"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="title"
                  label="公告名称"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="buyersName"
                  label="采购单位"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="signStartDate"
                  label="报名开始时间"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="signEndDate"
                  label="报名截止时间"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column label="参与状态" header-align="center" align="center">
                  <template #default>未参与</template>
                </el-table-column>
                <el-table-column
                  label="操作"
                  fixed="right"
                  header-align="center"
                  align="center"
                  width="160"
                >
                  <template #default="scope">
                    <el-button type="primary" link @click="stateCheck(scope.row)">
                      我要参与
                    </el-button>
                    <el-button
                      type="primary"
                      link
                      @click="viewHandle(scope.row.packageId)"
                    >
                      查看
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="notice-page">
              <el-pagination
                :current-page="state.pageNo"
                :page-sizes="state.pageSizes"
                :page-size="state.pageSize"
                :total="state.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
              >
              </el-pagination>
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane label="已参与" name="second">
          <!-- 已参与 -->
          <joined ref="joinedRef"></joined>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-card>
  <!-- 参与 -->
  <participate-vue
    ref="participateVueRef"
    v-if="state.participateVueShow"
    @fresh-list="getDataList()"
  ></participate-vue>
  <!-- 查看 -->
  <view-vue ref="viewVueRef" v-if="state.viewVueShow"></view-vue>
</template>
<script setup lang="ts">
import { useRegistrationCheckApi } from "@/api/pnotice";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { ElMessage } from "element-plus";
import { nextTick, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import participateVue from "./components/participate.vue";
import joined from "./joined.vue";
import viewVue from "./view.vue";

const state: IHooksOptions = reactive({
  dataListUrl: "/purchase/registration/bulletin_page",
  queryForm: {
    packageNo: "",
    packageName: "",
    time: "",
    startTime: "",
    endTime: "",
  },
  viewVueShow: false,
  participateVueShow: false,
  activeName: "first",
});
const router = useRouter();
const viewVueRef = ref();
const participateVueRef = ref();
const joinedRef = ref();
const elFormRef = ref();

const queryTime = (tt) => {
  if (tt instanceof Array) {
    state.queryForm.startTime = tt[0];
    state.queryForm.endTime = tt[1];
  } else {
    state.queryForm.startTime = state.queryForm.endTime = "";
  }
};
// 检查状态
const stateCheck = (obj) => {
  useRegistrationCheckApi(obj.packageId).then((res) => {
    if (res.code == 0) {
      switch (res.data) {
        case 1:
          participateHd(obj);
          break;
        case 2:
          ElMessage.error("报名已经结束");
          break;
        case 0:
          ElMessage.error("报名还未开始");
          break;
      }
    }
  });
};
// 参与
const participateHd = (obj) => {
  state.participateVueShow = true;
  nextTick(() => {
    participateVueRef.value.init(obj);
  });
};
// 查看
const viewHandle = (id) => {
  state.viewVueShow = true;
  nextTick(() => {
    viewVueRef.value.init(id);
  });
};
const resetForm = () => {
  elFormRef.value.resetFields();
  state.queryForm.startTime = state.queryForm.endTime = "";
  getDataList(1);
};
// tab点击
const handleClick = (tab, event) => {
  // console.log(tab.paneName)
  tab.paneName == "first" ? getDataList() : nextTick(() => joinedRef.value.getDataList());
};

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);
</script>
<style lang="scss" scoped>
.notice {
  &-list {
    margin-top: 16px;
  }
}
</style>
