<template>
  <el-card>
    <div class="notice">
      <div class="notice-search">
        <el-form
          ref="elFormRef"
          :inline="true"
          :model="state.queryForm"
          @keyup.enter="getDataList()"
        >
          <el-form-item prop="title">
            <el-input
              v-model="state.queryForm.title"
              placeholder="邀请函名称"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="auditStatus">
            <fast-select
              v-model="state.queryForm.auditStatus"
              dict-type="audit_status"
              clearable
              placeholder="状态"
            ></fast-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="resetForm()">重置</el-button>
            <el-button type="primary" @click="openInvation()">新增</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="notice-list">
        <el-table v-loading="state.dataListLoading" :data="state.dataList" border>
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="title"
            label="邀请函名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="auditStatus"
            label="状态"
            header-align="center"
            align="center"
            dict-type="audit_status"
          ></fast-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="240"
          >
            <template #default="scope">
              <el-button
                v-if="scope.row.auditStatus == 1 || scope.row.auditStatus == 4"
                type="primary"
                link
                @click="openInvation(scope.row.id)"
              >
                编辑
              </el-button>
              <el-button
                v-if="scope.row.auditStatus == 1 || scope.row.auditStatus == 4"
                type="primary"
                link
                @click="deleteBatchHandle(scope.row.id)"
              >
                删除
              </el-button>
              <el-button v-else type="primary" link @click="viewHandle(scope.row.id)">
                查看
              </el-button>
              <el-button
                v-if="scope.row.auditStatus == 3"
                type="primary"
                link
                @click="addsupplier(scope.row.packageId)"
              >
                追加供应商
              </el-button>
              <el-button type="primary" link @click="logHandle(scope.row.id)">
                审核记录
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="notice-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
    </div>
  </el-card>
  <!-- 查看 -->
  <view-vue ref="viewVueRef" v-if="state.viewVueShow"></view-vue>
  <!-- 追加 -->
  <addition-vue ref="additionVueRef" v-if="state.additionVueShow"></addition-vue>
  <!-- 审核记录 -->
  <auditlog ref="auditlogRef" v-if="state.auditlogShow"></auditlog>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { nextTick, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import auditlog from "./auditlog.vue";
import additionVue from "./components/addition.vue";
import viewVue from "./view.vue";

const state: IHooksOptions = reactive({
  dataListUrl: "/purchase/invitation/page",
  deleteUrl: "/purchase/invitation",
  queryForm: {
    title: "",
    auditStatus: "",
  },
  viewVueShow: false,
  auditlogShow: false,
  additionVueShow: false,
});
const router = useRouter();
const elFormRef = ref();
const viewVueRef = ref();
const auditlogRef = ref();
const additionVueRef = ref();

// 新增编辑
const openInvation = (id) => {
  if (id) {
    router.push({
      path: "/purchase/invation/action",
      query: { id },
    });
  } else {
    router.push("/purchase/invation/action");
  }
};
// 查看
const viewHandle = (id) => {
  state.viewVueShow = true;
  nextTick(() => {
    viewVueRef.value.init(id);
  });
};
// 记录
const logHandle = (id) => {
  state.auditlogShow = true;
  nextTick(() => {
    auditlogRef.value.init(id);
  });
};
const addsupplier = (packid) => {
  state.additionVueShow = true;
  nextTick(() => {
    additionVueRef.value.init(packid);
  });
};
const resetForm = () => {
  elFormRef.value.resetFields();
  getDataList(1);
};

const { getDataList, deleteBatchHandle, sizeChangeHandle, currentChangeHandle } = useCrud(
  state
);
</script>
<style lang="scss" scoped>
.notice {
  &-list {
    margin-top: 16px;
  }
}
</style>
