<template>
	<el-dialog
		v-model="visible"
		v-loading="loading"
		:title="!dataForm.id ? '新增邮寄地址' : '修改邮寄地址'"
		:close-on-click-modal="false"
		draggable
		:close-on-press-escape="false"
	>
		<el-form ref="dataFormRef" :model="dataForm" :rules="dataRules" label-width="140px" v-loading="floading">
			<el-form-item prop="recipient" label="收件人：">
				<el-input v-model="dataForm.recipient" placeholder="收件人"></el-input>
			</el-form-item>
			<el-form-item prop="areaSel" label="选择地区：">
				<el-cascader
          class="wd100"
					v-model="dataForm.areaSel"
					placeholder="选择地区"
					:options="areaList"
					:props="{ label: 'name', value: 'id' }"
					filterable
				></el-cascader>
			</el-form-item>
			<el-form-item prop="address" label="详细地址：">
				<el-input v-model="dataForm.address" placeholder="详细地址"></el-input>
			</el-form-item>
			<el-form-item prop="telePhone" label="联系电话：">
				<el-input v-model="dataForm.telePhone" placeholder="联系电话"></el-input>
			</el-form-item>
			<el-form-item prop="email" label="电子邮箱：">
				<el-input v-model="dataForm.email" placeholder="电子邮箱"></el-input>
			</el-form-item>
			<el-form-item prop="postalCode" label="邮政编码：">
				<el-input v-model="dataForm.postalCode" placeholder="邮政编码"></el-input>
			</el-form-item>
			<el-form-item prop="defaultAddress" label="">
				<el-checkbox v-model="dataForm.defaultAddress" true-label="01" false-label="00" label="设为默认邮寄地址" />
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="el-dialog__footer_center">
				<el-button type="primary" @click="submitHandle()">保存</el-button>
				<el-button @click="visible = false">取消</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, defineEmits, defineExpose, onMounted } from 'vue'
import { ElMessage } from 'element-plus/es'
import { useGetAreaTreeApi,useGetMailingAddressApi,useMailingAddressSaveApi,useMailingAddressSavePutApi } from '@/api/pnotice'
const emit = defineEmits(['refreshDataList'])

const visible = ref(false)
const floading = ref(false)
const dataFormRef = ref()
const areaList = reactive([])
const dataForm = reactive({
	id: '',
	recipient: '',
	areaSel: [],
	area: '',
	address: '',
	telePhone: '',
	email: '',
	postalCode: '',
	defaultAddress: '00'
})

const dataRules = ref({
	recipient: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
	areaSel: [{ required: true, message: '必填项不能为空', trigger: 'change' }],
	address: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
	telePhone: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
	email: [{ required: true, message: '必填项不能为空', trigger: 'blur' }],
	postalCode: [{ required: true, message: '必填项不能为空', trigger: 'blur' }]
})

const init = (id?: number) => {
	visible.value = true
	// 重置表单数据
	if (dataFormRef.value) {
		dataFormRef.value.resetFields()
	}
	getAreaList()
  dataForm.id = id ? id : ''
}

const getAddressInfo = (id: number) => {
	useGetMailingAddressApi(id).then(res => {
		Object.assign(dataForm, res.data)
		dataForm.areaSel = res.data.area.split(',').map(item=>parseInt(item))
    floading.value = false
	})
}
// 获取地区列表
const getAreaList = () => {
  floading.value = true
	useGetAreaTreeApi().then(res => {
		Object.assign(areaList, res.data)
    if (dataForm.id) {
      getAddressInfo(dataForm.id)
    }else{
      floading.value = false
    }
	})
}

// 表单提交
const submitHandle = () => {
	dataFormRef.value.validate((valid: boolean) => {
		if (!valid) {
			return false
		}
		dataForm.area = dataForm.areaSel.join(',')
    if(dataForm.id){
      useMailingAddressSavePutApi(dataForm).then(res => {
        if (res.code != 0) {
          return
        }
        ElMessage.success({
          message: '操作成功',
          duration: 500,
          onClose: () => {
            visible.value = false
            emit('refreshDataList')
          }
        })
      })
    }else{
      useMailingAddressSaveApi(dataForm).then(res => {
        if (res.code != 0) {
          return
        }
        ElMessage.success({
          message: '操作成功',
          duration: 500,
          onClose: () => {
            visible.value = false
            emit('refreshDataList')
          }
        })
      })
    }
	})
}

defineExpose({
	init
})
</script>
<style scoped lang="scss">
::v-deep(.wd100){width: 100%;}
::v-deep(.wd100 .el-input){width: 100%;}
</style>
