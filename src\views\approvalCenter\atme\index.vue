<template>
  <el-card>
    <div class="project">
      <div class="project-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataListBefore()">
          <el-form-item>
            <fast-select
              v-model="state.queryForm.flowType"
              dict-type="flow_type"
              class="normal"
              placeholder="审批类型"
              clearable
            >
            </fast-select>
          </el-form-item>
          <el-form-item>
            <fast-select
              v-model="state.queryForm.flowStatus"
              dict-type="flow_status"
              class="normal"
              placeholder="审批状态"
              clearable
            >
            </fast-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="state.queryForm.date"
              type="daterange"
              clearable
              start-placeholder="发起开始日期"
              end-placeholder="发起结束日期"
              value-format="YYYY-MM-DD"
              @change="changeDate"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="提交人姓名/标题/编号"
              clearable
              v-model="state.queryForm.appQueryStr"
              style="width:250px;"
            ></el-input>
          </el-form-item>
          <el-form-item v-if="state.queryForm.flowType">
            <el-input
              placeholder="事由/说明/金额/采购计划名称/合同名称"
              clearable
              v-model="state.queryForm.ywParam"
              style="width:250px;"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataListBefore()" type="primary">查询</el-button>
            <el-button @click="onResetSearch()">重置</el-button>
            <!-- <el-button @click="" type="success" plain>导出</el-button> -->
          </el-form-item>
        </el-form>
      </div>
      <div class="project-list">
        <el-table v-loading="state.dataListLoading" :data="state.dataList" border>
          <!-- <el-table-column type="selection" width="55" /> -->
          <el-table-column
            prop="flowNo"
            label="审批编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="flowTitle"
            label="审批标题"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="creatorName"
            label="发起人"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="createTime"
            label="发起时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="completeTime"
            label="完成时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="auditStatus"
            label="审批状态"
            header-align="center"
            align="center"
            dict-type="flow_status"
          ></fast-table-column>
          <fast-table-column
            prop="infoType"
            label="审批类型"
            header-align="center"
            align="center"
            dict-type="flow_type"
          ></fast-table-column>
          <el-table-column
            v-if="state.flowTypeSearch==='1'"
            prop="infoType"
            label="审批事由"
            header-align="center"
            align="center"
            width="250"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.oaCommonFlowInfoVO&&scope.row.oaCommonFlowInfoVO.title?scope.row.oaCommonFlowInfoVO.title:'-' }}
            </template>
          </el-table-column>
          <template v-if="state.flowTypeSearch==='2'">
            <el-table-column
              prop="infoType"
              label="请假类型"
              header-align="center"
              align="center"
            >
              <template #default="scope">
                {{ scope.row.oaLeaveFlowInfoVO&&scope.row.oaLeaveFlowInfoVO.typeName?
                scope.row.oaLeaveFlowInfoVO.typeName:'-' }}
              </template>
            </el-table-column>
            <el-table-column
              prop="infoType"
              label="请假事由"
              header-align="center"
              align="center"
              width="250"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.oaLeaveFlowInfoVO&&scope.row.oaLeaveFlowInfoVO.flowRemark?scope.row.oaLeaveFlowInfoVO.flowRemark:'-' }}
              </template>
            </el-table-column>
          </template>
          <template v-if="state.flowTypeSearch==='3'">
            <el-table-column
              prop="infoType"
              label="费用类型"
              header-align="center"
              align="center"
            >
              <template #default="scope">
                {{ scope.row.oaCostFlowInfoVO&&scope.row.oaCostFlowInfoVO.typeLabel?scope.row.oaCostFlowInfoVO.typeLabel:'-' }}
              </template>
            </el-table-column>
            <el-table-column
              prop="infoType"
              label="费用汇总（元）"
              header-align="center"
              align="center"
            >
              <template #default="scope">
                {{ scope.row.oaCostFlowInfoVO&&(scope.row.oaCostFlowInfoVO.costSumMoney||scope.row.oaCostFlowInfoVO.costSumMoney===0)?scope.row.oaCostFlowInfoVO.costSumMoney:'-' }}
              </template>
            </el-table-column>
            <el-table-column
              prop="infoType"
              label="费用说明"
              header-align="center"
              align="center"
              width="250"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.oaCostFlowInfoVO&&scope.row.oaCostFlowInfoVO.flowRemark?scope.row.oaCostFlowInfoVO.flowRemark:'-' }}
              </template>
            </el-table-column>
          </template>
          <template v-if="state.flowTypeSearch==='4'">
            <el-table-column
              prop="infoType"
              label="项目名称"
              header-align="center"
              align="center"
              width="250"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.oaPaymentFlowInfoVO&&scope.row.oaPaymentFlowInfoVO.projectName?scope.row.oaPaymentFlowInfoVO.projectName:'-' }}
              </template>
            </el-table-column>
            <el-table-column
              prop="infoType"
              label="付款金额（元）"
              header-align="center"
              align="center"
            >
              <template #default="scope">
                {{ scope.row.oaPaymentFlowInfoVO&&(scope.row.oaPaymentFlowInfoVO.amount||scope.row.oaPaymentFlowInfoVO.amount===0)?scope.row.oaPaymentFlowInfoVO.amount:'-' }}
              </template>
            </el-table-column>
            <el-table-column
              prop="infoType"
              label="付款事由"
              header-align="center"
              align="center"
              width="250"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.oaPaymentFlowInfoVO&&scope.row.oaPaymentFlowInfoVO.flowRemark?scope.row.oaPaymentFlowInfoVO.flowRemark:'-' }}
              </template>
            </el-table-column>
          </template>
          <template v-if="state.flowTypeSearch==='5'">
            <el-table-column
              prop="infoType"
              label="项目名称"
              header-align="center"
              align="center"
              width="250"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.oaPackageVo&&scope.row.oaPackageVo.projectName?scope.row.oaPackageVo.projectName:'-' }}
              </template>
            </el-table-column>
            <el-table-column
              prop="infoType"
              label="采购计划名称"
              header-align="center"
              align="center"
              width="250"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.oaPackageVo&&scope.row.oaPackageVo.packageName?scope.row.oaPackageVo.packageName:'-' }}
              </template>
            </el-table-column>
          </template>
          <template v-if="state.flowTypeSearch==='6'">
            <el-table-column
              prop="infoType"
              label="合同名称"
              header-align="center"
              align="center"
              width="250"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.oaContractFlowInfoVO&&scope.row.oaContractFlowInfoVO.contractName?scope.row.oaContractFlowInfoVO.contractName:'-' }}
              </template>
            </el-table-column>
            <el-table-column
              prop="infoType"
              label="合同金额（元）"
              header-align="center"
              align="center"
            >
              <template #default="scope">
                {{ scope.row.oaContractFlowInfoVO&&(scope.row.oaContractFlowInfoVO.contractPrice||scope.row.oaContractFlowInfoVO.contractPrice===0)?scope.row.oaContractFlowInfoVO.contractPrice:'-' }}
              </template>
            </el-table-column>
          </template>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="150"
          >
            <template #default="scope">
              <!-- v-auth="'purchase:project:info'" -->
              <el-button type="primary" link @click="onClickDetail(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="project-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <Detail ref="detailRef"></Detail>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive, ref } from "vue";
import Detail from "./detail.vue";
const approvalreasonRef = ref();
const detailRef = ref();
const state = reactive<IHooksOptions>({
  queryForm: {
    flowType: "",
    flowNo: "",
    date: "",
    startTime: "",
    endTime: "",
    flowStatus: "",
    queryType: 5,
    ywParam:""
  },
  dataListSelections: [],
  dataListUrl: "/work/flow/page",
  flowTypeSearch:"",//点了查询后的审批类型
});
const {
  getDataList,
  sizeChangeHandle,
  currentChangeHandle,
  deleteBatchHandle,
  selectionChangeHandle,
} = useCrud(state);

const onResetSearch = () => {
  state.queryForm = {
    flowType: "",
    flowNo: "",
    date: "",
    startTime: "",
    endTime: "",
    flowStatus: "",
    queryType: 5,
    ywParam:""
  };
  state.pageNo = 1;
  getDataListBefore();
};

const onClickDetail = (row: any) => {
  detailRef.value.init(row);
};

const changeDate = (date) => {
  if (date) {
    state.queryForm.startTime = date[0];
    state.queryForm.endTime = date[1];
  } else {
    state.queryForm.startTime = "";
    state.queryForm.endTime = "";
  }
};
const getDataListBefore=()=>{
  if(state.queryForm.flowType){
    state.flowTypeSearch=state.queryForm.flowType
  }else{
    state.flowTypeSearch=""
  }
  getDataList()
}
</script>
<style lang="scss" scoped>
.normal {
  width: 150px;
}
</style>
