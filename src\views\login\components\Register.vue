<template>
  <div class="register-form">
    <el-form
      ref="dataFormRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="80px"
      @keyup.enter="onClickSubmit"
    >
      <el-form-item prop="subjectType" label="主体类型">
        <el-radio-group v-model="state.dataForm.subjectType">
          <el-radio key="01" label="01">供应商</el-radio>
          <el-radio key="02" label="02">个人</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="state.dataForm.subjectType === '01'">
        <el-form-item prop="companyName" label="单位名称">
          <el-input
            v-model="state.dataForm.companyName"
            placeholder="请与营业执照的企业名称保持一致"
            style="width: 360px"
            @keyup="handleInput"
          ></el-input>
        </el-form-item>
        <el-form-item prop="attachmentList" label="营业执照">
          <div class="register-form-file">
            <div
              class="register-form-file-item"
              v-for="(item, index) in state.dataForm.attachmentList"
            >
              <el-image :src="item.url" class="register-form-file-item-img"></el-image>
              <div class="register-form-file-item-action">
                <el-icon
                  class="register-form-file-item-action-icon"
                  style="margin-right: 10px"
                  @click.stop="onPreviewImage(item.url)"
                >
                  <View />
                </el-icon>
                <el-icon
                  class="register-form-file-item-action-icon"
                  @click.stop="onDeleteImage(index)"
                  ><Delete
                /></el-icon>
              </div>
            </div>
            <div class="register-form-file-action">
              <el-upload
                class="register-form-file-action-upload"
                :action="constant.uploadUrl"
                :before-upload="beforeUpload"
                :on-success="handleSuccess"
                :show-file-list="false"
              >
                <el-icon class="register-form-file-action-upload-icon"><Plus /></el-icon>
              </el-upload>
            </div>
          </div>
        </el-form-item>
      </template>
      <el-form-item prop="username" label="用户名">
        <el-input
          v-model="state.dataForm.username"
          placeholder="用户名"
          style="width: 360px"
        ></el-input>
      </el-form-item>
      <el-form-item prop="password" label="密码">
        <el-input
          v-model="state.dataForm.password"
          placeholder="密码"
          style="width: 360px"
          type="password"
          show-password
        ></el-input>
      </el-form-item>
      <el-form-item prop="confirmPassword" label="确认密码">
        <el-input
          v-model="state.dataForm.confirmPassword"
          placeholder="确认密码"
          style="width: 360px"
          type="password"
          show-password
        ></el-input>
      </el-form-item>
      <el-form-item prop="qualificationOne" label="企业资质" v-if="state.dataForm.subjectType==='01'">
        <el-cascader v-model="state.dataForm.qualificationOne" :props="propsQualifications" filterable :options="qualificationsList" style="width: 360px;" clearable />
      </el-form-item>
      <el-form-item prop="contactName" label="联系人">
        <el-input
          v-model="state.dataForm.contactName"
          placeholder="联系人"
          style="width: 360px"
        ></el-input>
      </el-form-item>
      <el-form-item prop="contactPhone" label="联系电话">
        <el-input
          v-model="state.dataForm.contactPhone"
          placeholder="联系电话"
          style="width: 360px"
        ></el-input>
      </el-form-item>
      <el-form-item prop="email" label="邮箱">
        <el-input
          v-model="state.dataForm.email"
          placeholder="邮箱"
          style="width: 360px"
        ></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-checkbox v-model="state.agreement">
          我已阅读并同意
          <el-link
            href="https://www.eczl.com.cn/yuan-oa/upload/20240614/E采智链网协议_39581.pdf"
            target="_blank"
            type="primary"
            >《E采智链网协议》</el-link
          >
          <el-link
            href="https://www.eczl.com.cn/yuan-oa/upload/20240614/免责声明_39578.pdf"
            target="_blank"
            type="primary"
            >《免责声明》</el-link
          >
          <el-link
            href="https://www.eczl.com.cn/yuan-oa/upload/20240614/使用协议_39571.pdf"
            target="_blank"
            type="primary"
            >《使用协议》</el-link
          >
        </el-checkbox>
      </el-form-item>
      <el-form-item label="">
        <el-button type="primary" @click="onClickSubmit" :disabled="!state.agreement"
          >提交</el-button
        >
        <el-button @click="back()">返回</el-button>
      </el-form-item>
    </el-form>
    <el-image-viewer
      v-if="state.previewImage.show"
      @close="onClosePreviewImage"
      :url-list="state.previewImage.url"
    >
    </el-image-viewer>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref,onMounted } from "vue";
import { ElMessage, FormRules, UploadProps } from "element-plus";
import { Plus, View, Delete } from "@element-plus/icons-vue";
import constant from "@/utils/constant";
import { IUserRegister, userRegister } from "@/api/auth";
import { useRoute,useRouter } from "vue-router";
import { useQualificationsListApi } from "@/api/supplier/qualifications";
import { useUserStore } from "@/store/modules/user";

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const dataFormRef = ref();

interface IAttachmentItem {
  name: string;
  url: string;
  size: number;
  platform: string;
  type: string;
}

interface IDataForm {
  subjectType: string;
  companyName: string;
  username: string;
  password: string;
  confirmPassword: string;
  contactName: string;
  contactPhone: string;
  email: string;
  attachmentList: IAttachmentItem[];
  qualificationOne:any
}

interface IPreviewImage {
  show: boolean;
  url: string[];
}

interface IState {
  agreement: boolean;
  dataForm: IDataForm;
  dataRules: FormRules;
  previewImage: IPreviewImage;
}
const propsQualifications = {
  value: 'id',
  label: 'name',
  multiple: true,
  // emitPath:false//绑定的内容只获取最后一级的value值
}
const qualificationsList = ref([]);
const handleInput = () => {
  // 正则只能输入中文
  state.dataForm.companyName = state.dataForm.companyName.replace(
    /^[0-9a-zA-Z+\-*/\s\t\n\r!\"#$%&'()*+,-.\/:;<=>?@[\\\]^_`{|}~]+$/g,
    ""
  );
};
const validatorAttachmentList = (rule: any, value: any, callback: any) => {
  if (state.dataForm.attachmentList.length === 0) {
    callback(new Error("请上传营业执照"));
  } else {
    callback();
  }
};

const validatorPassword = (rule: any, value: any, callback: any) => {
  if (
    (state.dataForm.confirmPassword ?? "") !== "" &&
    state.dataForm.password !== state.dataForm.confirmPassword
  ) {
    callback(new Error("两次密码不一致"));
  } else {
    callback();
  }
};

const validatorConfirmPassword = (rule: any, value: any, callback: any) => {
  if (
    (state.dataForm.password ?? "") !== "" &&
    state.dataForm.password !== state.dataForm.confirmPassword
  ) {
    callback(new Error("两次密码不一致"));
  } else {
    callback();
  }
};

const state = reactive<IState>({
  agreement: false,
  dataForm: {
    subjectType: "01",
    companyName: "",
    username: "",
    password: "",
    confirmPassword: "",
    contactName: "",
    contactPhone: "",
    email: "",
    attachmentList: [],
    qualificationOne:[]
  },
  dataRules: {
    subjectType: [{ required: true, message: "请选择主体类型", trigger: "change" }],
    companyName: [{ required: true, message: "请输入单位名称", trigger: "blur" }],
    attachmentList: [
      { required: true, message: "请上传营业执照", trigger: ["change", "blur"] },
      { validator: validatorAttachmentList, trigger: ["change", "blur"] },
    ],
    username: [{ required: true, message: "请输入用户名", trigger: "blur" }],
    password: [
      { required: true, message: "请输入密码", trigger: "blur" },
      { validator: validatorPassword, trigger: "blur" },
    ],
    confirmPassword: [
      { required: true, message: "请再次输入密码", trigger: "blur" },
      { validator: validatorConfirmPassword, trigger: "blur" },
    ],
    contactName: [{ required: true, message: "请输入联系人", trigger: "blur" }],
    contactPhone: [{ required: true, message: "请输入联系电话", trigger: "blur" }],
    qualificationOne: [{ required: true, message: "请选择企业资质", trigger: "change" }],
  },
  previewImage: {
    show: false,
    url: [],
  },
});
onMounted(() => {
  if(route.query&&route.query.fromPage&&route.query.fromPage==="registerPay"){
    if(userStore.registerForm.subjectType){
      state.dataForm=userStore.registerForm
    }
  }
  getQualificationsList()
})
const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  console.log(file);
  let fileType = file.type.split("/")[1];
  if (["jpg", "png", "jpeg"].indexOf(fileType) == -1) {
    ElMessage.error("请上传图片文件");
    return false;
  }
  // if (file.size / 1024 / 1024 / 1024 / 1024 > 1) {
  // 	ElMessage.error('文件大小不能超过100M')
  // 	return false
  // }
  return true;
};

const handleSuccess: UploadProps["onSuccess"] = (res, file) => {
  if (res.code === 0) {
    let newAttachmentItem = {
      platform: res.data.platform,
      url: res.data.url,
      name: res.data.name,
      size: res.data.size,
      type: "QYZZ",
    };
    let newAttachmentList = JSON.parse(JSON.stringify(state.dataForm.attachmentList));
    newAttachmentList.push(newAttachmentItem);
    state.dataForm.attachmentList = newAttachmentList;
  } else {
    ElMessage.error("上传失败：" + res.msg);
  }
};

const onClickSubmit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      let reqData: IUserRegister = {
        subjectType: state.dataForm.subjectType,
        companyName:
          state.dataForm.subjectType === "01" ? state.dataForm.companyName : void 0,
        username: state.dataForm.username,
        password: state.dataForm.password,
        contactName: state.dataForm.contactName,
        contactPhone: state.dataForm.contactPhone,
        email: state.dataForm.email,
        attachmentList:
          state.dataForm.subjectType === "01" ? state.dataForm.attachmentList : [],
        qualificationOne:JSON.stringify(state.dataForm.qualificationOne)
      };
      userRegister(reqData).then((res: any) => {
        if (res.code === 0) {
          ElMessage.success("注册成功");
          userStore.setRegisterForm(state.dataForm);
          let query={
            userId:res.data.userId,
            payAmount:res.data.payAmount,
            subjectType:state.dataForm.subjectType,
            contactName:state.dataForm.contactName,
            contactPhone:state.dataForm.contactPhone,
            companyName:''
          }
          if(state.dataForm.subjectType==="01"){
            query.companyName=state.dataForm.companyName
          }
          router.replace({path:"/registerPay",query:query});
        }
      });
    }
  });
};

const onDeleteImage = (imageIndex: number) => {
  let newAttachmentList = JSON.parse(JSON.stringify(state.dataForm.attachmentList));
  newAttachmentList = newAttachmentList.filter((item: IAttachmentItem, index: number) => {
    return imageIndex !== index;
  });
  state.dataForm.attachmentList = newAttachmentList;
};

const onPreviewImage = (url: string) => {
  let newUrl = [];
  newUrl.push(url);
  state.previewImage.url = newUrl;
  state.previewImage.show = true;
};

const onClosePreviewImage = () => {
  state.previewImage.show = false;
  state.previewImage.url = [];
};

// 获取企业资质列表
const getQualificationsList = async () => {
  const res = await useQualificationsListApi();
  qualificationsList.value = res.data;
};

const back=()=>{
  router.replace("/login");
}
</script>

<style lang="scss" scoped>
.register-form {
  background-color: #ffffff;
  padding: 50px 0;
  display: flex;
  justify-content: center;
  &-file {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 360px;
    &-item {
      margin-right: 10px;
      width: 80px;
      height: 80px;
      margin-bottom: 10px;
      position: relative;
      &-img {
        width: 80px;
        height: 80px;
      }
      &-action {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        // display: flex;
        align-items: center;
        justify-content: center;
        display: none;
        &-icon {
          color: #ffffff;
          font-size: 16px;
          cursor: pointer;
        }
      }
    }
    &-item:hover {
      .register-form-file-item-action {
        display: flex;
      }
    }
    &-action {
      margin-bottom: 10px;
      &-upload {
        width: 80px;
        height: 80px;
        border: 1px dashed #cdd0d6;
        display: flex;
        align-items: center;
        justify-content: center;
        &-icon {
          font-size: 24px;
          color: #909399;
        }
      }
    }
  }
}
</style>
