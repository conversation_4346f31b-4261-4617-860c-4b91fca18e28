<template>
  <el-card>
    <div class="invoice">
      <el-tabs v-model="activeName">
        <el-tab-pane label="未开票" name="1"> </el-tab-pane>
        <el-tab-pane label="已开票" name="2"> </el-tab-pane>
      </el-tabs>
      <div class="invoice-content">
        <div v-if="activeName === '1'">
          <NotInvoice></NotInvoice>
        </div>
        <div v-else>
          <HasInvoice></HasInvoice>
        </div>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import HasInvoice from "./components/has-invoice.vue";
import NotInvoice from "./components/not-invoice.vue";
import { ref } from "vue";

const activeName = ref("1");
</script>
