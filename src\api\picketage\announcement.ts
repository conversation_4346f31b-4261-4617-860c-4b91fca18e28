import service from '@/utils/request'

interface IAttachmentItem {
  name: string;
  url: string;
  size: number;
  platform: string;
}

export interface IActionAnnouncement {
  id?: number
  packageId?: number
  packageNo: string
  packageName: string
  title: string
  winBidder: string
  makeType: string
  content?: string
  contentAttach?: IAttachmentItem
  bulletinAttachs?: IAttachmentItem[]
}

export const saveTempAnnouncement = (reqData: IActionAnnouncement) => {
  return service.post('/purchase/winBulletin/saveTemp', reqData)
}

export const saveAnnouncement = (reqData: IActionAnnouncement) => {
  return service.put('/purchase/winBulletin/saveSubmit', reqData)
}

export const getAnnouncementInfo = (id: number) => {
  return service.get(`/purchase/winBulletin/${id}`)
}