<template>
  <div class="compare">
    <vxe-table
      :data="state.dataList"
      border
      :scroll-x="{ enabled: true, gt: 20 }"
      :scroll-y="{ enabled: true, gt: 20 }"
      max-height="500"
      show-footer
      :footer-method="state.footerMethod"
    >
      <vxe-column type="seq" width="60" title="序号"></vxe-column>
      <vxe-column field="materialName" title="产品名称" min-width="100"></vxe-column>
      <vxe-column
        field="materialSpec"
        title="产品型号（规格参数）"
        min-width="100"
      ></vxe-column>
      <vxe-column field="materialUnit" title="单位" min-width="100"></vxe-column>
      <vxe-column field="materialQuantity" title="数量" min-width="100"></vxe-column>
      <vxe-colgroup
        :title="item.bidderName"
        v-for="item in state.tableColumn"
        :key="item.bidderId"
      >
        <vxe-column :field="item.priceField" title="单价（元）" width="100"></vxe-column>
        <vxe-column
          :field="item.totalField"
          title="含税总价（元）"
          width="120"
        ></vxe-column>
      </vxe-colgroup>
    </vxe-table>
  </div>
</template>
<script setup lang="ts">
import { getQuotationCompareList, IGetQuotationCompareList } from "@/api/purchase/plan";
import { watch, reactive, onMounted } from "vue";
import { VxeTablePropTypes } from "vxe-table";

//#region props相关
interface IProps {
  activeName: string;
  packageId?: number;
  roundNo?: number;
}
const props = withDefaults(defineProps<IProps>(), {
  activeName: "quotation",
  roundNo: 1,
});
//#endregion

interface ITableItem {
  bidderId: number;
  bidderName: string;
  priceField: string;
  totalField: string;
}

interface IState {
  initTable: boolean;
  resData: any[];
  dataList: any[];
  tableColumn: ITableItem[];
  footerMethod: VxeTablePropTypes.FooterMethod;
  mergeFooterItems: VxeTablePropTypes.MergeFooterItems;
  tbQuotationVOList: [];
}

const state = reactive<IState>({
  initTable: true,
  resData: [],
  dataList: [],
  tableColumn: [],
  mergeFooterItems: [
    { row: 0, col: 1, rowspan: 1, colspan: 2 },
    { row: 1, col: 1, rowspan: 1, colspan: 2 },
  ],
  footerMethod: ({ columns, data }) => {
    const footerData = [
      columns.map((column, _columnIndex) => {
        if (["materialName"].includes(column.field)) {
          return "优惠金额（元）";
        }
        return null;
      }),
      columns.map((column, _columnIndex) => {
        if (["materialName"].includes(column.field)) {
          return "总报价合计（元）";
        }
        return null;
      }),
    ];
    return footerData;
  },
  tbQuotationVOList: [],
});

watch(
  () => props.activeName,
  () => {
    if (props.activeName === "compare" && props.packageId) {
      GetQuotationCompareList();
    }
  }
);

// onMounted(() => {
//   GetQuotationCompareList();
// });

const GetQuotationCompareList = () => {
  let reqData: IGetQuotationCompareList = {
    packageId: props.packageId,
    roundNo: props.roundNo,
  };
  state.initTable = false;
  getQuotationCompareList(reqData).then((res: any) => {
    console.info(111111111111);
    console.info(res);
    if (res.code === 0) {
      let newTableColumn = [];
      if (res.data.tbPackageMaterialVOList.length > 0) {
        let newTbQuotationDetailVOList =
          res.data.tbPackageMaterialVOList[0]?.tbQuotationDetailVOList ?? [];
        for (let i = 0; i < newTbQuotationDetailVOList.length; i++) {
          newTableColumn.push({
            bidderId: newTbQuotationDetailVOList[i].bidderId,
            bidderName: newTbQuotationDetailVOList[i].bidderName,
            priceField: `price${newTbQuotationDetailVOList[i].bidderId}`,
            totalField: `total${newTbQuotationDetailVOList[i].bidderId}`,
          });
        }
        state.tableColumn = newTableColumn;
        let newDataList = [];
        newDataList = res.data.tbPackageMaterialVOList.map((item: any) => {
          let newTbQuotationDetailVOList = item.tbQuotationDetailVOList;
          let newDataItem = {};
          for (let i = 0; i < newTbQuotationDetailVOList.length; i++) {
            // @ts-ignore
            newDataItem[`price${newTbQuotationDetailVOList[i].bidderId}`] =
              newTbQuotationDetailVOList[i].unitPrice;
            // @ts-ignore
            newDataItem[`total${newTbQuotationDetailVOList[i].bidderId}`] =
              newTbQuotationDetailVOList[i].totalPrice;
          }

          return {
            materialId: item?.materialId ?? void 0,
            materialNo: item?.materialNo ?? "",
            materialName: item?.materialName ?? "",
            materialSpec: item?.materialSpec ?? "",
            materialUnit: item?.materialUnit ?? "",
            materialQuantity: item.materialQuantity,
            // materialType: item?.materialType ?? "",
            ...newDataItem,
          };
        });
        state.dataList = newDataList;
        state.tbQuotationVOList = res.data?.tbQuotationVOList ?? [];
        let newFooterMethod: VxeTablePropTypes.FooterMethod = ({ columns, data }) => {
          let tbQuotationVOList = JSON.parse(JSON.stringify(state.tbQuotationVOList));
          const footerData = [
            columns.map((column, _columnIndex) => {
              if (["materialName"].includes(column.field)) {
                return "优惠金额（元）";
              }
              for (let i = 0; i < newTbQuotationDetailVOList.length; i++) {
                let item = newTbQuotationDetailVOList[i];
                let index = tbQuotationVOList.findIndex(
                  (item2: any) => item2.bidderId == item.bidderId
                );
                if (index !== -1) {
                  if ([`total${item.bidderId}`].includes(column.field)) {
                    return tbQuotationVOList[index].discountAmount;
                  }
                }
              }
              return null;
            }),
            columns.map((column, _columnIndex) => {
              if (["materialName"].includes(column.field)) {
                return "总报价合计（元）";
              }
              for (let i = 0; i < newTbQuotationDetailVOList.length; i++) {
                let item = newTbQuotationDetailVOList[i];
                let index = tbQuotationVOList.findIndex(
                  (item2: any) => item2.bidderId == item.bidderId
                );
                if (index !== -1) {
                  if ([`total${item.bidderId}`].includes(column.field)) {
                    return tbQuotationVOList[index].quotationPrice;
                  }
                }
              }
              return null;
            }),
          ];
          return footerData;
        };
        state.footerMethod = newFooterMethod;
      }

      state.mergeFooterItems = [
        { row: 0, col: 1, rowspan: 1, colspan: 2 },
        { row: 1, col: 1, rowspan: 1, colspan: 2 },
      ];
    }
  });
};
</script>
<style lang="scss" scoped></style>
