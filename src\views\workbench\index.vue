<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-06-19 15:39:37
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-06-28 15:19:46
-->
<template>

 <div class="workbench">
  <div class="workbench-lft">
    <div class="workbench-title">审批</div>
    <div class="workbench-panel">
      <div class="workbench-panel-item" :class="activeTab=='1'?'workbench-panel-item-active':''" @click="changeTab(1)"><svg-icon size="24" icon="icon-tongyongshenpi"></svg-icon> 通用审批</div>
      <div class="workbench-panel-item" :class="activeTab=='2'?'workbench-panel-item-active':''" @click="changeTab(2)"><svg-icon size="24" icon="icon-qingjia"></svg-icon> 请假</div>
      <div class="workbench-panel-item" :class="activeTab=='3'?'workbench-panel-item-active':''" @click="changeTab(3)"><svg-icon size="24" icon="icon-feiyongbaoxiao"></svg-icon> 费用报销</div>
      <div class="workbench-panel-item" :class="activeTab=='4'?'workbench-panel-item-active':''" @click="changeTab(4)"><svg-icon size="24" icon="icon-fukuanshenqing"></svg-icon> 付款申请</div>
      <div class="workbench-panel-item" :class="activeTab=='5'?'workbench-panel-item-active':''" @click="changeTab(5)"><svg-icon size="24" icon="icon-caigoujihua"></svg-icon> 采购计划</div>
      <div class="workbench-panel-item" :class="activeTab=='6'?'workbench-panel-item-active':''" @click="changeTab(6)"><svg-icon size="24" icon="icon-hetongshenpi"></svg-icon> 合同审批</div>
    </div>
    <el-divider />
    <div class="workbench-comps">
      <el-scrollbar style="height: calc(100vh - 300px);">
        <!-- 通用审批 -->
        <general v-if="activeTab == '1'" />
        <!-- 请假 -->
        <leave v-if="activeTab == '2'" />
        <!-- 费用报销 -->
        <expenseReim v-if="activeTab == '3'" />
        <!-- 付款申请 -->
        <payment v-if="activeTab == '4'" />
        <!-- 采购计划 -->
        <purchase v-if="activeTab == '5'" :id="purchaseId"/>
        <!-- 合同审批 -->
        <contract v-if="activeTab == '6'" />
      </el-scrollbar>
    </div>
  </div>
  <!-- <div class="workbench-rht">Workbench</div> -->
 </div>

</template>

<script setup lang='ts'>
import { ref,onMounted } from 'vue';
import contract from './components/contract.vue';
import expenseReim from './components/expenseReim.vue';
import general from './components/general.vue';
import leave from './components/leave.vue';
import payment from './components/payment.vue';
import purchase from './components/purchase.vue';
import { useRoute } from "vue-router";
const route = useRoute();


const activeTab = ref('1')
const purchaseId = ref(0)
onMounted(() => {
  console.info(11111111)
  console.info(route.query)
  if (route.query.activeTab) {
    if(route.query.activeTab=='5'&&route.query.type==='update'){
      purchaseId.value=parseInt(route.query.id)
    }
    activeTab.value = route.query.activeTab
  }
});
const changeTab = (type) => {
  activeTab.value = type
}

</script>

<style scoped lang="scss">
.workbench{
  display: flex;
  &-title{
    color: #409eff;
    text-align: center;
    padding: 10px 0;
    width: 80px;
    border-bottom: 3px solid #409eff;
  }
  &-lft{
    width: 100%;
    margin-right: 20px;
    padding: 10px 20px;
    box-sizing: border-box;
    background-color: white;
  }
  &-rht{
    // width: calc( 40% - 20px);
    padding: 10px 20px;
    box-sizing: border-box;
    background-color: white;
  }
  &-panel{
    padding: 20px 0 0;
    display: flex;
    flex-wrap: wrap;
    .middle{vertical-align:middle;margin-right: 10px;}
    &-item{
      width: 120px;
      padding: 15px;
      text-align: center;
      border: 1px solid rgb(230, 230, 230);
      border-radius: 4px;
      cursor: pointer;
      margin-right: 20px;
    }
    &-item-active{
      border-color:#409eff;
    }
  }
  &-comps{}
}
.svg-icon{
  vertical-align: middle;
  margin-right: 10px;
}
</style>