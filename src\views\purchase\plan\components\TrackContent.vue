<template>
  <div class="track">
    <div class="track-html" v-if="state.makeType == 1">
      <div v-html="state.content"></div>
    </div>
    <div class="track-pdf" v-else>
      <vue-office-pdf :src="state.contentAttachUrl" />
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  getContract,
  getPurchaseAnnouncement,
  getWinBulletin,
  getWinResultNotice,
} from "@/api/purchase/plan";
import { watch, reactive, onMounted } from "vue";
import VueOfficePdf from "@vue-office/pdf";

interface IProps {
  id?: number;
  packageType: string;
}

const props = withDefaults(defineProps<IProps>(), {
  id: void 0,
  packageType: "01",
});

interface IState {
  makeType: number;
  content: string;
  contentAttachUrl: string;
}

const state = reactive<IState>({
  makeType: 1,
  content: "",
  contentAttachUrl: "",
});

const init = () => {
  switch (props.packageType) {
    case "01":
      //采购公告
      getPurchaseAnnouncement(props.id).then((res) => {
        console.log(res);
        state.makeType = res.data.makeType;
        state.content = res.data.content;
        state.contentAttachUrl = res.data?.contentAttach?.url ?? "";
      });
      break;
    case "02":
      //变更公告

      break;
    case "06":
      //中标公告
      getWinBulletin(props.id).then((res) => {
        console.log(res);
        state.makeType = res.data.makeType;
        state.content = res.data.content;
        state.contentAttachUrl = res.data?.contentAttach?.url ?? "";
      });
      break;
    case "07":
      //中标通知书
      getWinResultNotice(props.id).then((res) => {
        console.log(res);
        state.makeType = 1;
        state.content = res.data.content;
      });

      break;
    case "08":
      //采购合同
      getContract(props.id).then((res) => {
        state.makeType = res.data.makeType;
        state.content = res.data.content;
        state.contentAttachUrl = res.data?.contentAttach?.url ?? "";
      });
      break;
    default:
      break;
  }
};

onMounted(() => {
  if (props.id) {
    init()
  }
});

watch(
  () => [props.id, props.packageType],
  () => {
    if (props.id) {
      init()
    }
  }
);
</script>

<style lang="scss" scoped>
.track {
  &-pdf {
    max-height: 75vh;
    overflow-y: auto;
  }
}
</style>
