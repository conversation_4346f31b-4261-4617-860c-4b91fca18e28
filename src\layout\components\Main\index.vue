<template>
	<el-scrollbar>
		<el-main class="layout-main">
			<el-scrollbar class="layout-scrollbar">
				<div class="layout-card" :style="layoutMainHeight">
					<router-view v-slot="{ Component, route }">
						<keep-alive v-if="theme.isTabsCache" :include="[...tabsStore.cachedViews]">
							<component :is="Component" :key="route.fullPath" />
						</keep-alive>
						<component :is="Component" v-else :key="route.name" />
					</router-view>
				</div>
			</el-scrollbar>
		</el-main>
	</el-scrollbar>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router'
import { computed,onMounted,ref,h } from 'vue'
import { useAppStore } from '@/store/modules/app'
import { useTabsStore } from '@/store/modules/tabs'
import { WebSocketService } from '@/hooks/websocket';
import cache from "@/utils/cache";
import { useUserStore } from "@/store/modules/user";
import { ElNotification } from "element-plus/es";
import remind1 from '@/assets/image/remind1.png'
import remind2 from '@/assets/image/remind2.png'
import { StringLiteral } from '@babel/types';

const userStore = useUserStore();
const appStore = useAppStore()
const tabsStore = useTabsStore()
const theme = computed(() => appStore.theme)

const notify = ref({})


const layoutMainHeight = computed(() => {
	if (!theme.value.isTabsView) {
		return 'min-height: calc(100vh - var(--theme-header-height) - 30px) !important'
	} else {
		return ''
	}
});

onMounted(() => {
const handleMessage = (msg: string) => {
  let type=msg=='还有5分钟就要上班了，别忘记打卡哦~'?'1':'2'
  let timePart = Date.now().toString().slice(-4); // 取最后四位数字
  commutingReminder(type,timePart)
 };
const wsService = new WebSocketService(import.meta.env.VITE_WEBSOCKET+`/yuan-oa/ws/${userStore.user.id}?access_token=${cache.getToken()}`,handleMessage);

});

const turnOffReminder= (id:string)=>{
  notify.value[id].close()
}

const commutingReminder = (type:string,id:string) => {
  // let HTML=`<div><img class="imgStyle" src="${type=='1'?remind1:remind2}" alt=""> <div class="operatingButton" onclick="${turnOffReminder}"><span type="text">关闭本次提醒</span></div></div>`
 notify.value[id] = ElNotification({
    showClose: false,
    dangerouslyUseHTMLString: true,
    duration: 0,
    customClass: 'commutingReminderStyle',
    // message: HTML,
    message: h('div', [
          h(
            "img",
            {
              class:'imgStyle',
              src:type=='1'?remind1:remind2,
              alt:''
            },
          ),
          h(
            "div",
            {
              class:'operatingButton',
              onClick: ()=>turnOffReminder(id)
            },
            [
            h(
              "span",
              "关闭本次提醒"
            ),
            ]
        )])
  });
}

</script>
<style lang="scss">
.commutingReminderStyle{
  -webkit-user-select: none;  /* Safari */
  -moz-user-select: none;     /* Firefox */
  -ms-user-select: none;      /* IE/Edge */
  user-select: none;
  padding: 0 !important;
  .imgStyle{
    width:100%;
    height: 140px;
    pointer-events: none;
  }
  .operatingButton{
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid #EAEBED;
    margin-top: 10px;
    cursor: pointer;
    span{
      color: #007FFF;
    }
  }
}
.commutingReminderStyle .el-notification__group{
  margin: 0 !important;
  width: 100%;
}
</style>
