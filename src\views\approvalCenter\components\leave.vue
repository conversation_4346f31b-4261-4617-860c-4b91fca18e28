<template>

  <div class="detail-ctr" v-if="props.alldata.oaLeaveFlowInfoVO">
   <div class="detail-ctr-label">请假类型</div>
   <div class="detail-ctr-txt">{{props.alldata.oaLeaveFlowInfoVO.typeName}}</div>
   <div class="detail-ctr-label">开始时间</div>
   <div class="detail-ctr-txt">{{props.alldata.oaLeaveFlowInfoVO.startTime}} 
    <span class="part" v-if="props.alldata.oaLeaveFlowInfoVO.startTimePeriod == 0">上午</span>
    <span class="part" v-if="props.alldata.oaLeaveFlowInfoVO.startTimePeriod == 1">下午</span>
   </div>
   <div class="detail-ctr-label">结束时间</div>
   <div class="detail-ctr-txt">{{props.alldata.oaLeaveFlowInfoVO.endTime}}
    <span class="part" v-if="props.alldata.oaLeaveFlowInfoVO.endTimePeriod == 0">上午</span>
    <span class="part" v-if="props.alldata.oaLeaveFlowInfoVO.endTimePeriod == 1">下午</span>
   </div>
   <div class="detail-ctr-label">时长</div>
   <div class="detail-ctr-txt">{{props.alldata.oaLeaveFlowInfoVO.days}}天</div>
   <div class="detail-ctr-label">请假事由</div>
   <div class="detail-ctr-txt">{{props.alldata.oaLeaveFlowInfoVO.flowRemark}}</div>
   <div class="detail-ctr-label">附件</div>
   <div class="detail-ctr-txt">
    <el-link class="file" :underline="false" type="primary" v-for="item in props.alldata.oaLeaveFlowInfoVO.attachmentList"
    @click="downloadFile(item.url,item.name)">{{item.name}}</el-link>
   </div>
  </div>
 
 </template>
 
 <script setup lang='ts'>
 import { downloadFile } from "@/utils/tool";
import { IView } from '../types';

 interface IProps {
  id: number;
  alldata: IView;
}

const props = withDefaults(defineProps<IProps>(), {
  id: 0,
  alldata: {
    oaLeaveFlowInfoVO: {
      holidayRuleId:'',
      startTime:'',
      endTime:'',
      startTimePeriod:'',
      endTimePeriod:'',
      days:'',
      flowRemark: '',
      attachmentList: [],
    }
  }
})
 
 </script>
 
 <style scoped lang='scss'>
 .detail-ctr{
   &-label{
     color: #a2a2a2;
     padding: 0 12px 0 0;
     margin: 0 0 8px 0;
     line-height: 22px;
   }
   &-txt{
     margin-bottom: 15px;
     .file{display: block;line-height: 1.8;}
   }
 }
 </style>