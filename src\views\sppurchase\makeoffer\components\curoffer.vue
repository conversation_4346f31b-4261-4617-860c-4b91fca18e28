<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-08 18:54:42
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-10 09:25:34
-->
<template>
  <el-dialog
    v-model="state.visible"
    title="当前轮次报价"
    width="60%"
    top="40px"
    :close-on-click-modal="false"
  >
    <div class="viewbox">
      <view-vue
        ref="viewVueRef"
        :packId="state.rowData.id"
        :roundNum="state.rowData.roundNo"
        :curRound="state.rowData.roundNo"
      ></view-vue>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.visible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, ref, nextTick } from "vue";
import { usebulletinListApi } from "@/api/pnotice";
import { useCrud } from "@/hooks";
import viewVue from "./view.vue";

const state = reactive({
  visible: false,
  dataListLoading: false,
  dataForm: {},
  rowData: {},
});
const viewVueRef = ref();

const init = (row) => {
  console.log(row);
  state.visible = true;
  Object.assign(state.rowData, { roundNo: row.roundNum, id: row.packageId });
  nextTick(() => {
    viewVueRef.value.init();
  });
};

defineExpose({
  init,
});
</script>
<style lang="scss" scoped></style>
