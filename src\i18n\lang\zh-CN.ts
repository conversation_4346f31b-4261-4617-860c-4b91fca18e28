export default {
	loading: '加载中...',
	add: '新增',
	delete: '删除',
	edit: '修改',
	query: '查询',
	export: '导出',
	handle: '操作',
	back: '返回',
	confirm: '确定',
	cancel: '取消',
	clear: '清除',
	close: '关闭',
	createTime: '创建时间',
	updateTime: '更新时间',
	required: '必填项不能为空',
	app: {
		title: 'E采智链',
		description:
			'基于Vue3、TypeScript、Element Plus、Vue Router、Pinia、Axios、i18n、Vite等开发的后台管理，使用门槛极低，采用MIT开源协议，完全免费开源且终生免费，可免费用于商业项目等场景！',
		logoText: 'E采智链',
		miniLogoText: 'E采智链',
		username: '用户名',
		password: '密码',
		captcha: '验证码',
		signIn: '登录',
		mobileSignIn: '手机登录',
		mobile: '手机号',
		signOut: '退出',
		large: '大型',
		default: '默认',
		small: '小型',
		close: '关闭当前',
		closeOthers: '关闭其它',
		closeAll: '关闭全部'
	},
	settings: {
		title: '布局设置',
		sidebarDark: '暗色侧边栏',
		sidebarLight: '亮色侧边栏',
		navbarLight: '亮色顶栏',
		navbarTheme: '主题色顶栏',
		layout: '布局切换',
		vertical: '纵向',
		columns: '分栏',
		transverse: '横向',
		interface: '界面设置',
		dark: '开启暗黑模式',
		uniqueOpened: '侧栏排他展开',
		logo: '开启LOGO',
		breadcrumb: '开启面包屑',
		tabs: '开启标签页',
		tabsCache: '开启标签页缓存',
		tabsStyle: '标签页风格',
		tips: '设置之后仅是临时生效，要想永久生效，需点击下方的 "复制配置" 按钮，将配置替换到 store/theme/config.ts 中。',
		copyConfig: '复制配置',
		reset: '恢复默认',
		copySuc: '复制成功',
		style1: '风格1',
		style2: '风格2'
	},
	error: {
		email: '邮箱格式不正确',
		password: '密码不能小于{len}位数'
	},
	router: {
		home: '首页',
		profile: '个人中心',
		profilePassword: '修改密码'
	}
}
