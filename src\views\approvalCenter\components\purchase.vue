<template>
  <div class="detail-ctr" v-if="props.alldata.oaPackageVo">
    <div class="detail-ctr-label">项目</div>
    <div class="detail-ctr-txt">{{ props.alldata.oaPackageVo.projectName }}</div>
    <div class="detail-ctr-label">采购计划编号</div>
    <div class="detail-ctr-txt">{{ props.alldata.oaPackageVo.packageNo }}</div>
    <div class="detail-ctr-label">采购计划名称</div>
    <div class="detail-ctr-txt">{{ props.alldata.oaPackageVo.packageName }}</div>
    <div class="detail-ctr-label">本次预算金额（元）</div>
    <div class="detail-ctr-txt">{{ props.alldata.oaPackageVo.packageBudget }}</div>
    <div class="detail-ctr-label">采购方式</div>
    <div class="detail-ctr-txt">
      <span
        v-html="getDictLabelList('package_type', props.alldata.oaPackageVo.packageType)"
      ></span>
    </div>
    <div class="detail-ctr-label">采购类别</div>
    <div class="detail-ctr-txt">
      <span
        v-html="getDictLabelList('package_mode', props.alldata.oaPackageVo.packageMode)"
      ></span>
    </div>
    <div class="detail-ctr-label">申请部门</div>
    <div class="detail-ctr-txt">{{ props.alldata.oaPackageVo.orgName }}</div>
    <div class="detail-ctr-label">项目经理</div>
    <div class="detail-ctr-txt">{{ props.alldata.oaPackageVo.managerName }}</div>
    <div class="detail-ctr-label">送货地址</div>
    <div class="detail-ctr-txt">{{ props.alldata.oaPackageVo.deliveryAddress }}</div>
    <div class="detail-ctr-label">采购说明</div>
    <div class="detail-ctr-txt">
      <WangEditor
        v-model="props.alldata.oaPackageVo.comment"
        :disabled="true"
      ></WangEditor>
    </div>
    <div class="detail-ctr-label">采购清单</div>
    <div class="detail-ctr-label">
      <el-table
        show-overflow-tooltip
        :data="props.alldata.oaPackageVo.materialVOS"
        border
      >
        <el-table-column
          type="index"
          label="序号"
          header-align="center"
          align="center"
          width="70"
        ></el-table-column>
        <el-table-column
          prop="materialName"
          label="产品名称"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="materialType"
          label="产品型号（规格参数）"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="materialUnit"
          label="计量单位"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="materialQuantity"
          label="采购数量"
          header-align="center"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="comment"
          label="备注"
          header-align="center"
          align="center"
        ></el-table-column>
      </el-table>
    </div>
    <div class="detail-ctr-label">附件</div>
    <div class="detail-ctr-txt">
      <el-link
        class="file"
        :underline="false"
        type="primary"
        v-for="item in props.alldata.oaPackageVo.attachmentList"
        @click="downloadFile(item.url, item.name)"
        >{{ item.name }}</el-link
      >
    </div>
    <div class="detail-ctr-txt">
      <el-link class="file" :underline="false" type="primary">文档下载</el-link>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { downloadFile, getDictLabelList } from "@/utils/tool";
import WangEditor from "@/components/wang-editor/index.vue";
import { IView } from "../types";
interface IProps {
  id: number;
  alldata: IView;
}
const props = withDefaults(defineProps<IProps>(), {
  id: 0,
  alldata: {
    oaPackageVo: {
      id: void 0,
      projectName: "",
      packageNo: "",
      packageName: "",
      packageBudget: void 0,
      packageType: "",
      packageTypeLabel: "",
      packageMode: "",
      packageModeLabel: "",
      orgName: "",
      managerName: "",
      deliveryAddress: "",
      comment: "",
      attachmentList: [],
      materialVOS: [],
    },
  },
});
</script>

<style scoped lang="scss">
.detail-ctr {
  &-label {
    color: #a2a2a2;
    padding: 0 12px 0 0;
    margin: 0 0 8px 0;
    line-height: 22px;
  }
  &-txt {
    margin-bottom: 15px;
    .file {
      display: block;
      line-height: 1.8;
    }
  }
}
</style>
