import service from '@/utils/request';

export const exportMonthAttendanceStatistics = (reqData: any) => {
  return service.get('/work/monthAttendanceStatistics/export', { responseType: 'blob', params: reqData })
}

export const findHolidayByDate = (params: any) => {
  return service.get('/work/monthAttendanceStatistics/findHolidayByDate', {params})
}

export const findClockRecordByDate = (params: any) => {
  return service.get('/work/monthAttendanceStatistics/findClockRecordByDate', {params})
}