<template>
  <el-drawer
    v-model="state.show"
    title="中标结果通知书"
    :size="850"
    class="picketage_enter_detail_drawer"
    @close="onClickClose"
  >
    <el-card>
      <div class="detail">
        <el-form
          ref="dataFormRef"
          :model="state.dataForm"
          label-width="140px"
          class="detail-form"
        >
          <el-form-item prop="packageNo" label="采购计划编号">
            <div class="detail-form-value">
              {{ state.dataForm.packageNo }}
            </div>
          </el-form-item>
          <el-form-item prop="packageName" label="采购计划名称">
            <div class="detail-form-value">
              {{ state.dataForm.packageName }}
            </div>
          </el-form-item>
          <el-form-item prop="title" label="通知书标题">
            <div class="detail-form-value">
              {{ state.dataForm.title }}
            </div>
          </el-form-item>
          <el-form-item prop="winBidder" label="中标人">
            <div class="detail-form-value">
              {{ state.dataForm.winBidder }}
            </div>
          </el-form-item>
          <el-form-item prop="content" label="通知书内容">
            <div class="detail-form-value">
              <WangEditor v-model="state.dataForm.content" :disabled="true"></WangEditor>
            </div>
          </el-form-item>
          <el-form-item prop="contactPhone" label="附件">
            <div class="detail-form-list">
              <div
                class="detail-form-list-item"
                v-for="item in state.dataForm.attachmentList"
              >
                <div class="detail-form-list-item-text">
                  {{ item.name }}
                </div>
                <div class="detail-form-list-item-action">
                  <el-icon class="action-icon" @click="onClickDownload(item)"
                    ><Download
                  /></el-icon>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </el-drawer>
</template>
<script setup lang="ts">
import { reactive, watch } from "vue";
import WangEditor from "@/components/wang-editor/index.vue";
import { getWinResultInfo } from "@/api/picketage/notice";
import service from "@/utils/request";
import { Download } from "@element-plus/icons-vue";

interface IProps {
  id?: number;
  show: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
});

interface IAttachmentItem {
  name: string;
  url: string;
  platform: string;
  size: number;
}

interface IDataForm {
  packageNo: string;
  packageName: string;
  title: string;
  winBidder: string;
  content: string;
  attachmentList: IAttachmentItem[];
}

interface IState {
  show: boolean;
  dataForm: IDataForm;
}

const state = reactive<IState>({
  show: false,
  dataForm: {
    packageNo: "",
    packageName: "",
    title: "",
    winBidder: "",
    content: "",
    attachmentList: [],
  },
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      state.show = true;
      if (props.id) {
        GetWinResultInfo(props.id);
      }
    }
  }
);

const GetWinResultInfo = (id: number) => {
  getWinResultInfo(id).then((res: any) => {
    if (res.code === 0) {
      state.dataForm.packageNo = res.data.packageNo;
      state.dataForm.packageName = res.data.packageName;
      state.dataForm.title = res.data.title;
      state.dataForm.winBidder = res.data.winBidder;
      state.dataForm.content = res.data.content;
      state.dataForm.attachmentList = res.data.attachmentList.map((item: any) => {
        return {
          name: item.name,
          url: item.url,
          platform: item.platform,
          size: item.size,
        };
      });
    }
  });
};

const emit = defineEmits<{
  (e: "on-close"): void;
}>();

const onClickClose = () => {
  state.show = false;
  emit("on-close");
};

const onClickDownload = async (item: IAttachmentItem) => {
  const response = await service.get(item.url, {
    responseType: "blob", // important
  });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", item.name);
  document.body.appendChild(link);
  link.click();
};
</script>

<style lang="scss">
.picketage_enter_detail_drawer {
  .el-drawer__body {
    padding: 14px !important;
    background: #f5f6fa !important;
  }
}
</style>
<style lang="scss" scoped>
.detail {
  &-form {
    &-value {
      color: #000;
    }
    &-list {
      &-item {
        display: flex;
        align-items: center;
        &-text {
          color: #409eff;
          cursor: pointer;
        }
        &-action {
          color: #545252;
          cursor: pointer;
          .action-icon {
            margin-left: 4px;
          }
        }
      }
    }
  }
}
</style>
