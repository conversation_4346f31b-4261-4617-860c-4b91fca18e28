<template>
  <el-dialog
    v-model="state.show"
    title="查看"
    :close-on-click-modal="false"
    draggable
    width="1000px"
    @close="onClose"
  >
    <div class="project">
      <div class="content">
        <div class="content-chart" id="chart" ref="myChart"></div>
      </div>
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, watch, onMounted, onUnmounted, nextTick, ref } from "vue";
import * as echarts from 'echarts'
import { getMaterialPriceCurve } from '@/api/material/category'


interface IProps {
  show: boolean;
  id: number;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  id: 0,
});

interface IState {
  show: boolean;
  echarts: any;
}
const myChart = ref()
const state = reactive<IState>({
  show: false,
  echarts: void 0,
});


const emit = defineEmits<{
  (e: "on-close"): void;
}>();

const onClose = () => {
  state.show = false;
  emit("on-close");
};

const getPriceCurve = (id) => {
  state.echarts = echarts.init(myChart.value);
  getMaterialPriceCurve(id).then((res: any) => {
    if (res.code === 0) {
      initEcharts(res.data.timeCurve, res.data.priceCurve);
    }
  });
};

const initEcharts = (timeCurve: string[], priceCurve: number[]) => {
  state.echarts.setOption({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: timeCurve
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "价格（元）",
      }
    ],
    series: [
      {
        name: "价格",
        type: "line",
        label: {
          show: true,
          position: 'top'
        },
        itemStyle: {
          color: "#00C29A",
        },
        data: priceCurve
      }
    ]
  })
};

onUnmounted(() => {
  if (state.echarts) {
    state.echarts.dispose();
  }
});


watch(
  props,
  () => {
    if (props.show) {
      state.show = true;
      nextTick(() => {
        getPriceCurve(props.id)
      });
    }
  }
);
</script>
<style lang="scss" scoped>
.project {
  min-height: 450px;
}
#chart{
  width: 100%;
  height: 400px;
}
</style>
