<template>
  <el-dialog
    v-model="state.visible"
    title="参与信息"
    width="60%"
    :close-on-click-modal="false"
  >
    <div class="notice-search">
      <el-form :model="state.dataForm" label-width="140px" class="elform" ref="elformRef">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="供应商名称">
              <span>{{ state.dataForm.bidderName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="统一社会信用代码">
              <span>{{ state.dataForm.creditCode }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人">
              <span>{{ state.dataForm.contactName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话">
              <span>{{ state.dataForm.contactPhone }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邮编">
              <span>{{ state.dataForm.postalCode }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系地址">
              <span>{{ state.dataForm.businessAddress }}</span>
            </el-form-item> 
          </el-col>
        </el-row>
        <el-form-item label="保证金附件">
          <div class="list">
            <Fragment v-for="i in state.dataForm.attachmentList">
              <el-link type="primary" class="block"  @click="downloadFile(i.url,i.name)">{{ i.name }}</el-link>
            </Fragment>
          </div>
        </el-form-item>
      </el-form>
      <el-alert title="参与说明：" type="warning" :closable="false"  />
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="state.visible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive,ref } from "vue"
import { useRegistrationByIdApi } from '@/api/pnotice'
import { ElMessage } from 'element-plus'
import { downloadFile } from '@/utils/tool'

const state = reactive({
  visible: false,
  uploadList:[],
  fileList:[],
  nowId:'',
  dataForm: {
    bidderName:'',
    contactName: "",
    contactPhone: "",
    creditCode: "",
    businessAddress: "",
    postalCode: "",
    attachmentList:[],
	}
})
const elformRef = ref()

const init = (id)=>{
  state.visible = true
  state.nowId = id
  getDetail()
}
// 详情
const getDetail = () => {
  useRegistrationByIdApi(state.nowId).then((res) => {
    if(res.code == 0){
      Object.assign(state.dataForm, res.data)
    }
  })
}

defineExpose({
	init
})
</script>
<style lang="scss" scoped>
.elform{width: 95%;}
.ctr{text-align: center;margin: 5px 0;}
.block{display:block;}
</style>
