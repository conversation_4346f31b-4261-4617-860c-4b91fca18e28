<template>
  <div class="bid">
    <div class="bid-title">
      <HomeTitle icon="icon-daikaibiaoxiangmu" title="开标项目"> </HomeTitle>
    </div>
    <div class="bid-content">
      <el-calendar ref="refCalendar" v-model="state.seleteDate">
        <template #header="{ date }">
          <div class="calendar-header">
            <div class="calendar-header-last-year" @click="onClickAction('prev-year')">
              <svgIcon icon="icon-doubleleft" class-name="svg-size" />
            </div>
            <div class="calendar-header-last-month" @click="onClickAction('prev-month')">
              <svgIcon icon="icon-left" class-name="svg-size" />
            </div>
            <div class="calendar-header-value">
              {{ date }}
            </div>
            <div class="calendar-header-next-month" @click="onClickAction('next-month')">
              <svgIcon icon="icon-right" class-name="svg-size" />
            </div>
            <div class="calendar-header-next-year" @click="onClickAction('next-year')">
              <svgIcon icon="icon-doubleright" class-name="svg-size" />
            </div>
          </div>
        </template>
        <template #date-cell="{ data }">
          <div class="calendar-content">
            {{ dayjs(data.day).format("DD") }}
            <div
              class="calendar-content-point"
              v-if="state.bidDateList.includes(dayjs(data.day).format('YYYY-MM-DD'))"
            ></div>
          </div>
        </template>
      </el-calendar>
    </div>
    <CalendarProjectList
      :show="state.detailShow"
      :select-date="state.seleteDate"
      @on-close="() => (state.detailShow = false)"
    ></CalendarProjectList>
  </div>
</template>
<script setup lang="ts">
import { getSuppelierStatistics } from "@/api/home";
import svgIcon from "@/components/svg-icon";
import { onMounted, reactive, ref, watch } from "vue";
import HomeTitle from "./HomeTitle.vue";
import dayjs from "dayjs";
import { CalendarDateType, CalendarInstance } from "element-plus";
import CalendarProjectList from "./CalendarProjectList.vue";

const refCalendar = ref<CalendarInstance>();

interface IState {
  seleteDate: Date;
  seleteDateModel: Date;
  bidDateList: string[];
  clickDate: boolean;
  detailShow: boolean;
}

const state = reactive<IState>({
  seleteDate: new Date(),
  seleteDateModel: new Date(),
  bidDateList: [],
  clickDate: true,
  detailShow: false,
});

onMounted(() => {
  GetBidStatistics(dayjs().format("YYYY"), dayjs().format("MM"));
});

const GetBidStatistics = (year: string, month: string) => {
  getSuppelierStatistics(year, month).then((res: any) => {
    console.log(res);
    if (res.code === 0) {
      let newBidDateList = (res.data ?? []).map((item: any) => {
        return dayjs(item).format("YYYY-MM-DD");
      });
      state.bidDateList = newBidDateList;
    }
  });
};

const onClickAction = (type: CalendarDateType) => {
  state.clickDate = false;
  refCalendar.value?.selectDate(type);
};

watch(
  () => state.seleteDate,
  () => {
    if ( state.clickDate ) {
      state.seleteDateModel = state.seleteDate;
      state.detailShow = true;
    } else {
      state.clickDate = true;
    }
  }
)

</script>
<style lang="scss" scoped>
.bid {
  background-color: #ffffff;
  padding: 16px 10px;
  border-radius: 4px;
  .calendar-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    &-last-year {
      margin-right: 8px;
      cursor: pointer;
    }
    &-last-month {
      margin-right: 8px;
      cursor: pointer;
    }
    &-next-year {
      margin-left: 8px;
      cursor: pointer;
    }
    &-next-month {
      margin-left: 8px;
      cursor: pointer;
    }
  }
  .calendar-content {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    &-point {
      position: absolute;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #ff5918;
      bottom: -10px;
    }
  }
  &-content {
    :deep(.el-calendar-day) {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
