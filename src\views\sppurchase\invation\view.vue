<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-02 16:25:21
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-06-13 14:49:40
-->
<template>
  <el-drawer
    v-model="state.visible"
    title="查看邀请公告"
    size="60%"
    :close-on-click-modal="false"
  >
    <el-form ref="infoRef" :model="state.dataForm" label-width="170px">
      <div class="action_base">
        <div class="action_title">
          <ContentTitle title="基本信息"></ContentTitle>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="采购计划">
              <span>{{ state.dataForm.packageName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购计划编号">
              <span>{{ state.dataForm.packageNo }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="邀请函名称">
              <span>{{ state.dataForm.title }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购模式">
              <span>{{ state.dataForm.packageModeLabel }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报名开始时间">
              <span>{{ state.dataForm.signStartDate }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报名截止时间">
              <span>{{ state.dataForm.signEndDate }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="投标开始时间">
              <span>{{ state.dataForm.bidStartDate }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="投标截止时间">
              <span>{{ state.dataForm.bidEndDate }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="项目所在地">
              <span>{{ state.dataForm.supplyAddress }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="送货地址">
              <span>{{ state.dataForm.deliveryAddress }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="是否报名审核">
              <span>{{ state.dataForm.signFlag == "1" ? "是" : "否" }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="是否内部审核">
              <span>{{ state.dataForm.needAudit == "1" ? "是" : "否" }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="投标保证金">
              <span>{{ state.dataForm.bidbondFlag == "1" ? "收取" : "不收取" }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="state.dataForm.bidbondFlag == 1">
            <el-form-item label="投标保证金金额（元）">
              <span>{{ state.dataForm.bidbond }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="邀请函内容" class="noflex">
              <div>{{ state.dataForm.makeTypeLabel }}</div>
              <WangEditor v-model="state.dataForm.content" :disabled="true"></WangEditor>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件">
              <Fragment v-if="state.dataForm.contentAttach">
                <el-link
                  type="primary"
                  @click="
                    downloadFile(
                      state.dataForm.contentAttach.url,
                      state.dataForm.contentAttach.name
                    )
                  "
                  >{{ state.dataForm.contentAttach.name }}</el-link
                >
              </Fragment>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="action_purchase">
        <div class="action_title">
          <ContentTitle title="采购人信息"></ContentTitle>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="采购单位名称">
              <span>{{ state.dataForm.buyersName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采购单位地址">
              <span>{{ state.dataForm.buyersAddress }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人">
              <span>{{ state.dataForm.buyersLinkerName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话">
              <span>{{ state.dataForm.buyersLinkerTel }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <div class="action_title">
      <ContentTitle title="采购清单"></ContentTitle>
    </div>
    <el-table v-loading="state.dataListLoading" :data="state.materialList" border>
      <el-table-column
        type="index"
        label="序号"
        header-align="center"
        align="center"
        width="70"
      ></el-table-column>
      <el-table-column
        prop="materialName"
        label="产品名称"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="materialType"
        label="产品型号（规格参数）"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="materialUnit"
        label="计量单位"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="materialQuantity"
        label="采购数量"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column align="center" label="备注" prop="comment"></el-table-column>
    </el-table>
    <div class="files">
      <div class="label">附件：</div>
      <div class="list">
        <Fragment v-for="i in state.dataForm.bulletinAttachs">
          <el-link type="primary" class="block" @click="downloadFile(i.url, i.name)">{{
            i.name
          }}</el-link>
        </Fragment>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button class="btn" @click="state.visible = false">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { reactive } from "vue";
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import { useInvitationByPackageIdApi, useGetMaterialByPackIdApi } from "@/api/pnotice";
import { downloadFile } from "@/utils/tool";
import WangEditor from "@/components/wang-editor/index.vue";
import { ElMessage } from "element-plus";

const state = reactive({
  visible: false,
  nowId: "",
  dataForm: {
    packageId: "",
    packageNo: "",
    packageName: "",
    title: "",
    packageMode: "",
    supplyAddress: "",
    needAudit: 0,
    makeType: 1,
    content: "",
    signFlag: 0,
    signStartDate: "",
    signEndDate: "",
    bidStartDate: "",
    bidEndDate: "",
    bidbondFlag: 0,
    buyersName: "",
    buyersAddress: "",
    buyersLinkerName: "",
    buyersLinkerTel: "",
    contentAttach: null,
    bulletinAttachs: [],
  },
});

const init = (id) => {
  state.visible = true;
  state.nowId = id;
  getDetail(id);
};
const getDetail = (id) => {
  useInvitationByPackageIdApi(id).then((res) => {
    if (res.code == 0) {
      state.dataForm = Object.assign({}, res.data);
      getList();
    }
  });
};
const getList = () => {
  useGetMaterialByPackIdApi(state.dataForm.packageId).then((result) => {
    if (result.code == 0) {
      state.materialList = result.data;
    }
  });
};

defineExpose({
  init,
});
</script>
<style lang="scss" scoped>
.action_title {
  margin-bottom: 15px;
}
::v-deep(.el-form-item--default) {
  margin-bottom: 6px;
}
.block {
  display: block;
}
.files {
  margin-top: 20px;
  display: flex;
  align-items: baseline;
  .label {
    margin-right: 5px;
  }
}
::v-deep(.noflex .el-form-item__content) {
  display: block;
}
.opinion {
  margin-bottom: 5px;
}
.btn {
  margin-left: 12px;
}
.tip {
  color: red;
  font-size: 12px;
  display: inline-block;
  width: 62%;
  text-align: left;
}
</style>
