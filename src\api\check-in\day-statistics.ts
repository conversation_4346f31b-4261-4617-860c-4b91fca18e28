import service from '@/utils/request';

export const exportDayAttendanceStatistics = (reqData: any) => {
  return service.get('/work/dayAttendanceStatistics/export', { responseType: 'blob', params: reqData })
}

export const updateDayAttendanceStatistics = (reqData: any) => {
  return service.put(`/work/dayAttendanceStatistics`, reqData)
}

export const getReason = (id: any, clockType: any) => {
  return service.get(`/work/reason/${id}/${clockType}`)
}