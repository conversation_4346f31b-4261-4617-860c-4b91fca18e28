<template>
  <div class="auth">
    <el-form
      ref="authRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="140px"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="authorizationLetter" label="企业授权委托书">
            <div class="auth_letter">
              <div class="auth_letter_left">
                <div class="letter">
                  <div
                    class="letter_img"
                    v-if="(state.dataForm.authorizationLetter ?? '') !== ''"
                  >
                    <el-image
                      :src="state.dataForm.authorizationLetter"
                      class="letter_img_main"
                    ></el-image>
                    <div class="letter_img_action">
                      <el-icon
                        class="letter_img_action_icon"
                        @click="onPreviewImage(state.dataForm.authorizationLetter)"
                      >
                        <View />
                      </el-icon>
                      <el-icon
                        class="letter_img_action_icon"
                        @click="onDeleteImage('authorizationLetter')"
                        style="margin-left: 10px"
                        v-if="props.action === 'edit'"
                      >
                        <Delete />
                      </el-icon>
                    </div>
                  </div>
                  <div class="letter_action" v-else>
                    <el-upload
                      class="letter_action_upload"
                      :headers="{ Authorization: cache.getToken() }"
                      :action="constant.uploadUrl"
                      :before-upload="beforeUpload"
                      :on-success="
                        (response:any) => {
                          handleSuccess(response, 'authorizationLetter');
                        }
                      "
                      :show-file-list="false"
                      v-if="props.action === 'edit'"
                    >
                      <el-icon class="letter_action_upload_icon"><Plus /></el-icon>
                    </el-upload>
                  </div>
                </div>
              </div>
              <div class="auth_letter_right">
                <div class="auth_letter_right_example">
                  <el-button type="primary" link @click="onPreviewExample"
                    >查看示例</el-button
                  >
                </div>
                <div class="auth_letter_right_download">
                  <div class="auth_letter_right_download_name">《企业授权委托书》</div>
                  <div class="auth_letter_right_download_action">
                    <el-button type="primary" link @click="onClickDownload">
                      下载
                    </el-button>
                  </div>
                  <div class="auth_letter_right_download_desc">
                    授权协议需填写相关内容、签字并加盖实体公章后才算有效。
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="authorizedIdCardFront" label="被授权人的身份证">
            <div class="auth_card">
              <div class="auth_card_left">
                <div class="idcard" style="margin-right: 16px">
                  <div
                    class="idcard_img"
                    v-if="(state.dataForm.authorizedIdCardFront ?? '') !== ''"
                  >
                    <el-image
                      :src="state.dataForm.authorizedIdCardFront"
                      class="idcard_img_main"
                    ></el-image>
                    <div class="idcard_img_action">
                      <el-icon
                        class="idcard_img_action_icon"
                        @click="onPreviewImage(state.dataForm.authorizedIdCardFront)"
                      >
                        <View />
                      </el-icon>
                      <el-icon
                        class="idcard_img_action_icon"
                        @click="onDeleteImage('authorizedIdCardFront')"
                        style="margin-left: 10px"
                        v-if="props.action === 'edit'"
                      >
                        <Delete />
                      </el-icon>
                    </div>
                  </div>
                  <div class="idcard_action" v-else>
                    <el-upload
                      class="idcard_action_upload"
                      :headers="{ Authorization: cache.getToken() }"
                      :action="constant.uploadUrl"
                      :before-upload="beforeUpload"
                      :on-success="
                        (response:any) => {
                          handleSuccess(response, 'authorizedIdCardFront');
                        }
                      "
                      :show-file-list="false"
                      v-if="props.action === 'edit'"
                    >
                      <div
                        style="display: flex; align-items: center; flex-direction: column"
                      >
                        <el-icon class="idcard_action_upload_icon"><Plus /></el-icon>
                        <div class="idcard_action_upload_desc">请上传身份证正面</div>
                      </div>
                    </el-upload>
                  </div>
                </div>
                <div class="idcard">
                  <div
                    class="idcard_img"
                    v-if="(state.dataForm.authorizedIdCardBack ?? '') !== ''"
                  >
                    <el-image
                      :src="state.dataForm.authorizedIdCardBack"
                      class="idcard_img_main"
                    ></el-image>
                    <div class="idcard_img_action">
                      <el-icon
                        class="idcard_img_action_icon"
                        @click="onPreviewImage(state.dataForm.authorizedIdCardBack)"
                      >
                        <View />
                      </el-icon>
                      <el-icon
                        class="idcard_img_action_icon"
                        @click="onDeleteImage('authorizedIdCardBack')"
                        style="margin-left: 10px"
                        v-if="props.action === 'edit'"
                      >
                        <Delete />
                      </el-icon>
                    </div>
                  </div>
                  <div class="idcard_action" v-else>
                    <el-upload
                      class="idcard_action_upload"
                      :headers="{ Authorization: cache.getToken() }"
                      :action="constant.uploadUrl"
                      :before-upload="beforeUpload"
                      :on-success="
                        (response:any) => {
                          handleSuccess(response, 'authorizedIdCardBack');
                        }
                      "
                      :show-file-list="false"
                      v-if="props.action === 'edit'"
                    >
                      <div
                        style="display: flex; align-items: center; flex-direction: column"
                      >
                        <el-icon class="idcard_action_upload_icon"><Plus /></el-icon>
                        <div class="idcard_action_upload_desc">请上传身份证背面</div>
                      </div>
                    </el-upload>
                  </div>
                </div>
              </div>
              <div class="auth_card_right">
                请上传被授权人身份证正反面，支持 .jpg .png格式
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-image-viewer
      v-if="state.previewImage.show"
      @close="onClosePreviewImage"
      :url-list="state.previewImage.url"
    >
    </el-image-viewer>
  </div>
</template>
<script setup lang="ts">
import { ElMessage, FormRules, UploadProps } from "element-plus";
import { reactive, ref, watch, onMounted, nextTick } from "vue";
import cache from "@/utils/cache";
import constant from "@/utils/constant";
import { Plus, View, Delete } from "@element-plus/icons-vue";
import MemberLetterExampleJPG from "@/assets/image/member/member_letter_example.jpg";

const authRef = ref();

export interface ILetterOfAuth {
  authorizationLetter: string;
  authorizedIdCardFront: string;
  authorizedIdCardBack: string;
}

interface IPreviewImage {
  show: boolean;
  url: string[];
}

//#region props相关

interface IProps {
  action: string;
  letterOfAuth: ILetterOfAuth;
}

const props = withDefaults(defineProps<IProps>(), {
  action: "edit",
  letterOfAuth: () => {
    return {
      authorizationLetter: "",
      authorizedIdCardFront: "",
      authorizedIdCardBack: "",
    };
  },
});
//#endregion

interface IState {
  dataForm: ILetterOfAuth;
  dataRules: FormRules;
  previewImage: IPreviewImage;
}

const validatorAuthorizationLetter = (rule: any, value: any, callback: any) => {
  if ((state.dataForm.authorizationLetter ?? "") === "") {
    callback(new Error("请上传企业授权委托书"));
  } else {
    callback();
  }
};

const validatorIdCard = (rule: any, value: any, callback: any) => {
  if (
    (state.dataForm.authorizedIdCardFront ?? "") === "" ||
    (state.dataForm.authorizedIdCardBack ?? "") === ""
  ) {
    callback(new Error("请上传被授权人的身份证"));
  } else {
    callback();
  }
};

const state = reactive<IState>({
  dataForm: {
    authorizationLetter: "",
    authorizedIdCardFront: "",
    authorizedIdCardBack: "",
  },
  dataRules: {
    authorizationLetter: [
      {
        required: true,
        message: "请上传企业授权委托书",
        trigger: "change",
      },
      {
        validator: validatorAuthorizationLetter,
        trigger: "change",
      },
    ],
    authorizedIdCardFront: [
      {
        required: true,
        message: "请上传被授权人的身份证",
        trigger: "change",
      },
      {
        validator: validatorIdCard,
        trigger: "change",
      },
    ],
  },
  previewImage: {
    show: false,
    url: [],
  },
});

watch(
  () => props.letterOfAuth,
  () => {
    let newLetterOfAuth = JSON.parse(JSON.stringify(props.letterOfAuth));
    state.dataForm = newLetterOfAuth;
  },
  { immediate: true }
);

const emit = defineEmits<{
  (e: "emit-ref", baseInfoRef: any): void;
  (e: "on-change-value", type: string, field: string, value: any): void;
}>();

onMounted(() => {
  nextTick(() => {
    emit("emit-ref", authRef.value);
  });
});

const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  console.log(file);
  let fileNameList = file.name.split(".");
  let fileType = fileNameList[fileNameList.length - 1];
  if (["jpg", "png", "jpeg"].indexOf(fileType) == -1) {
    ElMessage.error("请上传图片文件");
    return false;
  }
  // if (file.size / 1024 / 1024 / 1024 / 1024 > 1) {
  // 	ElMessage.error('文件大小不能超过100M')
  // 	return false
  // }
  return true;
};

const handleSuccess = (res: any, type: string) => {
  if (res.code === 0) {
    let url = res.data.url;
    let newLetterOfAuth = JSON.parse(JSON.stringify(state.dataForm));
    switch (type) {
      case "authorizationLetter":
        newLetterOfAuth.authorizationLetter = url;
        break;
      case "authorizedIdCardFront":
        newLetterOfAuth.authorizedIdCardFront = url;
        break;
      case "authorizedIdCardBack":
        newLetterOfAuth.authorizedIdCardBack = url;
        break;
      default:
        break;
    }
    // @ts-ignore
    emit("on-change-value", "letterOfAuth", type, newLetterOfAuth);
  } else {
    ElMessage.error("上传失败：" + res.msg);
  }
};

//#region 预览相关

const onDeleteImage = (type: string) => {
  let newLetterOfAuth = JSON.parse(JSON.stringify(state.dataForm));
  switch (type) {
    case "authorizationLetter":
      newLetterOfAuth.authorizationLetter = "";
      break;
    case "authorizedIdCardFront":
      newLetterOfAuth.authorizedIdCardFront = "";
      break;
    case "authorizedIdCardBack":
      newLetterOfAuth.authorizedIdCardBack = "";
      break;
    default:
      break;
  }
  // @ts-ignore
  emit("on-change-value", "letterOfAuth", type, newLetterOfAuth);
};

const onPreviewImage = (url: string) => {
  let newUrl = [];
  newUrl.push(url);
  state.previewImage.url = newUrl;
  state.previewImage.show = true;
};

const onClosePreviewImage = () => {
  state.previewImage.show = false;
  state.previewImage.url = [];
};

const onPreviewExample = () => {
  let newUrl = [];
  newUrl.push(MemberLetterExampleJPG);
  state.previewImage.url = newUrl;
  state.previewImage.show = true;
};

//#endregion

const onClickDownload = async () => {
  location.href = constant.apiUrl + '/sys/userDetails/downloadExcel?access_token=' + cache.getToken()
};
</script>
<style lang="scss" scoped>
.auth {
  &_letter {
    display: flex;
    align-items: center;
    &_right {
      margin-left: 16px;
      &_download {
        margin-top: -10px;
        display: flex;
        align-items: center;
        &_desc {
          margin-left: 16px;
          color: #f56c6c;
        }
      }
    }
  }
  &_card {
    display: flex;
    align-items: center;
    &_left {
      display: flex;
      align-items: center;
    }
    &_right {
      margin-left: 16px;
      color: #f56c6c;
    }
  }
}

.letter {
  &_img {
    width: 120px;
    height: 120px;
    position: relative;
    border: 1px dashed #fff;
    cursor: pointer;
    &_main {
      width: 120px;
      height: 120px;
    }
    &_action {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      display: none;
      &_icon {
        color: #ffffff;
        font-size: 16px;
        cursor: pointer;
      }
    }
  }
  &_action {
    &_upload {
      width: 120px;
      height: 120px;
      border: 1px dashed #cdd0d6;
      display: flex;
      align-items: center;
      justify-content: center;
      &_icon {
        font-size: 24px;
        color: #909399;
      }
    }
  }
}
.letter_img:hover {
  .letter_img_action {
    display: flex;
  }
}
.idcard {
  &_img {
    width: 180px;
    height: 120px;
    position: relative;
    border: 1px dashed #fff;
    &_main {
      width: 180px;
      height: 120px;
    }
    &_action {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      display: none;
      &_icon {
        color: #ffffff;
        font-size: 16px;
        cursor: pointer;
      }
    }
  }
  &_action {
    &_upload {
      width: 180px;
      height: 120px;
      border: 1px dashed #cdd0d6;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      &_icon {
        font-size: 24px;
        color: #909399;
      }
      &_desc {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}
.idcard_img:hover {
  .idcard_img_action {
    display: flex;
  }
}
</style>
