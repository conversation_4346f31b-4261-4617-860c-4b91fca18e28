<template>
  <el-card>
    <div style="display: flex; justify-content: space-between">
      <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
        <el-form-item>
          <el-input
            v-model="state.queryForm.holidaysName"
            placeholder="请输入节假日名称"
            clearable
            style="width: 250px"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList()">查询</el-button>
        </el-form-item>
      </el-form>
      <div>
        <el-form-item>
          <!-- <el-button type="primary" @click="addOrUpdateHandle()">新增假期规则</el-button> -->
          <el-button type="primary" :loading="synchronousLoading" @click="synchronizeHolidayRules()">同步假期规则</el-button>
        </el-form-item>
      </div>
    </div>

    <el-table
      v-loading="state.dataListLoading"
      :data="state.dataList"
      border
      style="width: 100%"
      @selection-change="selectionChangeHandle"
    >
      <el-table-column
        prop="holidaysName"
        label="节假日名称"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="holiday"
        label="假期"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="makeUpClass"
        label="调休上班日期"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="year"
        label="年度"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        header-align="center"
        align="center"
        width="150"
      >
        <template #default="scope">
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)"
            >编辑</el-button
          >
          <el-button type="primary" link @click="deleteBatchHandle(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="state.pageNo"
      :page-sizes="state.pageSizes"
      :page-size="state.pageSize"
      :total="state.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    >
    </el-pagination>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="getDataList"></add-or-update>
  </el-card>
</template>

<script setup lang="ts" name="WorkLegalHolidayRulesIndex">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive, ref } from "vue";
import AddOrUpdate from "./add-or-update.vue";
import {
  getHolidayRules,
} from "@/api/check-in/legalHoliday";
import { ElMessage } from "element-plus/es";


const state: IHooksOptions = reactive({
  dataListUrl: "/work/legalHolidayRules/page",
  deleteUrl: "/work/legalHolidayRules",
  queryForm: {
    holidaysName: "",
  },
});

const addOrUpdateRef = ref();
const synchronousLoading = ref(false)
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

const synchronizeHolidayRules = () =>{
  synchronousLoading.value=true
  let year=''
  year = new Date().getFullYear()+''
  getHolidayRules(year).then((res)=>{
    if(res.code===0){
      ElMessage.success('同步成功！');
      getDataList()
    }
  }).finally(()=>{
    synchronousLoading.value=false
  })
}


const {
  getDataList,
  selectionChangeHandle,
  sizeChangeHandle,
  currentChangeHandle,
  deleteBatchHandle,
} = useCrud(state);
</script>
