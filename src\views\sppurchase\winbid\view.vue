<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-02 16:25:21
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-16 16:22:14
-->
<template>
  <el-drawer
    v-model="state.visible"
    title="中标通知书"
    size="60%"
    :close-on-click-modal="false"
  >
    <div class="action">
      <div class="action_base">
        <el-descriptions
          class="margin-top"
          title="项目信息" :column="2" border>
          <el-descriptions-item label-align="right" width="150px" label="采购计划编号">{{state.rowData.packageNo}}</el-descriptions-item>
          <el-descriptions-item label-align="right" width="150px" label="采购计划名称">{{state.rowData.packageName}}</el-descriptions-item>
          <el-descriptions-item label-align="right" width="150px" label="中标供应商">{{state.rowData.winBidder}}</el-descriptions-item>
          <el-descriptions-item label-align="right" width="150px" label="上传附件">
            <Fragment v-for="i in state.rowData.attachmentList">
              <el-link type="primary" class="block"  @click="downloadFile(i.url,i.name)">{{ i.name }}</el-link>
            </Fragment>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="action_inventory">
        <WangEditor v-model="state.rowData.content" :disabled="true"></WangEditor>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button class='btn' @click="state.visible = false">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { reactive } from "vue"
import { usebulletinByIdApi,useGetMaterialByPackIdApi } from '@/api/pnotice'
import WangEditor from "@/components/wang-editor/index.vue";
import { ElMessage } from 'element-plus'
import { downloadFile } from '@/utils/tool'

const state = reactive({
  visible: false,
  rowData: {}
})

const init = (row)=>{
  state.visible = true
  Object.assign(state.rowData, row)
}

defineExpose({
	init
})
</script>
<style lang="scss" scoped>
.action_title{margin-bottom: 15px;}
.block{display:block;}
.btn{margin-left: 12px;}
.action_base{margin-bottom: 20px;}
</style>
