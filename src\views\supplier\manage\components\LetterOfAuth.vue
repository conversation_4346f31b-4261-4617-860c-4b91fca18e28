<template>
  <div class="auth">
    <el-form
      ref="authRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="140px"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="companyName" label="企业授权委托书">
            <div class="auth_letter">
              <div class="auth_letter_left">
                <el-button type="primary">上传</el-button>
              </div>
              <div class="auth_letter_right">
                <div class="auth_letter_right_example">
                  <el-button type="primary" link>查看示例</el-button>
                </div>
                <div class="auth_letter_right_download">
                  <div class="auth_letter_right_download_name">《企业授权委托书》</div>
                  <div class="auth_letter_right_download_action">
                    <el-button type="primary" link>下载</el-button>
                  </div>
                  <div class="auth_letter_right_download_desc">
                    授权协议需填写相关内容、签字并加盖实体公章后才算有效。
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="companyName" label="被授权人的身份证">
            <div class="auth_card">
              <div class="auth_card_left">
                <el-button type="primary">上传</el-button>
              </div>
              <div class="auth_card_right">
                请上传被授权人身份证正反面，支持 .jpg .png格式
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { FormRules } from "element-plus";
import { reactive, ref } from "vue";

const authRef = ref();

interface IDataForm {}

interface IState {
  dataForm: IDataForm;
  dataRules: FormRules;
}

const state = reactive<IState>({
  dataForm: {},
  dataRules: {},
});
</script>
<style lang="scss" scoped>
.auth {
  &_letter {
    display: flex;
    align-items: center;
    &_right {
      margin-left: 16px;
      &_download {
        margin-top: -10px;
        display: flex;
        align-items: center;
        &_desc {
          margin-left: 16px;
          color: #f56c6c;
        }
      }
    }
  }
  &_card {
    display: flex;
    align-items: center;
    &_right {
      margin-left: 16px;
      color: #f56c6c;
    }
  }
}
</style>
