<template>
  <el-card>
    <div class="action">
      <div class="action_base">
        <div class="action_base_title">
          <ContentTitle title="基本信息"></ContentTitle>
        </div>
        <div class="action_base_content">
          <BaseInfo
            :type="state.type"
            :baseInfo="state.baseInfo"
            @on-change-org="onChangeOrg"
            @on-change-value="onChangeBaseInfo"
            @emit-ref="onEmitBaseInfoRef"
            @on-select-manager="onSelectUser('manage')"
            @on-select-project="onSelectProject"
          ></BaseInfo>
        </div>
      </div>
      <div class="action_inventory">
        <div class="action_inventory_title">
          <ContentTitle title="采购清单">
            <template #right>
              <el-button type="primary" class="ml10" @click="selectMaterialItem" :disabled="state.materialListDiaAll.length<=0">
                选择清单项
              </el-button>
            </template>
          </ContentTitle>
        </div>
        <div class="action_inventory_content">
          <PurchaseList
            :attachmentList="state.attachmentList"
            :materialList="state.materialList"
            @change-quantity="onChangeQuantity"
            @change-comment="onChangeComment"
            @on-delete="onDeleteMaterial"
            @upload-field="onUploadField"
          ></PurchaseList>
        </div>
      </div>
      <div class="action_process">
        <div class="action_process_title">
          <ContentTitle title="审核流程"></ContentTitle>
        </div>
        <div class="action_process_content">
          <ApprovalProcess
            :auditInfo="state.auditInfo"
            @emit-ref="onEmitAuditRef"
            @on-select-user="onSelectUser('audit')"
          ></ApprovalProcess>
        </div>
      </div>
      <div class="action_btn">
        <el-button type="primary" @click="onSubmit('2')">提交</el-button>
        <el-button type="primary" plain @click="onSubmit('1')">暂存</el-button>
        <el-button @click="onCancel">取消</el-button>
      </div>
      <UserSelect
        :show="state.userAction.show"
        :title="state.userAction.title"
        @on-close="onCloseSelectUser"
        @on-select="onConfirmSelectUser"
      ></UserSelect>
      <ProjectSelect
        :show="state.projectAction.show"
        @on-close="onCloseSelectProject"
        @on-select="onConfirmSelectProject"
      ></ProjectSelect>
    </div>
  </el-card>


  <MaterialSelect :show="state.MaterialSelectShow" :list="state.materialListDiaAll" :selectlist="state.materialList" 
    @on-select="onSelectMaterial" @on-close="onCloseSelectMaterial"></MaterialSelect>
</template>

<script setup lang="ts">
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import BaseInfo from "./components/BaseInfo.vue";
import PurchaseList from "./components/PurchaseList.vue";
import ApprovalProcess from "./components/ApprovalProcess.vue";
import UserSelect from "./components/UserSelect.vue";
import ProjectSelect from "./components/ProjectSelect.vue";
import { getProjectInfo } from "@/api/purchase/project";
import MaterialSelect from './components/MaterialSelect.vue'
import { ref, reactive, onMounted } from "vue";
import {
  getPackageNo,
  getProjectPlanInfo,
  IActionProjectPlan,
  saveProjectPlan,
  updateProjectPlan,
} from "@/api/purchase/plan";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { closeTab } from "@/utils/tabs";
import { useUserStore } from "@/store/modules/user";

const userStore = useUserStore();
const router = useRouter();
const route = useRoute();
const baseInfoRef = ref();
const auditRef = ref();

const onEmitBaseInfoRef = (dataFormRef: any) => {
  baseInfoRef.value = dataFormRef;
};

const onEmitAuditRef = (dataFormRef: any) => {
  auditRef.value = dataFormRef;
};

export interface IBaseInfo {
  projectId?: number;
  projectName: string;
  packageNo: string;
  packageName: string;
  packageBudget?: number;
  packageType: string;
  packageMode: string;
  orgId?: number;
  orgName?: string;
  managerId?: number;
  managerName: string;
  comment: string;
  deliveryAddress: string;
}

interface IUserAction {
  show: boolean;
  title: string;
  type: "manage" | "audit";
}

interface IProjectAction {
  show: boolean;
}

export interface IMaterialItem {
  id: number;
  materialNo: string;
  materialName: string;
  materialSpec: string;
  materialType: string;
  materialUnit: string;
  materialQuantity?: number;
  surplusQuantity?: number;
  comment: string;
}

export interface IAuditInfo {
  reviewedById?: number;
  reviewedBy: string;
}

export interface IAttachmentItem {
  name: string;
  url: string;
  platform: string;
  size: number;
}

interface IState {
  type: string;
  id?: number;
  baseInfo: IBaseInfo;
  userAction: IUserAction;
  projectAction: IProjectAction;
  materialList: IMaterialItem[];
  materialListDiaAll: IMaterialItem[];
  attachmentList: IAttachmentItem[];
  auditInfo: IAuditInfo;
  MaterialSelectShow: boolean;
}

const state = reactive<IState>({
  type: "add",
  id: void 0,
  baseInfo: {
    projectId: void 0,
    projectName: "",
    packageNo: "",
    packageName: "",
    packageBudget: void 0,
    packageType: "1",
    deliveryAddress:'',
    packageMode: "",
    orgId: void 0,
    orgName: "",
    managerId: void 0,
    managerName: "",
    comment: "",
  },
  projectAction: {
    show: false,
  },
  MaterialSelectShow: false,
  userAction: {
    show: false,
    title: "选择项目经理",
    type: "manage",
  },
  materialList: [],
  materialListDiaAll: [],
  attachmentList: [],
  auditInfo: {
    reviewedById: void 0,
    reviewedBy: "",
  },
});

interface IQuery {
  type: string;
  id?: number;
}

onMounted(() => {
  const { type, id } = (route.query as unknown) as IQuery;
  state.type = type;
  state.id = id;
  if (type === "update") {
    if (id) {
      getProjectPlanInfo(id).then((res: any) => {
        if (res.code === 0) {
          editGetProjectInfo(res.data.projectId)
          let newBaseInfo = {
            projectId: res.data.projectId,
            projectName: res.data.projectName,
            packageNo: res.data.packageNo,
            packageName: res.data.packageName,
            packageBudget: res.data.packageBudget,
            packageType: res.data.packageType + "",
            packageMode: res.data.packageMode + "",
            orgId: res.data.orgId,
            orgName: res.data.orgName,
            managerId: res.data.managerId,
            deliveryAddress:res.data.deliveryAddress,
            managerName: res.data.managerName,
            comment: res.data.comment,
          };
          state.baseInfo = newBaseInfo;
          let newMaterialList = res.data.materialVOS.map((item: any) => {
            return {
              id: item.materialId,
              materialNo: item?.materialNo ?? "",
              materialName: item?.materialName ?? "",
              materialSpec: item?.materialSpec ?? "",
              materialType: item?.materialType ?? "",
              materialUnit: item?.materialUnit ?? "",
              materialQuantity: item.materialQuantity,
              surplusQuantity: item.surplusQuantity,
              comment: item?.comment ?? "",
            };
          });
          state.materialList = newMaterialList;
          let newAttachmentList = res.data.attachmentList.map((item: any) => {
            return {
              name: item.name,
              url: item.url,
              platform: item.platform,
              size: item.size,
            };
          });
          state.attachmentList = newAttachmentList;
          let newAuditInfo = {
            reviewedById: res.data?.reviewedById,
            reviewedBy: res.data?.reviewedBy,
          };
          state.auditInfo = newAuditInfo;
        }
      });
    }
  } else {
    state.baseInfo.orgId = userStore.user?.orgId;
  }
});

const onSelectUser = (type: "manage" | "audit") => {
  if (type == "manage") {
    state.userAction.title = "选择项目经理";
  } else {
    state.userAction.title = "选择审批人";
  }
  state.userAction.type = type;
  state.userAction.show = true;
};

const onCloseSelectUser = () => {
  state.userAction.show = false;
};

const onConfirmSelectUser = (row: any) => {
  if (state.userAction.type === "manage") {
    let newBaseInfo = JSON.parse(JSON.stringify(state.baseInfo));
    newBaseInfo.managerId = row.id;
    newBaseInfo.managerName = row.username;
    console.log(newBaseInfo);
    state.baseInfo = newBaseInfo;
  } else {
    let newAuditInfo = JSON.parse(JSON.stringify(state.auditInfo));
    newAuditInfo.reviewedById = row.id;
    newAuditInfo.reviewedBy = row.username;
    state.auditInfo = newAuditInfo;
  }
};

const onSelectProject = () => {
  state.projectAction.show = true;
};

const onCloseSelectProject = () => {
  state.projectAction.show = false;
};

const onConfirmSelectProject = (row: any) => {
  state.projectAction.show = false;
  let projectId = row.id;

  getProjectInfo(projectId).then((res: any) => {
    if (res.code === 0) {
      if (state.type === "add") {
        getPackageNo(projectId).then((resPackageNo: any) => {
          if (resPackageNo.code === 0) {
            let newBaseInfo = JSON.parse(JSON.stringify(state.baseInfo));
            newBaseInfo.projectId = projectId;
            newBaseInfo.projectName = res.data.projectName;
            newBaseInfo.packageBudget = res.data.budget;
            newBaseInfo.packageNo = resPackageNo.data;
            state.baseInfo = newBaseInfo;
            let newMaterialList = (res.data.tbProjectMaterialVOS ?? []).map(
              (item: any) => {
                return {
                  id: item?.materialId ?? void 0,
                  materialNo: item?.materialId ?? "",
                  materialName: item?.materialName ?? "",
                  materialSpec: item?.materialSpec ?? "",
                  materialType: item?.materialType ?? "",
                  materialUnit: item?.materialUnit ?? "",
                  materialQuantity: item.materialQuantity,
                  surplusQuantity: item.surplusQuantity,
                  comment: item?.comment ?? "",
                };
              }
            );
            state.materialListDiaAll = newMaterialList;
          } else {
            ElMessage.error("自动获取计划编号失败!");
          }
        });
      } else {
        let newBaseInfo = JSON.parse(JSON.stringify(state.baseInfo));
        newBaseInfo.projectId = projectId;
        newBaseInfo.projectName = res.data.projectName;
        newBaseInfo.packageBudget = res.data.budget;
        state.baseInfo = newBaseInfo;
        let newMaterialList = (res.data.tbProjectMaterialVOS ?? []).map((item: any) => {
          return {
            id: item?.materialId ?? void 0,
            materialNo: item?.materialId ?? "",
            materialName: item?.materialName ?? "",
            materialSpec: item?.materialSpec ?? "",
            materialType: item?.materialType ?? "",
            materialUnit: item?.materialUnit ?? "",
            materialQuantity: item.materialQuantity,
            surplusQuantity: item.surplusQuantity,
            comment: item?.comment ?? "",
          };
        });
        state.materialListDiaAll = newMaterialList;
      }
      state.materialList = [];
    }
  });
};

const onChangeBaseInfo = (field: string, value: string | number) => {
  let newBaseInfo = JSON.parse(JSON.stringify(state.baseInfo));
  newBaseInfo[field] = value;
  state.baseInfo = newBaseInfo;
};

const onChangeOrg = (orgId: number, orgName: string) => {
  let newBaseInfo = JSON.parse(JSON.stringify(state.baseInfo));
  newBaseInfo.orgId = orgId;
  newBaseInfo.orgName = orgName;
  state.baseInfo = newBaseInfo;
};

const onChangeQuantity = (rowIndex: number, value: string) => {
  let newMaterialList = JSON.parse(JSON.stringify(state.materialList));
  newMaterialList[rowIndex].materialQuantity = value;
  state.materialList = newMaterialList;
};

const onChangeComment = (rowIndex: number, value: string) => {
  let newMaterialList = JSON.parse(JSON.stringify(state.materialList));
  newMaterialList[rowIndex].comment = value;
  state.materialList = newMaterialList;
};

const onDeleteMaterial = (rowIndex: number) => {
  let newMaterialList = JSON.parse(JSON.stringify(state.materialList));
  newMaterialList = newMaterialList.filter((item: IMaterialItem, index: number) => {
    return index !== rowIndex;
  });
  state.materialList = newMaterialList;
};

const onUploadField = (attachmentList: IAttachmentItem[]) => {
  let newAttachmentList = JSON.parse(JSON.stringify(state.attachmentList));
  newAttachmentList = attachmentList;
  state.attachmentList = newAttachmentList;
};

const onSubmit = (auditStatus: string) => {
  baseInfoRef.value.validate((valid: boolean) => {
    if (valid) {
      auditRef.value.validate((validAudit: boolean) => {
        if (validAudit || auditStatus === "1") {
          let reqData: IActionProjectPlan = {
            id: state.type === "update" ? state.id : void 0,
            projectId: state.baseInfo.projectId,
            projectName: state.baseInfo.projectName,
            packageName: state.baseInfo.packageName,
            packageNo: state.baseInfo.packageNo,
            packageBudget: state.baseInfo.packageBudget,
            packageType: state.baseInfo.packageType,
            packageMode: state.baseInfo.packageMode,
            managerId: state.baseInfo.managerId,
            managerName: state.baseInfo.managerName,
            comment: state.baseInfo.comment,
            auditStatus: auditStatus,
            orgId: state.baseInfo.orgId,
            orgName: state.baseInfo.orgName,
            deliveryAddress: state.baseInfo.deliveryAddress,
            reviewedById: state.auditInfo.reviewedById,
            reviewedBy: state.auditInfo.reviewedBy,
            attachmentList: state.attachmentList.map((item) => {
              return {
                name: item.name,
                url: item.url,
                platform: item.platform,
                size: item.size,
              };
            }),
            materialVOS: state.materialList.map((item) => {
              return {
                materialId: item?.id ?? void 0,
                materialNo: item?.materialNo ?? "",
                materialName: item?.materialName ?? "",
                materialSpec: item?.materialSpec ?? "",
                materialType: item?.materialType ?? "",
                materialUnit: item?.materialUnit ?? "",
                materialQuantity: item.materialQuantity,
                surplusQuantity: item.surplusQuantity,
                comment: item?.comment ?? "",
              };
            }),
          };
          (state.type === "update"
            ? updateProjectPlan(reqData)
            : saveProjectPlan(reqData)
          ).then((res: any) => {
            if (res.code === 0) {
              closeTab(router, route);
              router.push("/purchase/plan/index");
              ElMessage.success("操作成功");
            }
          });
        }
      });
    }
  });
};

const onCancel = () => {
  closeTab(router, route);
  router.push("/purchase/plan/index");
};

const selectMaterialItem = ()=>{
  state.MaterialSelectShow = true
}
const onSelectMaterial = (materialList: IMaterialItem[])=>{
  materialList.map(item=> item.materialQuantity = item.surplusQuantity)
  state.materialList = materialList
  state.MaterialSelectShow = false
}
const onCloseSelectMaterial = ()=>{ state.MaterialSelectShow = false }

const editGetProjectInfo = (id: string)=>{
  getProjectInfo(id).then((res: any) => {
    if (res.code === 0) {
      let newMaterialList = (res.data.tbProjectMaterialVOS ?? []).map((item: any) => {
        return {
          id: item?.materialId ?? void 0,
          materialNo: item?.materialId ?? "",
          materialName: item?.materialName ?? "",
          materialSpec: item?.materialSpec ?? "",
          materialType: item?.materialType ?? "",
          materialUnit: item?.materialUnit ?? "",
          materialQuantity: item.materialQuantity,
          surplusQuantity: item.surplusQuantity,
          comment: item?.comment ?? "",
        };
      });
      state.materialListDiaAll = newMaterialList;
    }
  })
}


</script>

<style lang="scss" scoped>
.action {
  &_base {
    &_content {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 10px;
    }
  }
  &_process {
    margin-top: 16px;
    &_content {
      margin-top: 16px;
    }
  }
  &_btn {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .ml10{margin-left: 10px;}
}
</style>
