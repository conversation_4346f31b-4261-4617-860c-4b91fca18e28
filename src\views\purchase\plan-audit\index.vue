<template>
  <el-card>
    <div class="audit">
      <div class="audit-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input placeholder="采购计划编号" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-input placeholder="采购计划名称" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-select placeholder="采购方式" style="width: 100%">
              <el-option label="企业微信" value="wechat_work"></el-option>
              <el-option label="钉钉" value="dingtalk"></el-option>
              <el-option label="飞书" value="feishu"></el-option>
              <el-option label="微信" value="wechat_open"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-select placeholder="状态" style="width: 100%">
              <el-option label="企业微信" value="wechat_work"></el-option>
              <el-option label="钉钉" value="dingtalk"></el-option>
              <el-option label="飞书" value="feishu"></el-option>
              <el-option label="微信" value="wechat_open"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              type="daterange"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button>重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="audit-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="projectName"
            label="项目"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="packageType"
            label="采购方式"
            dict-type="package_type"
          ></fast-table-column>
          <el-table-column
            prop="packageBudget"
            label="预算金额（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="orgName"
            label="申请人"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="orgName"
            label="申请部门"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="orgName"
            label="状态"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">
                审核
              </el-button>
              <el-button type="primary" link @click="deleteBatchHandle(scope.row.id)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="audit-page">
        <el-pagination
          :current-page="state.page"
          :page-sizes="state.pageSizes"
          :page-size="state.limit"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <div class="audit_record">
        <AuditRecord></AuditRecord>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { reactive } from "vue";
import AuditRecord from "./components/AuditRecord.vue";
const state = reactive({
  queryForm: {},
  dataListLoading: false,
  dataList: [],
  page: 1,
  pageSizes: [10, 20, 30, 50],
  limit: 10,
  total: 0,
});

const getDataList = () => {};

const addOrUpdateHandle = (id: string) => {};

const deleteBatchHandle = (id: string) => {};

const sizeChangeHandle = () => {};

const currentChangeHandle = () => {};
</script>
<style lang="scss" scoped>
.audit {
  &-list {
    margin-top: 16px;
  }
}
</style>
