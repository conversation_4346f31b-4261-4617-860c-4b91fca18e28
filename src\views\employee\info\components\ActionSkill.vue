<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    draggable
  >
    <div class="action-certificate">
      <el-form :model="dataForm" :rules="dataRule" ref="dataFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="skillName" label="技能名称">
              <el-input v-model="dataForm.skillName" placeholder="技能名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="skillType" label="技能类型">
              <fast-select
                v-model="dataForm.skillType"
                dict-type="skill_type"
                placeholder="技能类型"
              ></fast-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="masteryLevel" label="掌握程度">
              <fast-select
                v-model="dataForm.masteryLevel"
                dict-type="mastery_level"
                placeholder="掌握程度"
              ></fast-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="remark" label="备注">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { getUserSkillInfo, useSkillSubmitApi } from "@/api/employee/index";
import { ElMessage } from "element-plus";

const dataForm = reactive({
  id: "",
  skillName: "",
  skillType: "",
  masteryLevel: "",
  remark: "",
});

const dataRule = reactive({
  skillName: [{ required: true, message: "技能名称不能为空", trigger: "blur" }],
});

const visible = ref(false);

const dataFormRef = ref();

const init = (id) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  // id 存在则为修改
  if (id) {
    GetUserSkillInfo(id);
  }
};

const emit = defineEmits(["refreshDataList"]);

const submitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    useSkillSubmitApi(dataForm).then(() => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        },
      });
    });
  });
};

const GetUserSkillInfo = (id) => {
  getUserSkillInfo(id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

defineExpose({
  init,
});
</script>
<style lang="scss" scoped></style>
