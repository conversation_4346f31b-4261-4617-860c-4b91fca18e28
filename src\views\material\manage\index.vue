<template>
  <el-card>
    <div class="material">
      <div class="material-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="物料编码"
              clearable
              v-model="state.queryForm.materialNo"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="物料名称"
              clearable
              v-model="state.queryForm.materialName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onResetSearch()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="meterial-action">
        <el-button
          type="primary"
          @click="addOrUpdateHandle(false)"
          v-auth="'purchase:material:save'"
        >
          新增
        </el-button>
        <!-- <el-button>导入</el-button>
        <el-button type="text">物料模板下载</el-button> -->
      </div>
      <div class="material-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="materialNo"
            label="物料编码"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="materialName"
            label="物料名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="categoryName"
            label="物料分类"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="materialType"
            label="型号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="materialSpec"
            label="规格"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="materialUnit"
            label="计量单位"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="addOrUpdateHandle(true, scope.row.id)"
                v-auth="'purchase:material:update'"
              >
                修改
              </el-button>
              <el-button
                type="primary"
                link
                @click="deleteBatchHandle(scope.row.id)"
                v-auth="'purchase:material:delete'"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="material-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <AddOrUpdate
        :show="action.show"
        :id="action.id"
        @on-close="onCloseAction"
        @on-submit="onSumbitAction"
      ></AddOrUpdate>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive } from "vue";
import AddOrUpdate from "./add-or-update.vue";

const state = reactive<IHooksOptions>({
  queryForm: {
    materialNo: "",
    materialName: "",
  },
  dataListUrl: "/purchase/material/page",
  deleteUrl: "/purchase/material",
});

const { getDataList, sizeChangeHandle, currentChangeHandle, deleteBatchHandle } = useCrud(
  state
);

interface IAction {
  show: boolean;
  id?: number;
}

const action = reactive<IAction>({
  show: false,
  id: void 0,
});

const addOrUpdateHandle = (isUpdate: boolean, id?: number) => {
  if (isUpdate) {
    action.id = id;
  }
  action.show = true;
};

const onResetSearch = () => {
  state.queryForm.materialNo = "";
  state.queryForm.materialName = "";
  state.pageNo = 1;
  getDataList();
};

const onCloseAction = () => {
  action.show = false;
  action.id = void 0;
};

const onSumbitAction = () => {
  action.show = false;
  action.id = void 0;
  getDataList();
};
</script>
<style lang="scss" scoped>
.material {
  &-list {
    margin-top: 16px;
  }
}
</style>
