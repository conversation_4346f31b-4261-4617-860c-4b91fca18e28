<template>
	<el-card>
		<el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
			<el-form-item>
				<el-input v-model="state.queryForm.paramKey" placeholder="参数键"></el-input>
			</el-form-item>
			<el-form-item>
				<el-input v-model="state.queryForm.paramValue" placeholder="参数值"></el-input>
			</el-form-item>
			<el-form-item>
				<fast-select v-model="state.queryForm.paramType" dict-type="params_type" placeholder="系统参数" clearable></fast-select>
			</el-form-item>
			<el-form-item>
				<el-button @click="getDataList()">查询</el-button>
			</el-form-item>
			<el-form-item>
				<el-button type="primary" @click="addOrUpdateHandle()"> 新增 </el-button>
			</el-form-item>
			<el-form-item>
				<el-button type="danger" @click="deleteBatchHandle()"> 删除 </el-button>
			</el-form-item>
		</el-form>
		<el-table
				v-loading="state.dataListLoading"
				show-overflow-tooltip
				:data="state.dataList"
				border
				style="width: 100%"
				@selection-change="selectionChangeHandle">
			<el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
			<el-table-column prop="paramName" label="参数名称" header-align="center" align="center"></el-table-column>
			<el-table-column prop="paramKey" label="参数键" header-align="center" align="center"></el-table-column>
			<el-table-column prop="paramValue" label="参数值" header-align="center" align="center"></el-table-column>
			<fast-table-column prop="paramType" label="系统参数" dict-type="params_type"></fast-table-column>
			<el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
			<el-table-column prop="createTime" label="创建时间" header-align="center" align="center"></el-table-column>
			<el-table-column label="操作" fixed="right" header-align="center" align="center" width="150">
				<template #default="scope">
					<el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)">修改 </el-button>
					<el-button type="primary" link @click="deleteBatchHandle(scope.row.id)">删除 </el-button>
				</template>
			</el-table-column>
		</el-table>
		<el-pagination
				:current-page="state.pageNo"
				:page-sizes="state.pageSizes"
				:page-size="state.pageSize"
				:total="state.total"
				layout="total, sizes, prev, pager, next, jumper"
				@size-change="sizeChangeHandle"
				@current-change="currentChangeHandle">
		</el-pagination>

		<!-- 弹窗, 新增 / 修改 -->
		<add-or-update ref="addOrUpdateRef" @refreshDataList="getDataList"></add-or-update>
	</el-card>
</template>

<script setup lang="ts" name="SysParamsIndex">
	import { useCrud } from '@/hooks'
	import { reactive, ref } from 'vue'
	import AddOrUpdate from './add-or-update.vue'
	import { IHooksOptions } from '@/hooks/interface'

	const state: IHooksOptions = reactive({
		dataListUrl: '/sys/params/page',
		deleteUrl: '/sys/params',
		queryForm: {
			paramType: '',
			paramKey: '',
			paramValue: ''
		}
	})

	const addOrUpdateRef = ref()
	const addOrUpdateHandle = (id?: number) => {
		addOrUpdateRef.value.init(id)
	}

	const { getDataList, selectionChangeHandle, sizeChangeHandle, currentChangeHandle, deleteBatchHandle } = useCrud(state)
</script>
