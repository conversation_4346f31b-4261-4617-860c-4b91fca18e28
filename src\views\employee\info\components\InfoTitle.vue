<template>
  <div class="title">
    <div class="title-divider"></div>
    <div class="title-text">
      {{ title }}
    </div>
  </div>
</template>
<script setup>
const props = defineProps({
  title: {
    type: String,
    default: "",
  },
});
</script>
<style lang="scss" scoped>
.title {
  display: flex;
  align-items: center;
  &-divider {
    margin-right: 6px;
    width: 6px;
    height: 18px;
    background-color: #2561ef;
    border-radius: 8px;
  }
}
</style>
