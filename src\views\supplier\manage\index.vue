<template>
  <el-card>
    <div class="manage">
      <div class="manage-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList">
          <el-form-item>
            <el-input
              placeholder="单位名称"
              clearable
              v-model="state.queryForm.userDetailsName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="selectDate"
              type="daterange"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="onChangeDate"
              style="width: 350px"
            />
          </el-form-item>
          <el-form-item>
            <el-select v-model="state.queryForm.auditStatus" placeholder="审核状态" style="width: 200px" clearable>
              <el-option
                v-for="item in state.auditStatusList"
                :key="item.dictValue"
                :label="item.dictLabel"
                :value="item.dictValue"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList" type="primary">查询</el-button>
            <el-button @click="onResetSearch()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <!-- <div class="manage-action">
        <el-button type="primary">新增供应商</el-button>
      </div> -->
      <div class="manage-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="userDetailsName"
            label="单位名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="contactName"
            label="联系人"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="contactPhone"
            label="联系电话"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="auditStatus"
            label="状态"
            dict-type="audit_status"
          ></fast-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="250"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="onClickAction('watch', scope.row.id, scope.row.userDetailsId)"
              >
                查看
              </el-button>
              <!-- <el-button type="primary" link> 锁定 </el-button>
              <el-button type="primary" link> 解锁 </el-button> -->
              <el-button
                type="primary"
                link
                v-if="scope.row.auditStatus == '2'"
                @click="onClickAction('audit', scope.row.id, scope.row.userDetailsId)"
              >
                审核
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="manage-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import dayjs from "dayjs";
import { reactive, ref,onMounted } from "vue";
import { useRouter } from "vue-router";
import { useAppStore } from "@/store/modules/app";
import { getDictDataList } from "@/utils/tool";

const appStore = useAppStore();
const router = useRouter();
const selectDate = ref<string[]>([]);

const state = reactive<IHooksOptions>({
  queryForm: {
    userDetailsName: "",
    startTime: "",
    endTime: "",
    auditStatus:""
  },
  dataListUrl: "/sys/purchaserBidder/page",
  auditStatusList:[]
});

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);
onMounted(()=>{
  state.auditStatusList=[]
  let auditStatusFull=getDictDataList(appStore.dictList, 'audit_status')
  auditStatusFull.map((item:any)=>{
    if(item.dictValue=='2'||item.dictValue=='3'||item.dictValue=='4'){
      state.auditStatusList.push(item)
    }
  })
})
const onResetSearch = () => {
  state.queryForm.userDetailsName = "";
  state.queryForm.startTime = "";
  state.queryForm.endTime = "";
  state.queryForm.auditStatus = "";
  selectDate.value = [];
  state.pageNo = 1;
  getDataList();
};

const onClickAction = (type: string, id: number, userDetailsId: number) => {
  router.push({
    path: "/supplier/manage/action",
    query: { type, id, userDetailsId },
  });
};

const onChangeDate = (value: any) => {
  if (value && value.length === 2) {
    state.queryForm.startTime = dayjs(value[0]).format("YYYY-MM-DD");
    state.queryForm.endTime = dayjs(value[1]).format("YYYY-MM-DD");
  } else {
    state.queryForm.startTime = "";
    state.queryForm.endTime = "";
  }
};
</script>
<style lang="scss" scoped>
.manage {
  &-list {
    margin-top: 16px;
  }
}
</style>
