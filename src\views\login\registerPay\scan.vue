<template>
  <div class="register">
    <div class="register-header">
      <Header title="用户注册"></Header>
    </div>
    <div class="register-body">
      <div class="left">
        <div class="time-tip">距离二维码过期还剩<span class="time">{{state.timeSecond}}</span>秒，过期后请刷新页面重新获取二维码</div>
        <div class="money">
          <span class="money-label">支付金额：</span>
          <span class="money-unit">¥</span>
          <span class="money-value">{{state.registerQueryInfo.payAmount}}</span>
        </div>
        <div class="code">
          <div class="code-img">
            <qr-code ref="qrCodeRef" :urlCode="state.urlCode" :size="200"></qr-code>
          </div>
        </div>
        <div class="code-tip" :class="state.payQueryInfo.payType==='AllinpayUnitorder_A01'?'code-tip-alipay':''">
          <img class="scan-icon" src="@/assets/image/register/scan.png"  />
          <div>
            <span>请使用{{state.payQueryInfo.payTypeName}}扫一扫</span><br />
            <span>扫描二维码支付</span>
          </div>
        </div>
      </div>
      <div class="right">
        <img class="step-img" v-if="state.payQueryInfo.payType==='MicroNative'||state.payQueryInfo.payType==='AllinpayUnitorder_W01'" src="@/assets/image/register/wechat_tip.png"  />
        <img class="step-img" v-if="state.payQueryInfo.payType==='AllinpayUnitorder_A01'" src="@/assets/image/register/alipay_tip.png"  />
      </div>
    </div>
    <div class="register-body other">
      <span class="other-btn" @click="clickOther()">选择其他支付方式</span>
    </div>
    <div class="register-footer">
      <Footer></Footer>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref,onMounted,onUnmounted } from "vue";
import Header from "../components/Header.vue";
import Footer from "../components/Footer.vue";
import { useRoute,useRouter } from "vue-router";
import { registerScanCodeBuyApi,getPayStatusApi } from "@/api/auth";
import QrCode from '@/components/qr-code/index.vue'
const qrCodeRef=ref()

const route = useRoute();
const router = useRouter();
const state = reactive({
  payQueryInfo:{},
  registerQueryInfo:{},
  timeSecond:0,
  urlCode:"",
  payInterval:null,
  statusInterval:null
});
let eventSource = null;
onMounted(() => {
  clearInterval(state.payInterval);
  clearInterval(state.statusInterval);
  setInfo(0,"")
  if(route.query.registerQueryInfo){
    state.registerQueryInfo=JSON.parse(route.query.registerQueryInfo)
    // initSSE();
  }
  if(route.query.payQueryInfo){
    state.payQueryInfo=JSON.parse(route.query.payQueryInfo)
    newOrder()
  }
})

onUnmounted(() => {
  console.log("onUnmounted")
  clearInterval(state.payInterval);
  clearInterval(state.statusInterval);
  // if (eventSource) {
  //   console.log("onUnmounted-eventSource")
  //   eventSource.close();
  // }
});

const setInfo=(expireTime:any,urlCode:any)=>{
  console.log("setInfo",expireTime)
  if(expireTime>0){
    state.timeSecond=expireTime/1000
    state.urlCode=urlCode
    countdown(state.timeSecond);
    setTimeout(()=>{
      qrCodeRef.value.generateQRCode()
    },100)
  }else if(expireTime==0){
    state.timeSecond=0
    state.urlCode=""
  }
}

const newOrder=()=>{
  let reqData={
    creator:parseInt(state.registerQueryInfo.userId),
    goodsId:parseInt(state.registerQueryInfo.userId),
    orderAmount:parseFloat(state.registerQueryInfo.payAmount),
    payType:state.payQueryInfo.payType,
    remarks:state.payQueryInfo.remarks
  }
  registerScanCodeBuyApi(reqData).then((res: any) => {
    if (res.code === 0) {
      let resData=res.data
      setInfo(resData.expireTime,resData.urlCode)

      state.statusInterval=setInterval(() => {
        getPayStatus()
      }, 1000);
    }
  })
}

const countdown=(seconds:any)=> {
  state.payInterval = setInterval(function() {
    if (state.timeSecond === 0) {
      clearInterval(state.payInterval);
    } else {
      state.timeSecond--;
    }
  }, 1000);
}
 

const clickOther=()=>{
  let query={
    ...state.registerQueryInfo
  }
  router.replace({path:"/registerPay",query:query});
}

const initSSE = () => {
  eventSource = new EventSource(import.meta.env.VITE_API_URL+'/message/sse/connect/'+state.registerQueryInfo.userId);
  eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);
    console.log("initSSE-data",data)

    if(data.code==200 && data.userId==state.registerQueryInfo.userId){
      router.replace("/registerPayOk");
    }
  };
  eventSource.onerror = (error) => {
    console.error('SSE error:', error);
    eventSource.close();
  };
};

const getPayStatus=()=>{
  getPayStatusApi(parseInt(state.registerQueryInfo.userId)).then(res=>{
    if(res.code===0 && res.data===1){
      clearInterval(state.statusInterval);
      router.replace("/registerPayOk");
    }
  })
}
</script>
<style lang="scss" scoped>
.register-body {
  padding:40px 60px;
  display: flex;
  align-items: center;
}
.left{
  width:calc(100% - 350px);
  .time-tip{
    text-align: center;
    .time{
      color:#ff0000;
    }
  }
  .money{
    margin-top: 30px;
    text-align: center;
    .money-label{
      font-weight: bold;
      font-size: 16px;
      color:#000;
    }
    .money-unit{
      font-size: 14px;
      font-weight: bold;
      color:#F59A23;
      margin-right:2px;
    }
    .money-value{
      font-weight: bold;
      font-size: 20px;
      color:#F59A23;
    }
  }
  .code{
    border:1px solid #e5e5e5;
    width:250px;
    height:250px;
    margin:30px auto 0;
  }
  .code-tip{
    width:220px;
    margin:10px auto 0;
    background: #19BE6A;
    color:#fff;
    font-size: 16px;
    line-height: 22px;
    display: flex;
    align-items: center;
    padding:5px 15px;
    .scan-icon{
      width:50px;
      height:50px;
      margin-right: 15px;
    }
  }
  .code-tip-alipay{
    background:#1E83D2 ;
  }
}
.right{
  width:300px;
  margin-left: 50px;
  .step-img{
    width:300px;
  }
}
.other{
  background: #fff;
  margin-top: 10px!important;
  padding:15px 20px;
  .other-btn{
    cursor: pointer;
    color:#409eff;
  }
}
</style>
