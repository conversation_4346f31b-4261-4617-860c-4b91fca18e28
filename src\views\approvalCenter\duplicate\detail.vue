<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-06-24 11:22:30
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-07-02 09:41:59
-->
<template>
  <el-drawer
    v-model="state.show"
    :close-on-click-modal="false"
    title="审批详情"
    :size="850"
    class="detail_drawer"
  >
    <approvalcontent :id="state.id" :alldata="state.detailInfo" v-loading="state.loading">
      <template #center>
        <!-- 通用 -->
        <general :id="state.id" :alldata="state.detailInfo" v-if="state.type === 1" />
        <!-- 请假 -->
        <leave :id="state.id" :alldata="state.detailInfo" v-if="state.type === 2" />
        <!-- 费用报销 -->
        <expenseReim :id="state.id" :alldata="state.detailInfo" v-if="state.type === 3" />
        <!-- 付款申请 -->
        <payment :id="state.id" :alldata="state.detailInfo" v-if="state.type === 4" />
        <!-- 采购申请 -->
        <purchase :id="state.id" :alldata="state.detailInfo" v-if="state.type === 5" />
        <!-- 合同审批 -->
        <contract :id="state.id" :alldata="state.detailInfo" v-if="state.type === 6" />
      </template>
    </approvalcontent>
    <template #footer>
      <div style="flex: auto">
        <!-- <el-button @click="openSelectUser">抄送</el-button> -->
        <el-button @click="onClickPrint" type="primary">打印</el-button>
      </div>
    </template>
  </el-drawer>
  <PrintDetail
    :show="print.show"
    :type="state.type"
    :alldata="state.detailInfo"
    @close="onClosePrint"
  ></PrintDetail>
  <!-- 选择成员 -->
  <select-user-tree ref="selectUserTreeRef"></select-user-tree>
</template>

<script setup lang="ts">
import { workGetStatusById } from "@/api/workbench";
import { reactive } from "vue";
import approvalcontent from "../components/approvalcontent.vue";
import contract from "../components/contract.vue";
import expenseReim from "../components/expenseReim.vue";
import general from "../components/general.vue";
import leave from "../components/leave.vue";
import payment from "../components/payment.vue";
import purchase from "../components/purchase.vue";
import PrintDetail from "../components/print-detail.vue";

interface IState {
  show: boolean;
  loading: boolean;
  detailInfo: Object;
  id: number;
  type: string | number;
}
const state = reactive<IState>({
  show: false,
  loading: false,
  detailInfo: {},
  id: 0,
  type: 1,
});

const init = (obj) => {
  state.show = true;
  state.id = obj.id;
  state.type = obj.infoType;
  getDetailInfo();
};
const getDetailInfo = () => {
  state.loading = true;
  workGetStatusById(state.id).then((res) => {
    if (res.code == 0) {
      state.detailInfo = res.data;
      state.loading = false;
    } else {
      state.loading = false;
    }
  });
};

//#region 打印相关
const print = reactive({
  show: false,
});
const onClickPrint = () => {
  print.show = true;
};

const onClosePrint = () => {
  print.show = false;
};
//#endregion

defineExpose({
  init,
});
</script>

<style scoped></style>
