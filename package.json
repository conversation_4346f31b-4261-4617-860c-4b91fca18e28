{"name": "yuan-oa", "version": "2.6.0", "type": "module", "scripts": {"dev": "vite", "dev:sit": "vite --mode sit", "build:prod": "vite build --mode production", "build:sit": "vite build --mode sit", "build:strict": "vue-tsc --noEmit && vite build", "preview": "vite preview", "lint": "eslint --fix --ext .vue,.jsx,.ts,.tsx ."}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@form-create/designer": "^3.1.3", "@form-create/element-ui": "^3.1.24", "@kangc/v-md-editor": "2.3.17", "@vue-office/pdf": "^2.0.2", "@vueuse/core": "10.9.0", "@wangeditor/editor": "5.1.23", "@wangeditor/editor-for-vue": "5.1.12", "axios": "1.6.8", "bignumber.js": "^9.1.2", "cropperjs": "1.6.1", "dayjs": "^1.11.11", "echarts": "5.5.0", "element-plus": "2.7.4", "highlight.js": "^11.9.0", "mitt": "3.0.1", "nprogress": "0.2.0", "pinia": "2.1.7", "print-js": "1.6.0", "qrcode": "^1.5.4", "qrcode.vue": "3.4.1", "qs": "6.12.0", "sortablejs": "^1.15.2", "vue": "3.4.21", "vue-dompurify-html": "^4.1.4", "vue-i18n": "9.1.9", "vue-jsonp": "^2.0.0", "vue-router": "4.3.0", "vxe-table": "4.5.21", "xe-utils": "3.5.22"}, "devDependencies": {"@babel/types": "7.24.0", "@types/node": "20.11.28", "@types/nprogress": "0.2.3", "@types/qs": "6.9.12", "@types/sortablejs": "1.15.8", "@vitejs/plugin-vue": "5.0.5", "@vue/compiler-sfc": "3.4.21", "@vue/eslint-config-prettier": "9.0.0", "@vue/eslint-config-typescript": "13.0.0", "@vue/tsconfig": "0.5.1", "eslint": "8.57.0", "eslint-plugin-vue": "9.23.0", "prettier": "3.2.5", "sass": "1.72.0", "typescript": "5.4.2", "vite": "5.2.12", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "0.4.0", "vue-tsc": "2.0.6"}, "license": "MIT"}