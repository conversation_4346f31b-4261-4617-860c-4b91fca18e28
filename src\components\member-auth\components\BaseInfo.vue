<template>
  <div class="info">
    <el-form
      ref="infoRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="140px"
      class="info_form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="companyName" label="企业名称">
            <el-input
              v-model="state.dataForm.companyName"
              placeholder="企业名称"
              @blur="onBlurField('companyName')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.companyName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="unitNature" label="认证状态" class="info_status">
            <div class="info_status_content">
              <div
                class="info_status_content_audit"
                v-if="state.dataForm.auditStatus == '2'"
              >
                审核中
              </div>
              <div
                class="info_status_content_pass"
                v-else-if="state.dataForm.auditStatus == '3'"
              >
                认证通过
              </div>
              <div
                class="info_status_content_pass_no"
                v-else-if="state.dataForm.auditStatus == '4'"
              >
                认证不通过
              </div>
              <div class="info_status_content_no" v-else>未认证</div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="creditCode" label="统一社会信用代码">
            <el-input
              v-model="state.dataForm.creditCode"
              placeholder="统一社会信用代码"
              @blur="onBlurField('creditCode')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.creditCode }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="industry" label="所属行业">
            <fast-select
              v-model="state.dataForm.industry"
              dict-type="industry_type"
              clearable
              placeholder="所属行业"
              style="width: 100%"
              @change="onBlurField('industry')"
              v-if="props.action === 'edit'"
            ></fast-select>
            <div class="info_form_value" v-else>
              {{
                getDictLabel(appStore.dictList, "industry_type", state.dataForm.industry)
              }}
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="areaId" label="企业所在地">
            <el-tree-select
              v-model="state.dataForm.areaId"
              :data="state.areaList"
              value-key="id"
              :render-after-expand="false"
              :props="{ label: 'name', children: 'children' }"
              style="width: 100%"
              clearable
              placeholder="企业所在地"
              @change="onChangeAreaId"
              :disabled="props.action !== 'edit'"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="legalPerson" label="法定代表人">
            <el-input
              v-model="state.dataForm.legalPerson"
              placeholder="法定代表人"
              @blur="onBlurField('legalPerson')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.legalPerson }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item
            prop="registeredCapital"
            label="注册资金(万元)"
            class="info_number"
          >
            <el-input-number
              v-model="state.dataForm.registeredCapital"
              placeholder="注册资金(万元)"
              controls-position="right"
              style="width: 100%"
              @blur="onBlurField('registeredCapital')"
              v-if="props.action === 'edit'"
            ></el-input-number>
            <div class="info_form_value" v-else>
              {{ state.dataForm.registeredCapital }}
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="establishTime" label="成立时间">
            <el-date-picker
              v-model="state.dataForm.establishTime"
              type="date"
              placeholder="成立时间"
              style="width: 100%"
              clearable
              @change="onBlurField('establishTime')"
              v-if="props.action === 'edit'"
            />
            <div class="info_form_value" v-else>{{ state.dataForm.establishTime }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="qualificationOne" label="企业资质">
            <el-cascader :disabled="props.action === 'edit'?false:true" v-model="state.dataForm.qualificationOne" :props="propsQualifications" filterable :options="qualificationsList" style="width: 100%;" clearable @change="onBlurField('qualificationOne')" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="workAddress" label="办公地址">
            <el-input
              v-model="state.dataForm.workAddress"
              placeholder="办公地址"
              @blur="onBlurField('workAddress')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.workAddress }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="introduction" label="单位简介">
            <el-input
              v-model="state.dataForm.introduction"
              placeholder="单位简介"
              type="textarea"
              :rows="4"
              @blur="onBlurField('introduction')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.introduction }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="businessScope" label="经营范围">
            <el-input
              v-model="state.dataForm.businessScope"
              placeholder="经营范围"
              type="textarea"
              :rows="4"
              @blur="onBlurField('businessScope')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.businessScope }}</div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { FormRules } from "element-plus";
import { onMounted, reactive, ref, nextTick, watch } from "vue";
import { getAreaTree } from "@/api/supplier/manage";
import { getDictLabel } from "@/utils/tool";
import { useAppStore } from "@/store/modules/app";
import { useQualificationsListApi } from "@/api/supplier/qualifications";

const appStore = useAppStore();

export interface IBaseInfo {
  companyName: string;
  auditStatus: string;
  creditCode: string;
  industry: string;
  areaId: string;
  legalPerson: string;
  registeredCapital?: number;
  establishTime: string;
  qualificationOne: any;
  workAddress: string;
  introduction: string;
  businessScope: string;
}

const infoRef = ref();

interface IProps {
  action: string;
  baseInfo: IBaseInfo;
}

const props = withDefaults(defineProps<IProps>(), {
  action: "edit",
  baseInfo: () => {
    return {
      companyName: "",
      auditStatus: "",
      creditCode: "",
      industry: "",
      areaId: "",
      legalPerson: "",
      registeredCapital: void 0,
      establishTime: "",
      qualificationOne:[],
      workAddress: "",
      introduction: "",
      businessScope: "",
    };
  },
});

interface IAreaItem {
  id: string;
  name: string;
  disabled: boolean;
  children?: IAreaItem[];
}

interface IState {
  dataForm: IBaseInfo;
  dataRules: FormRules;
  areaList: IAreaItem[];
}

const state = reactive<IState>({
  dataForm: {
    companyName: "",
    auditStatus: "",
    creditCode: "",
    industry: "",
    areaId: "",
    legalPerson: "",
    registeredCapital: void 0,
    establishTime: "",
    qualificationOne:[],
    workAddress: "",
    introduction: "",
    businessScope: "",
  },
  dataRules: {
    companyName: [{ required: true, message: "请输入单位名称", trigger: "blur" }],
    creditCode: [{ required: true, message: "请输入统一社会信用代码", trigger: "blur" }],
    industry: [{ required: true, message: "请选择所属行业", trigger: "change" }],
    areaId: [{ required: true, message: "请选择企业所在地", trigger: "change" }],
    legalPerson: [{ required: true, message: "请输入法定代表人姓名", trigger: "blur" }],
    registeredCapital: [
      { required: true, message: "请输入注册资金(万元)", trigger: "blur" },
    ],
    qualificationOne: [{ required: true, message: "请选择企业资质", trigger: "change" }],
    workAddress: [{ required: true, message: "请输入办公地址", trigger: "blur" }],
    businessScope: [{ required: true, message: "请输入经营范围", trigger: "blur" }],
  },
  areaList: [],
});

watch(
  () => props.baseInfo,
  () => {
    let newBaseInfo = JSON.parse(JSON.stringify(props.baseInfo));
    state.dataForm = newBaseInfo;
  },
  { immediate: true }
);
const qualificationsList = ref([]);
const propsQualifications = {
  value: 'id',
  label: 'name',
  multiple: true,
  // emitPath:false//绑定的内容只获取最后一级的value值
}
const emit = defineEmits<{
  (e: "on-change-value", type: string, field: string, value: any): void;
  (e: "emit-ref", baseInfoRef: any): void;
}>();

onMounted(() => {
  getQualificationsList()
  GetAreaTree();
  nextTick(() => {
    emit("emit-ref", infoRef.value);
  });
});

const onBlurField = (field: string) => {
  // @ts-ignore
  emit("on-change-value", "baseInfo", field, state.dataForm[field]);
};

const onChangeAreaId = (areaItem: IAreaItem) => {
  // @ts-ignore
  emit("on-change-value", "baseInfo", "areaId", areaItem);
  // console.log(state.dataForm.areaId);
  // if (!areaItem.disabled) {
  //   // @ts-ignore
  //   emit("on-change-value", "baseInfo", "areaId", areaItem.id);
  // }
};

const GetAreaTree = () => {
  getAreaTree().then((res: any) => {
    if (res.code === 0) {
      let newAreaList = computeAreaList(res.data);
      state.areaList = newAreaList;
    }
  });
};

const computeAreaList = (list: IAreaItem[]) => {
  let newAreaList: IAreaItem[] = list.map((item) => {
    return {
      id: item.id + "",
      name: item.name,
      // disabled: (item.children ?? []).length > 0 ? true : false,
      disabled: false,
      children: computeAreaList(item.children ?? []),
    };
  });
  return newAreaList;
};

// 获取企业资质列表
const getQualificationsList = async () => {
  const res = await useQualificationsListApi();
  qualificationsList.value = res.data;
};
</script>
<style lang="scss" scoped>
.info {
  width: 100%;
  max-width: 1000px;
  margin-top: 10px;
  &_status_content {
    &_no {
      color: #909399;
    }
    &_audit {
      color: #409eff;
    }
    &_pass {
      color: #95d475;
      &_no {
        color: rgb(245, 108, 108);
      }
    }
  }
  &_number {
    :deep(.el-input__inner) {
      text-align: left;
    }
  }
}
</style>
