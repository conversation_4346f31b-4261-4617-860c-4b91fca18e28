<template>
  <div class="info">
    <el-form
      ref="infoRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="170px"
      class="normalform"
    >
      <div class="action_base">
        <div class="action_title">
          <ContentTitle title="基本信息"></ContentTitle>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="packageName" label="采购计划">
              <div style="display: flex; align-items: center; width: 100%">
                <el-input
                  readonly
                  v-model="dataForm.packageName"
                  placeholder="采购计划"
                ></el-input>
                <el-button type="primary" style="margin-left: 10px" @click="pickplan()"
                  >选择</el-button
                >
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="packageNo" label="采购计划编号">
              <el-input
                readonly
                v-model="dataForm.packageNo"
                placeholder="采购计划编号"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="title" label="采购邀请函名称">
              <el-input v-model="dataForm.title" placeholder="采购邀请函名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="packageMode" label="采购模式">
              <fast-radio-group
                v-model="dataForm.packageMode"
                dict-type="bulletin_package_mode"
              ></fast-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="signStartDate" label="报名开始时间">
              <el-date-picker
                v-model="dataForm.signStartDate"
                type="datetime"
                placeholder="报名开始时间"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="signEndDate" label="报名截止时间">
              <el-date-picker
                v-model="dataForm.signEndDate"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="报名截止时间"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="bidStartDate" label="报价开始时间">
              <el-date-picker
                v-model="dataForm.bidStartDate"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="报价开始时间"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="bidEndDate" label="报价截止时间">
              <el-date-picker
                v-model="dataForm.bidEndDate"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="报价截止时间"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item prop="supplyAddress" label="项目所在地">
              <el-input
                v-model="dataForm.supplyAddress"
                placeholder="项目所在地"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item prop="deliveryAddress" label="送货地址">
              <el-input
                v-model="dataForm.deliveryAddress"
                placeholder="送货地址"
                readonly
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="signFlag" label="是否报名审核">
              <el-radio-group v-model="dataForm.signFlag">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="needAudit" label="是否内部审核">
              <el-radio-group v-model="dataForm.needAudit">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="bidbondFlag" label="投标保证金">
              <el-radio-group v-model="dataForm.bidbondFlag">
                <el-radio :label="1">收取</el-radio>
                <el-radio :label="0">不收取</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="dataForm.bidbondFlag == 1">
            <el-form-item prop="bidbond" label="投标保证金金额（元）">
              <el-input
                v-model="dataForm.bidbond"
                placeholder="投标保证金金额"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="指定供应商" prop="invitationBidders">
              <el-button type="primary" @click="addsupplier()">新增供应商</el-button>
              <el-table
                v-loading="state.dataListLoading"
                :data="dataForm.invitationBidders"
                border
                style="margin-top: 16px"
              >
                <el-table-column
                  type="index"
                  label="序号"
                  header-align="center"
                  align="center"
                  width="70"
                ></el-table-column>
                <el-table-column
                  prop="bidderName"
                  label="供应商名称"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="contactName"
                  label="联系人"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="contactPhone"
                  label="联系电话"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column label="操作" header-align="center" align="center">
                  <template #default="scope">
                    <el-button type="primary" link @click="deleteHandle(scope.$index)">
                      删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="邀请函类型">
              <el-radio-group v-model="dataForm.makeType">
                <el-radio :label="1">文本模式</el-radio>
                <el-radio :label="0">上传pdf公告文件</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="dataForm.makeType == 1">
            <el-form-item prop="content" label="邀请函内容">
              <WangEditor v-model="dataForm.content"></WangEditor>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-else>
            <el-form-item prop="contentAttach" label="pdf公告文件">
              <el-upload
                v-model:file-list="state.fileList"
                :headers="{ Authorization: cache.getToken() }"
                :action="constant.uploadUrl"
                :before-upload="beforeUpload"
                :on-success="handleSuccess"
                :on-remove="handleRemove"
                accept=".pdf"
                limit="1"
              >
                <el-button type="primary" :disabled="state.fileList.length >= 1"
                  >上传</el-button
                >
                <template #tip>
                  <div class="el-upload__tip">支持pdf格式</div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="action_purchase">
        <div class="action_title">
          <ContentTitle title="采购人信息"></ContentTitle>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="buyersName" label="采购单位名称">
              <el-input
                v-model="dataForm.buyersName"
                placeholder="采购单位名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="buyersAddress" label="采购单位地址">
              <el-input
                v-model="dataForm.buyersAddress"
                placeholder="采购单位地址"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="buyersLinkerName" label="联系人">
              <el-input
                v-model="dataForm.buyersLinkerName"
                placeholder="联系人"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="buyersLinkerTel" label="联系电话">
              <el-input
                v-model="dataForm.buyersLinkerTel"
                placeholder="联系电话"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <!-- 选择计划 -->
    <selectplan
      v-if="state.selectplanShow"
      ref="selectplanRef"
      @select-result="selectPlanres"
    ></selectplan>
    <!-- 选择供应商 -->
    <supplier-list
      v-if="state.supplierListShow"
      ref="supplierListRef"
      @select-result="selectBidres"
    ></supplier-list>
  </div>
</template>
<script setup lang="ts">
import { reactive, nextTick, ref, inject, onMounted, watch } from "vue";
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import selectplan from "./selectplan.vue";
import supplierList from "./supplierList.vue";
import WangEditor from "@/components/wang-editor/index.vue";
import constant from "@/utils/constant";
import { ElMessage } from "element-plus";
import cache from "@/utils/cache";

const state = reactive({
  selectplanShow: false,
  materialList: [],
  fileList: [],
  dataListLoading: false,
  supplierListShow: false,
});
const dataForm = inject("dataForm");
const dataRules = inject("dataRules");
const selectplanRef = ref();
const infoRef = ref();
const supplierListRef = ref();
const emit = defineEmits(["refreshProvide", "pickPlanres"]);

watch(dataForm, (newValue, oldValue) => {
  if (dataForm.contentAttach) {
    state.fileList = [].concat(dataForm.contentAttach);
  }
});

const handleSuccess = (res, file) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  dataForm.contentAttach = Object.assign({}, res.data);
};
const beforeUpload = (file) => {
  let types = ["application/pdf"];
  if (!types.includes(file.type)) {
    ElMessage.error("格式不支持");
    return false;
  }
  return true;
};
const handleExceed = () => {
  ElMessage.error("文件超出限制个数");
};

// 选择计划
const pickplan = () => {
  state.selectplanShow = true;
  nextTick(() => {
    selectplanRef.value.init();
  });
};

// 选取采购计划回调
const selectPlanres = (res) => {
  if (res.packageId) {
    dataForm.packageId = res.packageId;
    dataForm.packageNo = res.packageNo;
    dataForm.packageName = res.packageName;
    dataForm.deliveryAddress = res.deliveryAddress;
    emit("pickPlanres", res);
  }
};

// 表单提交
const submitHandle = () => {
  infoRef.value.validate((valid: boolean) => {
    if (!valid) {
      emit("refreshProvide", false);
      return false;
    }
    emit("refreshProvide", true);
  });
};

// 新增供应商
const addsupplier = () => {
  state.supplierListShow = true;
  nextTick(() => {
    supplierListRef.value.init();
  });
};
const selectBidres = (res) => {
  let bidders = [];
  for (let rr of res) {
    bidders.push({
      bidderId: rr.userDetailsId,
      bidderName: rr.userDetailsName,
      contactPhone: rr.contactPhone,
      contactName: rr.contactName,
    });
  }
  dataForm.invitationBidders = bidders;
};
const deleteHandle = (ind) => {
  dataForm.invitationBidders.splice(ind, 1);
};

const handleRemove = (file, files) => {
  state.fileList = [];
  dataForm.contentAttach = null;
};

defineExpose({
  submitHandle,
});
</script>

<style lang="scss" scoped>
.info {
  width: 100%;
  margin-top: 10px;
}
::v-deep(.normalform .el-form-item__content) {
  width: 100%;
}
::v-deep(.el-date-editor.el-input) {
  width: 100%;
}
.list_file_content {
  margin-right: 5px;
}
.action_title {
  margin-bottom: 25px;
}
</style>
