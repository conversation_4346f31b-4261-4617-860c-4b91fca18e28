<template>
  <div class="list">
    <div class="list-table">
      <el-table
        v-loading="state.dataListLoading"
        show-overflow-tooltip
        :data="state.dataList"
        border
      >
        <el-table-column
          type="index"
          label="序号"
          header-align="center"
          align="center"
          width="70"
        ></el-table-column>
        <el-table-column
          prop="materialName"
          label="产品名称"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="materialType"
          label="产品型号（规格参数）"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="materialUnit"
          label="单位"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="materialQuantity"
          label="数量"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="comment"
          label="备注"
          header-align="center"
          align="center"
        ></el-table-column>
      </el-table>
    </div>
    <div class="list-page">
      <el-pagination
        :current-page="state.pageNo"
        :page-sizes="state.pageSizes"
        :page-size="state.pageSize"
        :total="state.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="sizeChangeHandle"
        @current-change="currentChangeHandle"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive, watch } from "vue";

//#region props相关
interface IProps {
  packageId?: number;
  activeName: string;
}
const props = withDefaults(defineProps<IProps>(), {
  activeName: "quotation",
});
//#endregion

const state = reactive<IHooksOptions>({
  createdIsNeed: false,
  queryForm: {
    packageId: void 0,
  },
  dataListUrl: "/purchase/packageMaterial/page",
});

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

watch(
  () => props.activeName,
  () => {
    if (props.activeName === "buy" && props.packageId) {
      state.queryForm.packageId = props.packageId;
      state.pageNo = 1;
      getDataList();
    }
  }
);
</script>
<style lang="scss" scoped></style>
