<template>
  <div class="info">
    <el-form
      ref="infoRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="140px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="openingBank" label="开户银行">
            <el-input
              v-model="state.dataForm.openingBank"
              placeholder="开户银行"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="accountNumber" label="基本账户账号">
            <el-input
              v-model="state.dataForm.accountNumber"
              placeholder="基本账户账号"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="unit" label="注册资金单位">
            <el-input v-model="state.dataForm.unit" placeholder="注册资金单位"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="currency" label="注册资本币种">
            <el-input
              v-model="state.dataForm.currency"
              placeholder="注册资本币种"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="registeredCapital" label="注册资本">
            <el-input
              v-model="state.dataForm.registeredCapital"
              placeholder="注册资本"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { reactive } from "vue";
const state = reactive({
  dataForm: {},
  dataRules: {},
});
</script>
<style lang="scss" scoped>
.info {
  width: 100%;
  max-width: 1000px;
  margin-top: 10px;
}
</style>
