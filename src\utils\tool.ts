import { useAppStore } from '@/store/modules/app'
import constant from '@/utils/constant'
import dayjs from 'dayjs'
import type { App, Plugin } from 'vue'
import service from './request'

// 把路径转换成驼峰命名
export const pathToCamel = (path: string): string => {
  return path.replace(/\/(\w)/g, (all, letter) => letter.toUpperCase())
}

// 是否外链
export const isExternalLink = (url: string): boolean => {
  return /^(https?:|\/\/|http?:|\/\/|^{{\s?apiUrl\s?}})/.test(url)
}

// 替换外链参数
export const replaceLinkParam = (url: string): string => {
  return url.replace('{{apiUrl}}', constant.apiUrl)
}

// 转换文件大小格式
export const convertSizeFormat = (size: number): string => {
  const unit = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB']
  let index = Math.floor(Math.log(size) / Math.log(1024))
  let newSize = size / Math.pow(1024, index)

  // 保留的小数位数
  return newSize.toFixed(2) + ' ' + unit[index]
}

// 获取svg图标(id)列表
export const getIconList = (): string[] => {
  const rs: string[] = []
  const list = document.querySelectorAll('svg symbol[id^="icon-"]')   // 引入 iconfont 后获取的方式
  for (let i = 0; i < list.length; i++) {
    rs.push(list[i].id)
  }
  return rs
}

// 获取字典Label
export const getDictLabel = (dictList: any[], dictType: string, dictValue: string) => {
  const type = dictList.find((element: any) => element.dictType === dictType)
  if (type) {
    const val = type.dataList.find((element: any) => element.dictValue === dictValue + '')
    if (val) {
      return val.dictLabel
    } else {
      return dictValue
    }
  } else {
    return dictValue
  }
}

// 获取字典Label样式
export const getDictLabelClass = (dictList: any[], dictType: string, dictValue: string): string => {
  const type = dictList.find((element: any) => element.dictType === dictType)
  if (type) {
    const val = type.dataList.find((element: any) => element.dictValue === dictValue + '')
    if (val) {
      return val.labelClass
    } else {
      return ''
    }
  } else {
    return ''
  }
}

export const getDictLabelList = (dictType: string, dictValue: string): string => {
  if (Number.isInteger(dictValue)) {
    dictValue = dictValue + ''
  }

  if (!dictValue) {
    return ''
  }

  const appStore = useAppStore()

  let result = ''
  dictValue.split(',').forEach(value => {
    const classStyle = getDictLabelClass(appStore.dictList, dictType, value)
    const label = getDictLabel(appStore.dictList, dictType, value)

    if (classStyle) {
      result += `<span class="el-tag el-tag--${classStyle} el-tag--${appStore.componentSize}">${label}</span>&nbsp;`
    } else {
      result += label + '&nbsp;'
    }
  })

  return result
}

// 获取字典数据列表
export function getDictDataList(dictList: any[], dictType: string) {
  const type = dictList.find((element: any) => element.dictType === dictType)
  if (type) {
    return type.dataList
  } else {
    return []
  }
}

// 全局组件安装
export const withInstall = <T>(component: T, alias?: string) => {
  const comp = component as any
  comp.install = (app: App) => {
    app.component(comp.name || comp.displayName, component)
    if (alias) {
      app.config.globalProperties[alias] = component
    }
  }
  return component as T & Plugin
}

// 数字转换为汉字大写
export const changeToChinese = (data: string) => {
  const a = data.split('.')//分割
  const b = a[0].split(',')//分割整数
  if (a.length === 1) a.push('')
  let combin = ''//整数定义
  b.forEach(ite => combin += ite)//合并整数
  const Aword = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖']
  const Bword = ['角', '分']
  const Cword = ['', '拾', '佰', '仟']
  const Dword = ['', '万', '亿']
  const Eword = ['零', '元', '整']
  let returnWords = ''//返回值
  if (combin.length > 12) {
    console.error('超出限制')
    return ''
  }
  //整数部分拼接
  if (parseInt(combin) > 0) {
    let t = 0
    for (let i = 0; i < combin.length; i++) {
      const currentSite = combin.length - i - 1 //当前在第几位
      const currentData = combin.slice(i, i + 1)//当前元素
      const bigUnit = currentSite / 4 //4位一组单位选取 万|亿
      const smallUnit = currentSite % 4 //4位一组的位置 拾|佰|仟
      if (currentData === '0') { //是否为0
        t++
      } else {
        if (t > 0) {
          returnWords += Aword[0]//前面有0补零
        }
        t = 0//重置
        returnWords += Aword[parseInt(currentData)] + Cword[smallUnit]
      }
      if (smallUnit === 0 && t < 4) {
        returnWords += Dword[bigUnit]
        t = 0//重置
      }
    }
    returnWords += Eword[1]
  }
  //小数部分拼接
  //小数部分为全为0时
  if (a[1] === '00' || a[1] === '') {
    //金额为0时
    if (returnWords === '') {
      returnWords = Eword[0] + Eword[1] + Eword[2]
    } else {
      returnWords += Eword[2]
    }
    return returnWords
  } else {
    for (let i = 0; i < a[1].length; i++) {
      const currentData = a[1].slice(i, i + 1)//当前元素
      if (currentData !== '0') {
        returnWords += Aword[parseInt(currentData)] + Bword[i]
      } else if (i === 0) {
        returnWords += Aword[parseInt(currentData)]
      }
    }
  }
  return returnWords
}

// 数字大写 一二三
export const changeToChineseNumber = (data: string) => {
  const chineseNumbers = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
  const chineseUnits = ['', '十', '百', '千', '万', '亿'];

  if (value === 0) {
    return chineseNumbers[0];
  }

  let result = '';
  let unitIndex = 0;

  const valueOle = value;

  while (value > 0) {
    const digit = value % 10;
    if (digit !== 0 || unitIndex === 1) {
      result = chineseNumbers[digit] + chineseUnits[unitIndex] + result;
    }
    value = Math.floor(value / 10);
    unitIndex++;
  }

  if (valueOle > 9 && valueOle < 20) {
    // 把10：十一，处理为：十
    result = result.slice(1);
    console.log('111', result.slice(1));
  }

  return result;
}

export const downloadFile = async (fileUrl: string, fileName?: string) => {
  const response = await service.get(fileUrl, {
    responseType: "blob", // important
  });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement("a");
  link.href = url;
  if (fileName) {
    link.setAttribute("download", fileName);
  }
  else {
    let fileUrlList = fileUrl.split('.');
    let fileType = fileUrlList[fileUrlList.length - 1];
    let newFileName = dayjs().format('YYYYMMDDHHmmsss') + '.' + fileType;
    link.setAttribute("download", newFileName);
  }
  document.body.appendChild(link);
  link.click();
}

// 树结构定位
// 用法：findNodeInTree(treeData, node => node.id === 4);
export const findNodeInTree = (tree, predicate) => {
  for (let i = 0; i < tree.length; i++) {
    if (predicate(tree[i])) {
      return tree[i];
    }
    if (tree[i].children && tree[i].children.length > 0) {
      const foundNode = findNodeInTree(tree[i].children, predicate);
      if (foundNode) {
        return foundNode;
      }
    }
  }
  return null;
}
// 树结构更新属性
// 树，匹配条件，更新函数
export const updateTreeNode = (tree, predicate, updateFn) => {
  // 递归函数用于遍历树
  function traverse(node) {
    if (predicate(node)) {
      // 如果找到匹配的节点，应用更新函数
      updateFn(node);
    }
    // 递归遍历子节点
    if (node.children) {
      node.children.forEach(traverse);
    }
  }
 
  // 从根节点开始遍历
  traverse(tree);
}

// 获取当天是星期几
export const getDayOfWeek=(dateString:string)=>{
  const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  const date = new Date(dateString);
  return days[date.getDay()];
}

// 获取时间格式 2023-11-08 19:05:05
export const getTimeFull=()=>{
  // 创建一个 Date 对象
  var today = new Date();
  
  // 获取年、月、日、时、分、秒
  var year = today.getFullYear();
  var month = today.getMonth() + 1; // 月份是从 0 开始计数的，需要加1
  var day = today.getDate();
  var hours = today.getHours();
  var minutes = today.getMinutes();
  var seconds = today.getSeconds();
  
  // 格式化输出
  var formattedTime = year + "-" + 
                    (month < 10 ? "0" : "") + month + "-" + 
                    (day < 10 ? "0" : "") + day + " " + 
                    (hours < 10 ? "0" : "") + hours + ":" + 
                    (minutes < 10 ? "0" : "") + minutes + ":" + 
                    (seconds < 10 ? "0" : "") + seconds;
  return formattedTime;
}