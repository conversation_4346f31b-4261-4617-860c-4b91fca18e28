<template>
  <el-drawer
    v-model="state.visible"
    :close-on-click-modal="false"
    destroy-on-close
    title="打印预览"
    size="700"
    @close="onClickClose"
  >
    <div class="detail" ref="refPrint">
      <!-- 通用 -->
      <PrintGeneral
        :showDetail="state.showDetail"
        :showFile="state.showFile"
        :showReply="state.showReply"
        :alldata="props.alldata"
        v-if="props.type === 1"
      ></PrintGeneral>
      <!-- 请假 -->
      <PrintLeave
        :showDetail="state.showDetail"
        :showFile="state.showFile"
        :showReply="state.showReply"
        :alldata="props.alldata"
        v-else-if="props.type === 2"
      ></PrintLeave>
      <!-- 费用报销 -->
      <PrintExpenseReim
        :showDetail="state.showDetail"
        :showFile="state.showFile"
        :showReply="state.showReply"
        :alldata="props.alldata"
        v-else-if="props.type === 3"
      >
      </PrintExpenseReim>
      <!-- 付款申请 -->
      <PrintPayment
        :showDetail="state.showDetail"
        :showFile="state.showFile"
        :showReply="state.showReply"
        :alldata="props.alldata"
        v-else-if="props.type === 4"
      >
      </PrintPayment>
      <!-- 采购申请 -->
      <PrintPurchase
        :showDetail="state.showDetail"
        :showFile="state.showFile"
        :showReply="state.showReply"
        :alldata="props.alldata"
        v-else-if="props.type === 5"
      >
      </PrintPurchase>
      <!-- 合同审批 -->
      <PrintContract
        :showDetail="state.showDetail"
        :showFile="state.showFile"
        :showReply="state.showReply"
        :alldata="props.alldata"
        v-else-if="props.type === 6"
      >
      </PrintContract>
    </div>
    <template #footer>
      <div class="print-footer">
        <div class="print-footer-left">
          <div>选择打印：</div>
          <div>
            <el-checkbox v-model="state.showDetail">事由</el-checkbox>
            <el-checkbox v-model="state.showFile">附件</el-checkbox>
            <el-checkbox v-model="state.showReply">回复</el-checkbox>
          </div>
        </div>
        <div class="print-footer-right">
          <el-button @click="onClickClose">取消</el-button>
          <el-button type="primary" @click="onConfirmPrint">打印</el-button>
        </div>
      </div>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { nextTick, reactive, ref, watch } from "vue";
import { printHtml } from "@/utils/print";
import PrintGeneral from "./print-general.vue";
import PrintLeave from "./print-leave.vue";
import PrintExpenseReim from "./print-expense-reim.vue";
import PrintPayment from "./print-payment.vue";
import PrintPurchase from "./print-purchase.vue";
import PrintContract from "./print-contract.vue";

interface IProps {
  show: boolean;
  type: number;
  alldata: object;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  type: 1,
  alldata: () => {
    return {};
  },
});

interface IState {
  visible: boolean;
  showDetail: boolean;
  showFile: boolean;
  showReply: boolean;
}

const state = reactive<IState>({
  visible: false,
  showDetail: true,
  showFile: true,
  showReply: true,
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      state.visible = true;
    }
  }
);

const emit = defineEmits<{
  (e: "close"): void;
}>();

const refPrint = ref();

const onConfirmPrint = () => {
  // 获取打印内容
  const content = refPrint.value.innerHTML;

  // 创建 iframe 并插入内容
  const printFrame = document.createElement("iframe");
  printFrame.style.position = "absolute";
  printFrame.style.width = "0";
  printFrame.style.height = "0";
  printFrame.style.border = "none";
  document.body.appendChild(printFrame);

  // 写入内容并打印
  printFrame.contentDocument?.write(printHtml(content));
  printFrame.contentDocument?.close();

  printFrame.onload = () => {
    printFrame.contentWindow?.print();
    document.body.removeChild(printFrame);
  };
};

const onClickClose = () => {
  state.visible = false;
  emit("close");
};
</script>
<style lang="scss" scoped>
.print {
  &-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &-left {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
}
</style>
