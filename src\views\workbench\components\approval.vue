<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-06-19 16:29:28
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-06-29 10:59:02
-->
<template>
  <div class="title-line">
    <div class="title">审批流程</div>
  </div>
  <div class="content">
    <el-timeline>
      <el-timeline-item :icon="Connection">
        <div class="approval-line borderb">
          <div class="approval-line-lft">审批人 <span class="tag">依次审批</span></div>
          <div class="approval-line-rht">
            <div class="user-box" v-for="(item, index) in state.userList">
              <div class="user">
                <el-avatar
                  shape="circle"
                  :size="30"
                  :src="item.avatar ?? defaultAvatar"
                ></el-avatar>
                <div>{{ item.name }}</div>
                <svg-icon
                  icon="icon-close-circle-fill"
                  class="user-del"
                  @click="deluser(index)"
                ></svg-icon>
              </div>
              <svg-icon icon="icon-doubleright" class="user-next"></svg-icon>
            </div>
            <div class="plus" @click="openSelectUser(1)">
              <svg-icon icon="icon-plus"></svg-icon>
            </div>
          </div>
        </div>
      </el-timeline-item>
      <el-timeline-item :icon="Connection">
        <div class="approval-line">
          <div class="approval-line-lft">抄送人</div>
          <div class="approval-line-rht">
            <div class="user"></div>
            <div class="user-box" v-for="(item, index) in state.copyuserList">
              <div class="user">
                <el-avatar
                  v-if="item.avatar"
                  shape="circle"
                  :size="30"
                  :src="item.avatar || defaultAvatar"
                ></el-avatar>
                <svg-icon v-else icon="icon-morentouxiang" size="26"></svg-icon>
                <div>{{ item.name }}</div>
                <svg-icon
                  icon="icon-close-circle-fill"
                  class="user-del"
                  @click="delusercopy(index)"
                ></svg-icon>
              </div>
              <!-- <svg-icon icon="icon-doubleright" class='user-next'></svg-icon> -->
            </div>
            <div class="plus" @click="openSelectUser(2)">
              <svg-icon icon="icon-plus"></svg-icon>
            </div>
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>

  <!-- 选择成员 -->
  <select-user-tree
    ref="selectUserTreeRef"
    @select-user="getSelectUser"
  ></select-user-tree>
</template>

<script setup lang="ts">
import { Connection } from "@element-plus/icons-vue";
import { reactive, ref } from "vue";
import selectUserTree from "./selectUserTree.vue";
import defaultAvatar from "@/assets/avatar.png";
const selectUserTreeRef = ref<InstanceType<typeof selectUserTree>>();
const state = reactive({
  userList: [],
  copyuserList: [],
  type: 1,
});
const emit = defineEmits(["emitUser"]);

const init = (arr1, arr2) => {
  state.userList = arr1;
  state.copyuserList = arr2;
};
const openSelectUser = (type) => {
  state.type = type;
  if (state.type == 1) {
    selectUserTreeRef.value?.init(state.userList);
  } else {
    selectUserTreeRef.value?.init(state.copyuserList);
  }
};

const getSelectUser = (arr) => {
  if (state.type == 1) {
    state.userList = arr;
    emit("emitUser", state.userList);
  } else {
    state.copyuserList = arr;
    emit("emitUserCopy", state.copyuserList);
  }
};

const deluser = (index) => {
  state.userList.splice(index, 1);
};
const delusercopy = (index) => {
  state.copyuserList.splice(index, 1);
};
const clearInfo = () => {
  state.userList = [];
  state.copyuserList = [];
};

defineExpose({
  clearInfo,
  init,
});
</script>

<style scoped lang="scss">
.title-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .title {
    text-align: center;
    padding: 8px 0;
    width: 100px;
    border-left: 3px solid #409eff;
  }
}
.approval-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  &-lft {
    .tag {
      color: #409eff;
      border: 1px solid #409eff;
      display: inline-block;
      border-radius: 15px;
      padding: 3px 10px;
      margin-left: 5px;
    }
  }
  &-rht {
    display: flex;
  }
  .user-box {
    display: flex;
    align-items: center;
    margin: 0 10px;
  }
  .user {
    padding: 0 10px;
    position: relative;
    text-align: center;
    .user-del {
      position: absolute;
      right: 8px;
      top: -6px;
    }
    .user-next {
      margin: 0 10px;
    }
  }
  .plus {
    color: #409eff;
    padding: 10px;
    height: 16px;
    border: 1px dashed #409eff;
    border-radius: 3px;
    cursor: pointer;
  }
}
.borderb {
  border-bottom: 1px solid #dcdfe6;
}
:deep(.el-timeline-item) {
  padding-bottom: 0;
}
:deep(.el-timeline-item__tail) {
  top: 30px;
}
:deep(.el-timeline-item__node) {
  top: 20px;
}
</style>
