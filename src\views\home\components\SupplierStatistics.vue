<template>
  <div class="audit">
    <div class="statistics-title">
      <HomeTitle icon="icon-tongji" title="项目统计"> </HomeTitle>
    </div>
    <div class="audit-content">
      <div class="audit-content-item">
        <div class="audit-content-item-icon">
          <svg-icon icon="icon-yicanyuxiangmu1" class-name="svg-size" />
        </div>
        <div class="audit-content-item-content">
          <div class="audit-content-item-content-number">{{ state.packInNum }}</div>
          <div class="audit-content-item-content-text">已参与项目</div>
        </div>
      </div>
      <div class="audit-content-divider"></div>
      <div class="audit-content-item">
        <div class="audit-content-item-icon">
          <svg-icon icon="icon-leijizhongbiaojine" class-name="svg-size" />
        </div>
        <div class="audit-content-item-content">
          <div class="audit-content-item-content-number">
            {{ state.packBidMoney }}
          </div>
          <div class="audit-content-item-content-text">累计中标金额</div>
        </div>
      </div>
      <div class="audit-content-divider"></div>
      <div class="audit-content-item">
        <div class="audit-content-item-icon">
          <svg-icon icon="icon-zhongbiaoxiangmu" class-name="svg-size" />
        </div>
        <div class="audit-content-item-content">
          <div class="audit-content-item-content-number">
            {{ state.packBidNum }}
          </div>
          <div class="audit-content-item-content-text">中标项目</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { getSupplierStatistics } from "@/api/home";
import HomeTitle from "./HomeTitle.vue";
import { onMounted, reactive } from "vue";

interface IState {
  packInNum: number;
  packBidNum: number;
  packBidMoney: number;
}

const state = reactive<IState>({
  packInNum: 0,
  packBidNum: 0,
  packBidMoney: 0,
});
onMounted(() => {
  getSupplierStatistics().then((res: any) => {
    console.log(res);
    if (res.code === 0) {
      state.packInNum = res.data.packInNum;
      state.packBidNum = res.data.packBidNum;
      state.packBidMoney = res.data?.packBidMoney ?? 0;
    }
  });
});
</script>
<style lang="scss" scoped>
.audit {
  background-color: #ffffff;
  padding: 16px 10px 30px;
  border-radius: 4px;
  &-content {
    display: flex;
    align-items: center;
    margin-top: 20px;
    &-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      &-icon {
        :deep(.svg-size) {
          font-size: 40px;
        }
      }
      &-content {
        margin-left: 16px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        &-number {
          font-size: 24px;
          font-weight: bold;
          color: #1a90fe;
        }
        &-text {
          font-size: 14px;
          color: #999999;
        }
      }
    }
    &-divider {
      width: 1px;
      height: 40px;
      background-color: #e8e8e8;
    }
  }
}
</style>
