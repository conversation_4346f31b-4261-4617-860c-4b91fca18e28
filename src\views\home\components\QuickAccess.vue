<template>
  <div class="quick">
    <div class="quick-title">
      <HomeTitle icon="icon-daishenpi" title="快速入口"> </HomeTitle>
    </div>
    <div class="quick-content">
      <div
        class="quick-content-item"
        v-for="item in functionList"
        :key="item.key"
        @click="onQuickEnter(item.url)"
      >
        <div class="quick-content-item-icon">
          <svg-icon :icon="item.icon" class-name="svg-size" />
        </div>
        <div class="quick-content-item-label">{{ item.label }}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useRouter } from "vue-router";
import HomeTitle from "./HomeTitle.vue";

const router = useRouter();

const functionList = [
  {
    key: "project",
    label: "项目",
    icon: "icon-caigouxiangmu",
    url: "/purchase/project/index",
  },
  {
    key: "plan",
    label: "采购计划",
    icon: "icon-caigoujihua",
    url: "/purchase/plan/index",
  },
  {
    key: "notice",
    label: "采购公告",
    icon: "icon-caigougonggao",
    url: "/purchase/notice/index",
  },
  {
    key: "invitation",
    label: "邀请函",
    icon: "icon-yaoqinghan",
    url: "/purchase/invation/index",
  },
  {
    key: "project",
    label: "保证金管理",
    icon: "icon-toubiaobaozhengjin",
    url: "/bid/deposit/index",
  },
  {
    key: "project",
    label: "参与审核",
    icon: "icon-canyushenhe",
    url: "/bid/apply/index",
  },
  // {
  //   key: "project",
  //   label: "中标服务费",
  //   icon: "icon-zhongbiaofuwufei",
  //   url: "/sppurchase/bidcharge/index",
  // },
];

const onQuickEnter = (url: string) => {
  router.push(url);
};
</script>
<style lang="scss" scoped>
.quick {
  background-color: #ffffff;
  padding: 16px 10px;
  border-radius: 4px;
  &-content {
    display: flex;
    align-items: center;
    margin-top: 16px;
    &-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 136px;
      height: 106px;
      cursor: pointer;
      &-icon {
        :deep(.svg-size) {
          font-size: 44px;
        }
      }
      &-label {
        margin-top: 8px;
        font-size: 14px;
        color: #333333;
      }
    }
  }
}
</style>
