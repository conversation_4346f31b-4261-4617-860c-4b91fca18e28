import service from '@/utils/request'

export interface IActionMaterial {
  id?: number
  categoryId?: number
  materialNo: string
  materialName: string
  materialType: string
  materialSpec: string
  materialUnit: string
  comment: string
}

//获取物料详情
export const getMaterialInfo = (id: number) => {
  return service.get(`/purchase/material/${id}`)
}

//新增物料分类
export const addCategory = (reqData: IActionMaterial) => {
  return service.post(`/purchase/material`, reqData)
}

//修改物料分类
export const updateCategory = (reqData: IActionMaterial) => {
  return service.put(`/purchase/material`, reqData)
}

//获取物料列表
// export interface IGetMaterialList {
//   page?: number
//   limit?: number
//   categoryId?: number
//   materialName?: string
//   materialNo?: string
// }
// export const getMaterialList = (reqData: IGetMaterialList) => {
//   return service.get(`/purchase/material/page`, {
//     params: reqData
//   })
// }