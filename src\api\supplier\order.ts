import service from "@/utils/request"

export const getOrderDetail = (id: string) => {
  return service.get(`/work/record/${id}`)
}

export const exportOrderData = (reqData: any) => {
  return service.get(`/work/record/export`, {
    params: reqData,
    responseType: 'blob'
  })
}

export const applyInvoice = (reqData: any) => {
  return service.put(`/work/record`, reqData)
}

export const deleteInvoiceInfo = (id: string) => {
  return service.delete(`/work/info`, {
    data: [id]
  })
}