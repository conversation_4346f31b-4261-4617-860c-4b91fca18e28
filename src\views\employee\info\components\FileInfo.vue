<template>
  <el-card>
    <div class="info">
      <div class="info-title">
        <div class="info-title-text">
          <InfoTitle title="个人资料"></InfoTitle>
        </div>
        <div class="info-title-divider"></div>
        <div class="info-title-action" v-if="props.allowEdit && !isEdit">
          <el-button type="text" @click="onClickEdit">编辑</el-button>
        </div>
      </div>
      <div class="info-desc">
        单个文件大小不超过20M，可支持的文件格式有jpg，png,jpeg等
      </div>
      <div class="info-list">
        <el-form :model="dataForm" ref="dataFormRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="idCardFront" label="身份证正面">
                <div class="idcard">
                  <div v-if="dataForm.idCardFront.length > 0">
                    <div
                      class="idcard_img"
                      v-for="item in dataForm.idCardFront"
                      :key="item.url"
                    >
                      <el-image :src="item.url" class="idcard_img_main"></el-image>
                      <div class="idcard_img_action">
                        <el-icon
                          class="idcard_img_action_icon"
                          @click="onPreviewImage(item.url)"
                        >
                          <View />
                        </el-icon>
                        <el-icon
                          class="idcard_img_action_icon"
                          style="margin-left: 10px"
                          @click="onDeleteImage('idCardFront')"
                          v-if="props.allowEdit && isEdit"
                        >
                          <Delete />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                  <div
                    class="idcard_action"
                    v-if="dataForm.idCardFront.length === 0 && props.allowEdit && isEdit"
                  >
                    <el-upload
                      class="idcard_action_upload"
                      :headers="{ Authorization: cache.getToken() }"
                      :action="constant.uploadUrl"
                      :before-upload="beforeUpload"
                      :on-success="
                        (res:any) => {
                          handleSuccess(res, 'idCardFront');
                        }
                      "
                      :show-file-list="false"
                    >
                      <div
                        style="display: flex; align-items: center; flex-direction: column"
                      >
                        <el-icon class="idcard_action_upload_icon"><Plus /></el-icon>
                        <div class="idcard_action_upload_desc">请上传身份证正面</div>
                      </div>
                    </el-upload>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="idCardBack" label="身份证反面">
                <div class="idcard">
                  <div v-if="dataForm.idCardBack.length > 0">
                    <div
                      class="idcard_img"
                      v-for="item in dataForm.idCardBack"
                      :key="item.url"
                    >
                      <el-image :src="item.url" class="idcard_img_main"></el-image>
                      <div class="idcard_img_action">
                        <el-icon
                          class="idcard_img_action_icon"
                          @click="onPreviewImage(item.url)"
                        >
                          <View />
                        </el-icon>
                        <el-icon
                          class="idcard_img_action_icon"
                          style="margin-left: 10px"
                          @click="onDeleteImage('idCardBack')"
                          v-if="props.allowEdit && isEdit"
                        >
                          <Delete />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                  <div
                    class="idcard_action"
                    v-if="dataForm.idCardBack.length === 0 && props.allowEdit && isEdit"
                  >
                    <el-upload
                      class="idcard_action_upload"
                      :headers="{ Authorization: cache.getToken() }"
                      :action="constant.uploadUrl"
                      :before-upload="beforeUpload"
                      :on-success="
                        (res:any) => {
                          handleSuccess(res, 'idCardBack');
                        }
                      "
                      :show-file-list="false"
                    >
                      <div
                        style="display: flex; align-items: center; flex-direction: column"
                      >
                        <el-icon class="idcard_action_upload_icon"><Plus /></el-icon>
                        <div class="idcard_action_upload_desc">请上传身份证反面</div>
                      </div>
                    </el-upload>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="diploma" label="学历证照片">
                <div class="idcard">
                  <div v-if="dataForm.diploma.length > 0">
                    <div
                      class="idcard_img"
                      v-for="item in dataForm.diploma"
                      :key="item.url"
                    >
                      <el-image :src="item.url" class="idcard_img_main"></el-image>
                      <div class="idcard_img_action">
                        <el-icon
                          class="idcard_img_action_icon"
                          @click="onPreviewImage(item.url)"
                        >
                          <View />
                        </el-icon>
                        <el-icon
                          class="idcard_img_action_icon"
                          style="margin-left: 10px"
                          @click="onDeleteImage('diploma')"
                          v-if="props.allowEdit && isEdit"
                        >
                          <Delete />
                        </el-icon>
                      </div>
                    </div>
                  </div>

                  <div
                    class="idcard_action"
                    v-if="dataForm.diploma.length === 0 && props.allowEdit && isEdit"
                  >
                    <el-upload
                      class="idcard_action_upload"
                      :headers="{ Authorization: cache.getToken() }"
                      :action="constant.uploadUrl"
                      :before-upload="beforeUpload"
                      :on-success="
                        (res:any) => {
                          handleSuccess(res, 'diploma');
                        }
                      "
                      :show-file-list="false"
                    >
                      <div
                        style="display: flex; align-items: center; flex-direction: column"
                      >
                        <el-icon class="idcard_action_upload_icon"><Plus /></el-icon>
                        <div class="idcard_action_upload_desc">请上传学历证照片</div>
                      </div>
                    </el-upload>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="degree" label="学位证照片">
                <div class="idcard">
                  <div v-if="dataForm.degree.length > 0">
                    <div
                      class="idcard_img"
                      v-for="item in dataForm.degree"
                      :key="item.url"
                    >
                      <el-image :src="item.url" class="idcard_img_main"></el-image>
                      <div class="idcard_img_action">
                        <el-icon
                          class="idcard_img_action_icon"
                          @click="onPreviewImage(item.url)"
                        >
                          <View />
                        </el-icon>
                        <el-icon
                          class="idcard_img_action_icon"
                          style="margin-left: 10px"
                          @click="onDeleteImage('degree')"
                          v-if="props.allowEdit && isEdit"
                        >
                          <Delete />
                        </el-icon>
                      </div>
                    </div>
                  </div>

                  <div
                    class="idcard_action"
                    v-if="dataForm.degree.length === 0 && props.allowEdit && isEdit"
                  >
                    <el-upload
                      class="idcard_action_upload"
                      :headers="{ Authorization: cache.getToken() }"
                      :action="constant.uploadUrl"
                      :before-upload="beforeUpload"
                      :on-success="
                        (res:any) => {
                          handleSuccess(res, 'degree');
                        }
                      "
                      :show-file-list="false"
                    >
                      <div
                        style="display: flex; align-items: center; flex-direction: column"
                      >
                        <el-icon class="idcard_action_upload_icon"><Plus /></el-icon>
                        <div class="idcard_action_upload_desc">请上传学位证照片</div>
                      </div>
                    </el-upload>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="identification" label="证件照">
                <div class="idcard">
                  <div v-if="dataForm.identification.length > 0">
                    <div
                      class="idcard_img"
                      v-for="item in dataForm.identification"
                      :key="item.url"
                    >
                      <el-image :src="item.url" class="idcard_img_main"></el-image>
                      <div class="idcard_img_action">
                        <el-icon
                          class="idcard_img_action_icon"
                          @click="onPreviewImage(item.url)"
                        >
                          <View />
                        </el-icon>
                        <el-icon
                          class="idcard_img_action_icon"
                          style="margin-left: 10px"
                          @click="onDeleteImage('identification')"
                          v-if="props.allowEdit && isEdit"
                        >
                          <Delete />
                        </el-icon>
                      </div>
                    </div>
                  </div>

                  <div
                    class="idcard_action"
                    v-if="
                      dataForm.identification.length === 0 && props.allowEdit && isEdit
                    "
                  >
                    <el-upload
                      class="idcard_action_upload"
                      :headers="{ Authorization: cache.getToken() }"
                      :action="constant.uploadUrl"
                      :before-upload="beforeUpload"
                      :on-success="
                        (res:any) => {
                          handleSuccess(res, 'identification');
                        }
                      "
                      :show-file-list="false"
                    >
                      <div
                        style="display: flex; align-items: center; flex-direction: column"
                      >
                        <el-icon class="idcard_action_upload_icon"><Plus /></el-icon>
                        <div class="idcard_action_upload_desc">请上传证件照</div>
                      </div>
                    </el-upload>
                  </div>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="leave" label="离职证明">
                <div class="idcard">
                  <div v-if="dataForm.leave.length > 0">
                    <div
                      class="idcard_img"
                      v-for="item in dataForm.leave"
                      :key="item.url"
                    >
                      <el-image :src="item.url" class="idcard_img_main"></el-image>
                      <div class="idcard_img_action">
                        <el-icon
                          class="idcard_img_action_icon"
                          @click="onPreviewImage(item.url)"
                        >
                          <View />
                        </el-icon>
                        <el-icon
                          class="idcard_img_action_icon"
                          style="margin-left: 10px"
                          @click="onDeleteImage('leave')"
                          v-if="props.allowEdit && isEdit"
                        >
                          <Delete />
                        </el-icon>
                      </div>
                    </div>
                  </div>

                  <div
                    class="idcard_action"
                    v-if="dataForm.leave.length === 0 && props.allowEdit && isEdit"
                  >
                    <el-upload
                      class="idcard_action_upload"
                      :headers="{ Authorization: cache.getToken() }"
                      :action="constant.uploadUrl"
                      :before-upload="beforeUpload"
                      :on-success="
                        (res:any) => {
                          handleSuccess(res, 'leave');
                        }
                      "
                      :show-file-list="false"
                    >
                      <div
                        style="display: flex; align-items: center; flex-direction: column"
                      >
                        <el-icon class="idcard_action_upload_icon"><Plus /></el-icon>
                        <div class="idcard_action_upload_desc">请上传离职证明</div>
                      </div>
                    </el-upload>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <el-image-viewer
          v-if="preview.show"
          @close="onClosePreviewImage"
          :url-list="preview.url"
        >
        </el-image-viewer>
      </div>
      <div class="info-action" v-if="props.allowEdit && isEdit">
        <el-button type="primary" @click="onSubmit">保存</el-button>
        <el-button @click="onCancel">取消</el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup lang="ts">
import InfoTitle from "./InfoTitle.vue";
import { onMounted, reactive, ref } from "vue";
import cache from "@/utils/cache";
import constant from "@/utils/constant";
import { ElMessage, UploadProps } from "element-plus";
import { Plus, View, Delete } from "@element-plus/icons-vue";
import { getUserFiles, saveUserFiles } from "@/api/employee";
import { useUserStore } from "@/store/modules/user";

const userStore = useUserStore();

interface IProps {
  action: "add" | "edit";
  userId: number;
  allowEdit: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  action: "add",
  userId: 0,
  allowEdit: false,
});

const isEdit = ref(false);

const dataForm = reactive({
  idCardFront: [],
  idCardBack: [],
  diploma: [],
  degree: [],
  identification: [],
  leave: [],
});

const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  let fileNameList = file.name.split(".");
  let fileType = fileNameList[fileNameList.length - 1];
  if (["jpg", "png", "jpeg"].indexOf(fileType) == -1) {
    ElMessage.error("请上传图片文件");
    return false;
  }
  if (file.size / 1024 / 1024 > 20) {
    ElMessage.error("文件大小不能超过20M");
    return false;
  }
  return true;
};

const handleSuccess = (res, type) => {
  if (res.code === 0) {
    let newAttachmentItem = {
      platform: res.data.platform,
      url: res.data.url,
      name: res.data.name,
      size: res.data.size,
      type: type,
    };
    let newAttachmentList = [];
    newAttachmentList.push(newAttachmentItem);
    dataForm[type] = newAttachmentList;
  } else {
    ElMessage.error("上传失败：" + res.msg);
  }
};

const onDeleteImage = (type: string) => {
  dataForm[type] = [];
};

onMounted(() => {
  GetUserFiles();
});

const GetUserFiles = () => {
  getUserFiles(props.userId).then((res) => {
    console.log(res);
    if (res.code === 0) {
      let index = -1;
      index = res.data.findIndex((item) => item.type === "idCardFront");
      if (index != -1) {
        dataForm.idCardFront = [].concat(res.data[index]);
      }

      index = res.data.findIndex((item) => item.type === "idCardBack");
      if (index != -1) {
        dataForm.idCardBack = [].concat(res.data[index]);
      }

      index = res.data.findIndex((item) => item.type === "diploma");
      if (index != -1) {
        dataForm.diploma = [].concat(res.data[index]);
      }

      index = res.data.findIndex((item) => item.type === "degree");
      if (index != -1) {
        dataForm.degree = [].concat(res.data[index]);
      }

      index = res.data.findIndex((item) => item.type === "identification");
      if (index != -1) {
        dataForm.identification = [].concat(res.data[index]);
      }

      index = res.data.findIndex((item) => item.type === "leave");
      if (index != -1) {
        dataForm.leave = [].concat(res.data[index]);
      }
    }
  });
};

const preview = reactive({
  show: false,
  url: [],
});

const onPreviewImage = (url: string) => {
  preview.url = [url];
  preview.show = true;
};

const onClosePreviewImage = () => {
  preview.show = false;
  preview.url = [];
};

const onSubmit = () => {
  let newAttachmentList = [];
  if (dataForm.idCardFront.length > 0) {
    newAttachmentList = newAttachmentList.concat(dataForm.idCardFront);
  }
  if (dataForm.idCardBack.length > 0) {
    newAttachmentList = newAttachmentList.concat(dataForm.idCardBack);
  }
  if (dataForm.diploma.length > 0) {
    newAttachmentList = newAttachmentList.concat(dataForm.diploma);
  }
  if (dataForm.degree.length > 0) {
    newAttachmentList = newAttachmentList.concat(dataForm.degree);
  }
  if (dataForm.identification.length > 0) {
    newAttachmentList = newAttachmentList.concat(dataForm.identification);
  }
  if (dataForm.leave.length > 0) {
    newAttachmentList = newAttachmentList.concat(dataForm.leave);
  }
  let reqData = {
    id: props.userId,
    attachmentList: newAttachmentList,
  };
  saveUserFiles(reqData).then((res) => {
    if (res.code === 0) {
      isEdit.value = false;
      ElMessage.success("保存成功");
    }
  });
};

const onCancel = () => {
  isEdit.value = false;
};

const onClickEdit = () => {
  isEdit.value = true;
};
</script>
<style lang="scss" scoped>
.info {
  &-title {
    display: flex;
    align-items: center;
    &-divider {
      flex: 1;
      height: 1px;
      background-color: #f0f0f0;
      margin: 0 10px;
    }
  }
  &-desc {
    font-size: 14px;
    color: #999;
    margin-top: 10px;
  }
  &-list {
    margin-top: 20px;
  }
  &-action {
    text-align: center;
    margin-top: 15px;
  }
}
.idcard {
  width: 180px;
  height: 120px;
  &_img {
    width: 180px;
    height: 120px;
    position: relative;
    border: 1px dashed #fff;
    &_main {
      width: 180px;
      height: 120px;
    }
    &_action {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      display: none;
      &_icon {
        color: #ffffff;
        font-size: 16px;
        cursor: pointer;
      }
    }
  }
  &_action {
    &_upload {
      width: 180px;
      height: 120px;
      border: 1px dashed #cdd0d6;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      &_icon {
        font-size: 24px;
        color: #909399;
      }
      &_desc {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}
.idcard_img:hover {
  .idcard_img_action {
    display: flex;
  }
}
</style>
