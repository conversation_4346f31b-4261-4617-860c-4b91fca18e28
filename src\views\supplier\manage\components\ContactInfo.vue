<template>
  <div class="info">
    <el-form
      ref="infoRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="140px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="contactName" label="联系人">
            <el-input
              v-model="state.dataForm.contactName"
              placeholder="联系人"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="contactPhone" label="联系电话">
            <el-input
              v-model="state.dataForm.contactPhone"
              placeholder="联系电话"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="idNumber" label="身份证">
            <el-input v-model="state.dataForm.idNumber" placeholder="身份证"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="address" label="联系地址">
            <el-input v-model="state.dataForm.address" placeholder="联系地址"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="email" label="电子邮箱">
            <el-input v-model="state.dataForm.email" placeholder="电子邮箱"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="zipCode" label="邮政编码">
            <el-input v-model="state.dataForm.zipCode" placeholder="邮政编码"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { reactive } from "vue";
const state = reactive({
  dataForm: {},
  dataRules: {},
});
</script>
<style lang="scss" scoped>
.info {
  width: 100%;
  max-width: 1000px;
  margin-top: 10px;
}
</style>
