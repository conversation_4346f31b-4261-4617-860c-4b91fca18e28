<template>
  <el-card>
    <div class="info">
      <div class="info-title">
        <div class="info-title-text">
          <InfoTitle title="基础信息"></InfoTitle>
        </div>
        <div class="info-title-divider"></div>
        <div class="info-title-action">
          <el-button
            type="text"
            @click="onClickEdit"
            v-if="props.allowEdit && !isEdit"
            v-auth="'work:userInfo:update'"
            >编辑</el-button
          >
        </div>
      </div>
      <div class="info-content">
        <el-form
          :model="dataForm"
          :rules="dataRule"
          ref="dataFormRef"
          label-width="120px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="realName" label="姓名">
                <el-input
                  v-model="dataForm.realName"
                  placeholder="姓名"
                  v-if="props.allowEdit && isEdit"
                ></el-input>
                <div v-else>
                  {{ dataForm.realName }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="jobNum" label="工号">
                <el-input
                  v-model="dataForm.jobNum"
                  placeholder="工号"
                  v-if="props.allowEdit && isEdit"
                ></el-input>
                <div v-else>
                  {{ dataForm.jobNum }}
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="companyDept" label="所属公司">
                <!-- <el-tree-select
                  v-model="dataForm.companyDept"
                  :data="orgList"
                  value-key="id"
                  check-strictly
                  :render-after-expand="false"
                  :props="{ label: 'name', children: 'children' }"
                  style="width: 100%"
                  @node-click="onSelectCompany"
                  disabled
                /> -->
                {{ dataForm.companyName }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="orgId" label="部门">
                <!-- <el-tree-select
                  v-model="dataForm.orgId"
                  :data="orgList"
                  value-key="id"
                  check-strictly
                  :render-after-expand="false"
                  :props="{ label: 'name', children: 'children' }"
                  style="width: 100%"
                  disabled
                /> -->
                {{ dataForm.orgName }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="position" label="岗位">
                <!-- <el-select
                  v-model="dataForm.position"
                  placeholder="职位"
                  style="width: 100%"
                  disabled
                >
                  <el-option
                    v-for="post in postList"
                    :key="post.id"
                    :label="post.postName"
                    :value="post.id"
                  ></el-option>
                </el-select> -->
                {{ (dataForm.postName || []).join("、") }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="position" label="职务">
                <!-- <el-select
                  v-model="dataForm.userPost"
                  placeholder="职务"
                  style="width: 100%"
                  disabled
                >
                  <el-option
                    v-for="role in roleList"
                    :key="role.id"
                    :label="role.name"
                    :value="role.id"
                  ></el-option>
                </el-select> -->
                <el-input
                  v-model="dataForm.position"
                  placeholder="职务"
                  v-if="props.allowEdit && isEdit"
                ></el-input>
                <span v-else>{{ dataForm.position }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item prop="userStatus" label="员工状态">
                <fast-select
                  v-model="dataForm.userStatus"
                  dict-type="user_info_status"
                  placeholder="员工状态"
                  disabled
                  v-if="props.allowEdit && isEdit"
                ></fast-select>
                <span
                  v-html="getDictLabelList('user_info_status', dataForm.userStatus)"
                  v-else
                ></span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="isSign" label="是否签订合同">
                <fast-radio-group
                  v-model="dataForm.isSign"
                  dict-type="is_sign"
                  v-if="props.allowEdit && isEdit"
                ></fast-radio-group>
                <span v-html="getDictLabelList('is_sign', dataForm.isSign)" v-else></span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="probationPeriod" label="试用期">
                <div
                  style="display: flex; align-items: center; width: 100%"
                  v-if="props.allowEdit && isEdit"
                >
                  <el-input-number
                    v-model="dataForm.probationPeriod"
                    placeholder="试用期"
                    :min="0"
                    :max="99"
                    style="width: 100%"
                    @blur="onChangeEntryDate"
                  ></el-input-number>
                  <div style="flex-shrink: 0; margin-left: 10px">个月</div>
                </div>
                <div v-else>{{ dataForm.probationPeriod }}个月</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="entryDate" label="入职日期">
                <el-date-picker
                  v-model="dataForm.entryDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="入职日期"
                  style="width: 100%"
                  v-if="props.allowEdit && isEdit"
                  @change="onChangeEntryDate"
                />
                <div v-else>{{ dataForm.entryDate }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="formalStart" label="转正日期">
                <el-date-picker
                  v-model="dataForm.formalStart"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="转正日期"
                  style="width: 100%"
                  disabled
                  v-if="props.allowEdit && isEdit"
                />
                <div v-else>{{ dataForm.formalStart }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="workingYears" label="司龄">
                <el-input
                  v-model="dataForm.workingYears"
                  placeholder="司龄"
                  disabled
                  v-if="props.allowEdit && isEdit"
                ></el-input>
                <div v-else>{{ dataForm.workingYears }}年</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="workingDate" label="参加工作时间">
                <el-date-picker
                  v-model="dataForm.workingDate"
                  type="date"
                  value-format="YYYY-MM-DD"
                  placeholder="参加工作时间"
                  style="width: 100%"
                  v-if="props.allowEdit && isEdit"
                />
                <div v-else>{{ dataForm.workingDate }}</div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="workLength" label="工龄">
                <el-input
                  v-model="dataForm.workLength"
                  placeholder="工龄"
                  disabled
                  v-if="props.allowEdit && isEdit"
                ></el-input>
                <div v-else>{{ dataForm.workLength }}年</div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="info-action" v-if="props.allowEdit && isEdit">
        <el-button type="primary" @click="onSubmit">保存</el-button>
        <el-button @click="onCancel">取消</el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import dayjs from "dayjs";
import { ref, onMounted, watch } from "vue";
import InfoTitle from "./InfoTitle.vue";
import {
  getUserInfo,
  useUserSubmitApi,
  useUserInfoSubmitApi,
} from "@/api/employee/index";
import { useUserStore } from "@/store/modules/user";
import { getDictLabelList } from "@/utils/tool";
import { ElMessage } from "element-plus";
import { useRoute } from "vue-router";

const route = useRoute();

console.log(route);

const props = defineProps({
  userId: {
    type: Number,
    default: 0,
  },
  allowEdit: {
    type: Boolean,
    default: false,
  },
});

const dataFormRef = ref();

const userStore = useUserStore();

const isEdit = ref(false);

const dataForm = ref({
  id: "",
  realName: "",
  jobNum: "",
  companyDept: "",
  companyName: "",
  orgName: "",
  position: "",
  postName: "",
  // orgId: "",
  // userPost: "",
  // position: "",
  userStatus: "",
  isSign: 0,
  entryDate: "",
  formalStart: "",
  workingYears: "",
  workingDate: "",
  workLength: "",
  probationPeriod: 3,
});

const postList = ref([]);
const roleList = ref([]);
const orgList = ref([]);

// 获取岗位列表
// const getPostList = () => {
//   return usePostListApi().then((res) => {
//     postList.value = res.data;
//   });
// };

// // 获取角色列表
// const getRoleList = () => {
//   return useRoleListApi().then((res) => {
//     roleList.value = res.data;
//   });
// };

// // 获取机构列表
// const getOrgList = () => {
//   return useOrgListApi().then((res) => {
//     orgList.value = res.data;
//   });
// };

const GetUserInfo = () => {
  getUserInfo(props.userId).then((res) => {
    console.log(res);
    if (res.code === 0) {
      dataForm.value.id = res.data.id;
      dataForm.value.realName = res.data.realName;
      dataForm.value.jobNum = res.data.jobNum;
      dataForm.value.companyDept = res.data.companyDept;
      dataForm.value.companyName = res.data.companyName;
      dataForm.value.orgName = res.data.orgName;
      dataForm.value.position = res.data.position;
      dataForm.value.postName = res.data.postName;
      // dataForm.value.orgId = res.data.orgId;
      // dataForm.value.userPost = res.data.userPost;
      // dataForm.value.position = res.data.position;
      dataForm.value.userStatus = res.data.userStatus;
      dataForm.value.isSign = res.data.isSign;
      dataForm.value.entryDate = res.data.entryDate;
      dataForm.value.formalStart = res.data.formalStart;
      dataForm.value.workingYears = res.data.workingYears;
      dataForm.value.workingDate = res.data.workingDate;
      dataForm.value.workLength = res.data.workLength;
      dataForm.value.probationPeriod = res.data.probationPeriod ?? 3;
    }
  });
};

watch(
  () => props.userId,
  () => {
    if (props.userId) {
      GetUserInfo();
    }
  },
  { immediate: true }
);

// onMounted(() => {
//   // getPostList();
//   // getRoleList();
//   // getOrgList();
//   GetUserInfo();
// });

const dataRule = ref({
  realName: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
  jobNum: [{ required: true, message: "工号不能为空", trigger: "blur" }],
});

const onCancel = () => {
  isEdit.value = false;
};

const onSubmit = () => {
  dataFormRef.value.validate((valid) => {
    if (valid) {
      console.log(dataForm.value);
      let reqData = {
        id: dataForm.value.id,
        userId: props.userId,
        realName: dataForm.value.realName,
        position: dataForm.value.position,
        jobNum: dataForm.value.jobNum,
        isSign: dataForm.value.isSign,
        entryDate: dataForm.value.entryDate,
        formalStart: dataForm.value.formalStart,
        workingDate:
          dataForm.value.workingDate ||
          dayjs(dataForm.value.workingDate).format("YYYY-MM-DD"),
        probationPeriod: dataForm.value.probationPeriod,
      };
      useUserSubmitApi(reqData).then(() => {
        isEdit.value = false;
        ElMessage.success("保存成功");
        GetUserInfo();
      });
    }
  });
};

const onClickEdit = () => {
  isEdit.value = true;
};

const onChangeEntryDate = () => {
  dataForm.value.formalStart = dayjs(dataForm.value.entryDate)
    .add(dataForm.value.probationPeriod || 0, "month")
    .format("YYYY-MM-DD");
};
</script>
<style lang="scss" scoped>
.info {
  &-title {
    display: flex;
    align-items: center;
    &-divider {
      flex: 1;
      height: 1px;
      background-color: #f0f0f0;
      margin: 0 10px;
    }
  }
  &-content {
    margin-top: 20px;
  }
  &-action {
    display: flex;
    justify-content: center;
    margin-top: 40px;
    margin-bottom: 40px;
  }
}
</style>
