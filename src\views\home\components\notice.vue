<template>
  <div class="notice">
    <div class="notice-title">
      <HomeTitle icon="icon-y_yb_tongzhigonggao" title="通知公告">
        <template #right>
          <el-link type="primary" :underline="false" @click="moreSpClick()">更多</el-link>
        </template>
      </HomeTitle>
    </div>
    <div class="notice-content">
      <ul v-if="ggList.length > 0">
        <li
          class="notice-content-item"
          v-for="(item, index) in ggList"
          :key="index"
          @click="onNoticeClick(item)"
        >
          <div class="title">
            {{ item.title }} <span class="tag" v-if="item.isTop === 1">置顶</span>
          </div>
          <div class="time">{{ item.createTime }}</div>
        </li>
      </ul>
      <div style="margin: 20px auto; width: 100px" v-else>
        <svg-icon icon="icon-zanwuxiangguansousuoneirong" size="100px"></svg-icon>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useRouter } from "vue-router";
import HomeTitle from "./HomeTitle.vue";
import { nextTick, reactive, ref, onMounted } from "vue";
import { getWorkInfoPage } from "@/api/pnotice";

const router = useRouter();
const ggList = ref([]);
onMounted(() => {
  getWorkPage();
});
const getWorkPage = () => {
  let params = {
    order: "",
    asc: false,
    pageNo: 1,
    pageSize: 7,
    title: "",
    auditStatus: "",
  };
  getWorkInfoPage(params).then((res) => {
    ggList.value = res.data.list;
  });
};
// 审批点击更多
const moreSpClick = () => {
  router.push("/moreUse/index");
};

const onNoticeClick = (item) => {
  router.push({
    path: "/moreUse/action",
    query: { id: item.id, type: "look" },
  });
};
</script>
<style lang="scss" scoped>
.notice {
  background-color: #ffffff;
  padding: 16px 10px;
  border-radius: 4px;
  min-height: 283px;
  &-content {
    margin-top: 10px;
    padding: 10px;
    &-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      margin-bottom: 4px;
      line-height: 28px;
      .title {
        margin: 0 10px;
        font-size: 14px;
        color: #333333;
        flex: 1;
        .tag {
          color: white;
          background-color: #ff5353;
          padding: 2px 5px;
          border-radius: 3px;
        }
      }
      .time {
        font-size: 12px;
        color: #999;
      }
      &::before {
        content: "·";
        color: #1a90fe;
        font-size: 30px;
      }
    }
  }
}
</style>
