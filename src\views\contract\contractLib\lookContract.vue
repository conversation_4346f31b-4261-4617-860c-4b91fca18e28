<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-01 10:19:02
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-06-03 15:54:23
-->
<template>
  <el-card>
    <div class="action">
      <div class="hc-template">
        <div class="ylBox" v-if="route.query.type !== '0'">
          <iframe
            id="iframeRef"
            ref="iframeRef"
            class="ylFrame"
            :src="info.url"
            frameborder="0"
          ></iframe>
          <div class="ylRight">
            <el-tabs v-model="activeName">
              <el-tab-pane label="合同基本信息" name="first">
                <div class="htJbInfo">
                  <div class="htJbTitle">合同编号：</div>
                  <div>{{ dataList.contractNo }}</div>
                </div>
                <div class="htJbInfo">
                  <div class="htJbTitle">合同名称：</div>
                  <div>{{ dataList.contractName }}</div>
                </div>
                <div class="htJbInfo">
                  <div class="htJbTitle">采购计划编号：</div>
                  <div>{{ dataList.packageNo }}</div>
                </div>
                <div class="htJbInfo">
                  <div class="htJbTitle">采购计划名称：</div>
                  <div>{{ dataList.packageName }}</div>
                </div>
                <div class="htJbInfo">
                  <div class="htJbTitle">合同总金额（元）：</div>
                  <div>{{ dataList.contractPrice }}</div>
                </div>
                <div class="htJbInfo">
                  <div class="htJbTitle">合同开始时间：</div>
                  <div>{{ dataList.startTime }}</div>
                </div>
                <div class="htJbInfo">
                  <div class="htJbTitle">合同结束时间：</div>
                  <div>{{ dataList.endTime }}</div>
                </div>
                <div class="htJbInfo">
                  <div class="htJbTitle">我方主体名称：</div>
                  <div>{{ dataList.mySubjectName }}</div>
                </div>
                <div class="htJbInfo">
                  <div class="htJbTitle">我方负责人：</div>
                  <div>{{ dataList.myResponsibilityName }}</div>
                </div>
                <div class="htJbInfo">
                  <div class="htJbTitle">对方主体名称：</div>
                  <div>{{ dataList.youSubjectName }}</div>
                </div>
                <div class="htJbInfo">
                  <div class="htJbTitle">对方负责人：</div>
                  <div>{{ dataList.youResponsibilityName }}</div>
                </div>
                <div class="htJbInfo">
                  <div class="htJbTitle">备注：</div>
                  <div>{{ dataList.flowRemark }}</div>
                </div>
              </el-tab-pane>
              <el-tab-pane label="签章状态" name="second">
                <div style="margin-left: 15px; overflow-y: auto; height: 750px">
                  <el-timeline>
                    <el-timeline-item
                      v-for="(item, index) in dataList.oaContractSignRecordList"
                      :key="index"
                      :icon="item.icon"
                      :type="item.type"
                      :color="item.color"
                      :size="item.size"
                    >
                      <div
                        :style="
                          item.signStatus == 2 || item.signStatus == 3
                            ? 'color:#F56C6C;'
                            : item.signStatus == 4
                            ? 'color:#67C23A;'
                            : 'color:#409eff;'
                        "
                      >
                        <span v-if="item.signStatus == 1"> 已签章 </span>
                        <span v-else>
                          {{
                            dictList.filter((a) => a.dictValue == item.signStatus)[0]
                              .dictLabel
                          }}
                        </span>
                      </div>
                      <div class="conBox" v-if="item.signStatus != 0">
                        <div>签章人：{{ item.realName }}</div>
                        <div>签章时间：{{ item.signTime }}</div>
                        <div v-if="item.signStatus == 2">
                          退回理由：{{ item.rejectReason }}
                        </div>
                        <div v-if="item.signStatus == 3">
                          作废原因：{{ item.rejectReason }}
                        </div>
                      </div>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </div>
        <iframe
          v-else
          id="iframeRef"
          ref="iframeRef"
          class="frame"
          src="./hc/signer.html"
          frameborder="0"
        ></iframe>
      </div>
      <div class="bottom_btn">
        <el-button
          v-if="route.query.type == '0'"
          type="primary"
          v-auth="'contractLib:lookContract:submit'"
          @click="submit()"
          >提交</el-button
        >
        <el-button
          v-if="route.query.type != '0'"
          type="primary"
          v-auth="'contractLib:lookContract:download'"
          @click="downloadFile(info.url, dataList.attachmentList[0].name)"
          >下载合同</el-button
        >
        <el-button
          v-if="route.query.type == '2' && signStatus == 2"
          @click="zfClick"
          v-auth="'contractLib:lookContract:zf'"
          >作废</el-button
        >
        <el-button
          v-if="route.query.type == '4' && signStatus == 1"
          @click="htBackClick"
          v-auth="'contractLib:lookContract:htBack'"
          >合同退回</el-button
        >
        <el-button @click="closentab">取消</el-button>
      </div>
    </div>
  </el-card>
  <el-dialog
    title="操作"
    v-model="zfDialogVisible"
    width="30%"
    :before-close="
      () => {
        zfDialogVisible = false;
      }
    "
  >
    <div>审批已通过，合同作废后可需重新发起审批</div>
    <div class="zfBox" style="display: flex; align-items: flex-start">
      <span><span style="color: red">*</span>作废理由：</span>
      <el-input
        v-model="zfValue"
        placeholder="请输入作废理由"
        style="width: 60%"
        :rows="3"
        type="textarea"
      ></el-input>
    </div>
    <div slot="footer" class="bottom_btn">
      <el-button @click="zfDialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="zfQuery">确 定</el-button>
    </div>
  </el-dialog>
  <el-dialog
    title="合同退回"
    v-model="htBackDialogVisible"
    width="30%"
    :before-close="
      () => {
        htBackDialogVisible = false;
      }
    "
  >
    <div class="zfBox">
      <span><span style="color: red">*</span>退回理由：</span>
      <el-input
        v-model="htBacValue"
        placeholder="请输入内容"
        style="width: 60%"
      ></el-input>
    </div>
    <div slot="footer" class="bottom_btn">
      <el-button @click="htBackDialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="htBackQuery">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
import { closeTab } from "@/utils/tabs";
import { useRoute, useRouter } from "vue-router";
import { provide, reactive, ref, onMounted } from "vue";
import {
  getContractDetailInfo,
  contractSignRecordPage,
  contractSignCancel,
  contractSignReject,
  contractSignConfirmation,
} from "@/api/pnotice";
import { downloadFile } from "@/utils/tool";
import { getDictDataList } from "@/utils/tool";
import { useAppStore } from "@/store/modules/app";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
const route = useRoute();
const appStore = useAppStore();
const dictList = ref(getDictDataList(appStore.dictList, "sign_status"));
const router = useRouter();
const info = reactive({ id: "", url: "" });
const iframeRef = ref();
const signCode = ref("");
const signCompleteSrc = ref("");
const activeName = ref("first");
const dataList = ref({});
const zfDialogVisible = ref(false);
const zfValue = ref("");
const htBackDialogVisible = ref(false);
const isTh = ref(false);
const htBacValue = ref("");
const signStatus = ref(0);
onMounted(() => {
  getPdf(route.query.id);
});

const getPdf = async (id: any) => {
  info.id = id;
  getContractDetailInfo(id).then((res) => {
    if (res.data?.attachmentList?.length > 0) {
      info.url = res.data.attachmentList[0].url;
    }
    signStatus.value = res.data.signStatus;
    let newData = res.data;
    newData.oaContractSignRecordList = (res.data.oaContractSignRecordList ?? []).map(
      (item) => {
        return {
          ...item,
          signTime: dayjs(item.createTime).format("YYYY-MM-DD HH:mm:ss"),
        };
      }
    );
    dataList.value = newData;
  });
  let viewer = document.getElementById("iframeRef");
  let send = document.getElementById("iframeRef").contentWindow;
  viewer.onload = () => {
    send.postMessage(
      { url: info.url, type: "pp", api: import.meta.env.VITE_API_URL },
      "*"
    );
  };
};
window.addEventListener(
  "message",
  function (res) {
    if (res.data.code) {
      signCode.value = res.data.code;
    }
    if (res.data.signCompleteSrc) {
      signCompleteSrc.value = res.data.signCompleteSrc;
    }
  },
  false
);
// 点击作废
const zfClick = () => {
  zfDialogVisible.value = true;
};
// 确认作废
const zfQuery = () => {
  if (zfValue.value === "") {
    return ElMessage.error("请填写作废原因！");
  }
  let obj = {
    id: info.id,
    rejectReason: zfValue.value,
  };
  contractSignCancel(obj).then((res) => {
    if (res.code !== 0) {
      return ElMessage.error(res.msg);
    }
    ElMessage.success("操作成功！");
    zfDialogVisible.value = false;
    closentab();
  });
};
// 点击合同退回
const htBackClick = () => {
  htBackDialogVisible.value = true;
};
// 确认合同退回
const htBackQuery = () => {
  if (htBacValue.value === "") {
    return ElMessage.error("请填写退回原因！");
  }
  let obj = {
    id: info.id,
    rejectReason: htBacValue.value,
  };
  contractSignReject(obj).then((res) => {
    if (res.code !== 0) {
      return ElMessage.error(res.msg);
    }
    ElMessage.success("操作成功！");
    htBackDialogVisible.value = false;
    closentab();
  });
};
// 提交
const submit = () => {
  if (signCompleteSrc.value === "") {
    return ElMessage.error("请完成签署！");
  }
  let obj = {
    fileName: signCompleteSrc.value,
    signStatus: dataList.value.signStatus == 1 ? 4 : 1,
    id: info.id,
  };
  contractSignConfirmation(obj).then((res) => {
    if (res.code !== 0) {
      return ElMessage.error(res.msg);
    }
    ElMessage.success("操作成功！");
    closentab();
  });
};
// 返回
const closentab = () => {
  closeTab(router, route);
};
</script>
<style lang="scss" scoped>
.action {
  .info {
    width: 100%;
    margin-top: 10px;
  }
  .action_title {
    margin-bottom: 10px;
  }
}
.hc-template {
  width: 100%;
  height: 740px;
  .frame {
    width: 100%;
    height: 100%;
  }
  .ylBox {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .ylFrame {
    width: 70%;
    height: 100%;
  }
  .ylRight {
    width: 30%;
    height: 100%;
    background-color: white;
    padding-left: 10px;

    .conBox {
      background-color: rgba(245, 247, 250, 1);
      border-radius: 4px;
      padding: 6px 10px;
      line-height: 22px;
      margin-top: 8px;
      color: #aaaaaa;
    }
  }
  .htJbInfo {
    margin: 10px;
    font-size: 16px;
    .htJbTitle {
      color: #999999;
      line-height: 25px;
    }
  }
}
.bottom_btn {
  margin: 15px auto 0;
  text-align: center;
}
.zfBox {
  margin: 30px 10px;
}
</style>
