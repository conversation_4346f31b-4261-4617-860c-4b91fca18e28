<template>
  <div class="quick">
    <div class="quick-title">
      <HomeTitle icon="icon-kuaijierukou" title="快速入口"> </HomeTitle>
    </div>
    <div class="quick-content">
      <div
        class="quick-content-item"
        v-for="item in functionList"
        :key="item.key"
        @click="onQuickEnter(item.url)"
      >
        <div class="quick-content-item-icon">
          <svg-icon :icon="item.icon" class-name="svg-size" />
        </div>
        <div class="quick-content-item-label">{{ item.label }}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useRouter } from "vue-router";
import HomeTitle from "./HomeTitle.vue";

const router = useRouter();

const functionList = [
  {
    key: "base",
    label: "基本信息",
    icon: "icon-jibenxinxi",
    url: "/member/info/index",
  },
  {
    key: "plan",
    label: "投标保证金",
    icon: "icon-toubiaobaozhengjin",
    url: "/sppurchase/bidbond/index",
  },
  {
    key: "notice",
    label: "已参与项目",
    icon: "icon-yicanyuxiangmu",
    url: "/sppurchase/makeoffer/index",
  },
  // {
  //   key: "invitation",
  //   label: "中标服务费",
  //   icon: "icon-zhongbiaofuwufei",
  //   url: "/sppurchase/bidcharge/index",
  // },
  // {
  //   key: "project",
  //   label: "采购合同",
  //   icon: "icon-caigouhetong",
  //   url: "",
  // },
  // {
  //   key: "project",
  //   label: "发票管理",
  //   icon: "icon-fapiaoguanli",
  //   url: "/sppurchase/invoice/index",
  // },
];

const onQuickEnter = (url: string) => {
  router.push(url);
};
</script>
<style lang="scss" scoped>
.quick {
  background-color: #ffffff;
  padding: 16px 10px;
  border-radius: 4px;
  &-content {
    display: flex;
    align-items: center;
    margin-top: 16px;
    &-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 136px;
      height: 106px;
      cursor: pointer;
      &-icon {
        :deep(.svg-size) {
          font-size: 44px;
        }
      }
      &-label {
        margin-top: 8px;
        font-size: 14px;
        color: #333333;
      }
    }
  }
}
</style>
