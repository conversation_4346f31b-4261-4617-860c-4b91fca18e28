<html>
	<head>
		<meta
			charset="utf-8"
			name="viewport"
			content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no"
		/>
		<script src="./js/vue.min.js"></script>
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/element-ui/lib/theme-chalk/index.css" />
		<script src="https://cdn.jsdelivr.net/npm/element-ui/lib/index.js"></script>
		<link rel="stylesheet" href="./css/testCommon.css" />
		<script src="./js/testCommon.js"></script>
		<link rel="stylesheet" href="./hc-1.3.1/HebcaHC.css" />
		<script src="./hc-1.3.1/HebcaHC.umd.js"></script>
	</head>
	<body>
		<div id="app">
			<template>
				<el-container class="demo-signer-root">
					<!-- <el-header class="demo-signer-header">
        跳转页面发送命令示例：
        <el-input-number
          v-model="dTargetPage"
          style="margin-left: 15px; width: 50px"
          size="mini"
          :controls="false"
          @change="cmd('pageGo', { pageIndex: parseInt(dTargetPage) - 1 })"
          :min="1">
        </el-input-number>
      </el-header> -->
					<div class="demo-signer-main">
						<div class="demo-signer-board demo-page-wrapper">
							<h-c-template
								v-if="dVisible"
								ref="refTemplate"
								:type="dType"
								:api-dto.sync="dApiDto"
								:config-dto.sync="dConfigDto"
								:tool-dto="dToolDto"
								:file-dto-list="dFileDtoList"
								:toast="dToast"
								:verify="dVerify"
								@error="handleError"
								@index="handleIndex"
								@cmd="handleCmd"
								@route="handleRoute"
							></h-c-template>
						</div>
					</div>
				</el-container>
			</template>
		</div>

		<script>
			// window.onload = function () {}
			var app = new Vue({
				el: '#app',
				components: {
					HCTemplate: HebcaHC.HCTemplate
				},
				data: function () {
					return {
						/**
						 * 是否加载组件
						 * */
						dVisible: false,
						/**
						 * 组件使用方式
						 * */
						dType: '',
						/**
						 * 连接配置
						 * */
						dApiDto: null,
						/**
						 * 基础配置
						 * */
						dConfigDto: null,
						/**
						 * 工具栏配置
						 * */
						dToolDto: null,
						/**
						 * 文档
						 * */
						dFileDtoList: [],
						/**
						 * 是否显示缩略图
						 * */
						dThumb: false,
						/**
						 * 是否内部提示
						 * */
						dToast: false,
						/**
						 * 是否验章
						 * */
						dVerify: false,
						/**
						 * 要跳转的页面索引
						 * */
						dTargetPage: '0',
						/**
						 * 页面总页数
						 * */
						dTotalPageNum: 0,
            fileApi :'',
            signCompleteSrc:'' // 签章后的文件路径
					}
				},
				watch: {},
				computed: {},
				created: function () {},
				mounted: function () {
					var _this = this
					window.addEventListener(
						'message',
						function (e) {
							_this.dFileDtoList = []
							_this.dFileDtoList.push({
								sysId: e.data.type,
								pdfRoot: e.data.url
							})
              _this.fileApi = e.data.api
							_this.init()
						},
						false
					)
				},
				methods: {
					/**
					 * 错误
					 *
					 * message 错误信息
					 * type 错误类型 0 成功提示 1 日志 2 警告提示 3 异常错误提示
					 * */
					handleError: function (message, type) {
						if (type > 1) {
							console.log('error ' + type, message)
						}
					},
					/**
					 * 当前页变化
					 * */
					handleIndex: function (index) {
						this.dTargetPage = (index + 1).toString()
						console.log('index ', index)
					},
					/**
					 * 页面回调
					 *
					 * code 命令
					 * d 数据
					 * */
					handleRoute: function (code, d) {
						switch (code) {
							// 返回
							case 'revert':
								break
							// 签署完毕
							case 'sign':
                this.signCompleteSrc = d.fileDtoList[0].pdfSrc
                window.parent.postMessage({ signCompleteSrc:this.signCompleteSrc }, '*')
								break
							// 上传完毕
							case 'uploadDoc':
								break
						}
						console.log('route ' + code, d)
					},
					/**
					 * 自定义命令回调
					 *
					 * @param code 命令
					 * @param d 数据
					 * */
					handleCmd: function (code, d) {
						switch (code) {
							// 页面数
							case 'pageCount':
								this.dTotalPageNum = d.pageCount
								break
                // 开始签章
							case 'signStart':
                window.parent.postMessage({ code:'signStart' }, '*')
								break
                // 取消签章
							case 'signCancel':
                window.parent.postMessage({ code:'signCancel' }, '*')
								break
              case 'sign':
                window.parent.postMessage({ code:'sign' }, '*')
								break
						}
						console.log('cmd ',code , JSON.stringify(d || {}))
					},
          error:function(e){
            console.log(e,'eeeeeeee');
          },
					/**
					 * 初始化数据
					 * */
					init: function () {
						this.dType = 'uc'
						switch (this.dType) {
							case 'uc':
								this.dApiDto = {}
								this.dApiDto.type = 'go-go-go'
								this.dApiDto.mode = 'zxq1' // 证信签1.0
								this.dApiDto.context = 'default-stand-alone'
								this.dApiDto.fileHeader = null // 文件下载地址请求头 没有则不传
								this.dConfigDto = {}
								this.dConfigDto.debug = false
								this.dConfigDto.dragLimit = 10
								this.dConfigDto.union = 'v2' // 20230728 新参数 极大提高页面加载速度
								this.dConfigDto.page = false // 20230728 新参数 极大提高页面加载速度
								this.dConfigDto.signLimit = 9999
								this.dConfigDto.signMultiPage = false
								this.dConfigDto.onSignFile = $testCommon.onSignFile // 回传文件时的上传参数定义
								//this.dConfigDto.viewerCore = 'img/img';
								this.dConfigDto.showSignature = false // 开启手签
								this.dConfigDto.needKey = true
								this.dConfigDto.selectKey = true
								this.dConfigDto.srcCache = true // 是否按文件下载路径缓存文件 默认false
								this.dConfigDto.fileApi = this.fileApi + '/sys/file' // 需修改
								this.dConfigDto.fileDownload = '/download?fileName=' // 需修改
								this.dConfigDto.fileUpload = '/uploadSignature' // 需修改
								this.dToast = true
								this.dVerify = false
								break
							case 'vc':
								this.dApiDto = {}
								this.dApiDto.type = 'go-go-go'
								this.dApiDto.context = 'default-stand-alone'
								this.dApiDto.fileHeader = null // 文件下载地址请求头 没有则不传
								this.dConfigDto = {}
								this.dConfigDto.debug = false
								this.dConfigDto.union = 'v2' // 20230728 新参数 极大提高页面加载速度
								this.dConfigDto.page = true // 20230728 新参数 极大提高页面加载速度
								//this.dConfigDto.viewerCore = 'img/img';
								this.dConfigDto.srcCache = true // 是否按文件下载路径缓存文件 默认false
								this.dConfigDto.fileApi = this.fileApi + '/sys/file' // 需修改
								this.dConfigDto.fileDownload = '/download?fileName=' // 需修改
								this.dToast = true
								this.dVerify = false
								break
						}
						// this.dFileDtoList = [
						// 	{
						// 		// 需修改
						// 		sysId: '***业务系统自定义文件id***',
						// 		pdfName: 'ly.pdf',
						// 		pdfSrc: 'ly.pdf'
						// 	}
						// ]
						this.dToolDto = {
							/**
							 * 左侧自定义按钮
							 * */
							startCodeDtoList: [
								//{
								/**
								 * 返回
								 * */
								//code: 'revert'
								//}
								/*{
                                    code: 'printPage'
                                },
                                {
                                    code: 'print'
                                }*/
							],
							/**
							 * 中部按钮
							 * */
							middleCodeDtoList: [
								{
									/**
									 * 首页
									 * */
									code: 'pageFirst'
								},
								{
									/**
									 * 上一页
									 * */
									code: 'pageBack'
								},
								{
									/**
									 * 指定页
									 * */
									code: 'pageGo'
								},
								{
									/**
									 * 下一页
									 * */
									code: 'pageNext'
								},
								{
									/**
									 * 尾页
									 * */
									code: 'pageEnd'
								},
								{
									/**
									 * 页面模式
									 * */
									code: 'zoomGo'
								}
							],
							/**
							 * 尾部按钮
							 * */
							endCodeDtoList: [
								{
									/**
									 * 验章 注意: 不需验章时，应通过configDto.verify=false彻底移除验章功能 此处删除此项仅会移除验章按钮
									 * */
									code: 'verify'
								}
							]
						}
						this.dVisible = true
					},
					/**
					 * 发送指令
					 *
					 * @param code 指令名称
					 * @param d 具体参数
					 * */
					cmd: function (code, d) {
						this.$refs.refTemplate.cmd(code, d)
					}
				}
			})
		</script>
	</body>
</html>
