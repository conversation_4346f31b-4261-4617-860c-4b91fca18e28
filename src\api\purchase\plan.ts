import service from '@/utils/request'

//获取采购计划详情
export const getProjectPlanInfo = (id: number) => {
  return service.get(`/purchase/package/${id}`)
}

interface IMaterialItem {
  materialId: number;
  materialNo: string;
  materialName: string;
  materialSpec: string;
  materialType: string;
  materialUnit: string;
  materialQuantity?: number;
  comment: string;
}

export interface IAttachmentItem {
  name: string;
  url: string;
  platform: string;
  size: number;
}

export interface IActionProjectPlan {
  id?: number,
  projectId?: number
  projectName: string
  packageName: string
  packageNo: string
  packageBudget?: number
  packageType: string
  packageMode: string
  managerId?: number
  managerName: string
  comment: string
  auditStatus: string
  orgId?: number
  deliveryAddress?: string
  orgName?: string
  reviewedById?: number
  reviewedBy: string
  attachmentList: IAttachmentItem[]
  materialVOS: IMaterialItem[]
  oaFlowPersonList: []
}

export const saveProjectPlan = (reqData: IActionProjectPlan) => {
  return service.post('/purchase/package/oaSave', reqData)
}

export const updateProjectPlan = (reqData: IActionProjectPlan) => {
  return service.put('/purchase/package', reqData)
}

//采购计划审核
export interface IAuditProjectPlan {
  bizMark?: number
  bizType: string
  auditResult: string
  opinion: string
  nextReviewedById?: number
  nextReviewedBy: string
}
export const auditProjectPlan = (reqData: IAuditProjectPlan) => {
  return service.post('/purchase/package/audit', reqData)
}

//获取审核记录数据
export interface IGetAuditRecords {
  bizId?: number
  bizType: string
}

export const getAuditRecords = (reqData: IGetAuditRecords) => {
  return service.post('/purchase/record/auditRecords', reqData)
}

//报价信息相关

//获取报价清单列表
export const getQuotationInfo = (id: number) => {
  return service.get(`/purchase/package/quotationInfo/${id}`)
}

//发起多轮报价
export interface IStartQuotation {
  packageId?: number
  roundNum?: number
  startTime: string
  endTime: string
  showLastQuotation: string
  quotationDescription: string
  bidderIdList: any[]
}
export const startQuotation = (reqData: IStartQuotation) => {
  return service.post('/purchase/round', reqData)
}

//查询比价清单
export interface IGetQuotationCompareList {
  packageId?: number
  roundNo?: number
}
export const getQuotationCompareList = (reqData: IGetQuotationCompareList) => {
  return service.get('/purchase/packageMaterial/pageCompare', {
    params: reqData
  })
}

//自动生成采购计划编号
export const getPackageNo = (projectId: number) => {
  return service.get(`/purchase/package/getPackageNo/${projectId}`)
}

//获取计划跟踪完成情况
export const getTrackList = (packageId: number) => {
  return service.get(`purchase/tracking/list/${packageId}`)
}

//获取公告
export const getPurchaseAnnouncement = (packageId: number) => {
  return service.get(`purchase/tracking/getBulletin4Track/${packageId}`)
}

//获取确认中标人
export const getWinBidder = (packageId: number) => {
  return service.get(`purchase/winBidder/getByPackageId/${packageId}`)
}

//获取中标公告
export const getWinBulletin = (packageId: number) => {
  return service.get(`purchase/winBulletin/getByPackageId/${packageId}`)
}

//获取中标通知书
export const getWinResultNotice = (packageId: number) => {
  return service.get(`purchase/winResultNotice/getByPackageId/${packageId}`)
}


//获取采购合同
export const getContract = (packageId: number) => {
  return service.get(`purchase/contract/getByPackageId/${packageId}`)
}