<template>
  <div class="statistics">
    <div class="statistics-title">
      <el-select v-model="state.year" class="right" @change="onChangeYear">
        <el-option
          v-for="item in state.yearList"
          :key="item"
          :label="item"
          :value="item"
        ></el-option>
      </el-select>
    </div>
    <div class="statistics-content">
      <div class="statistics-content-chart" id="chart"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { getPurchaseProjectStatistics } from "@/api/home";
import dayjs from "dayjs";
import { BarChart, BarSeriesOption, LineChart, LineSeriesOption } from "echarts/charts";
import {
  GridComponent,
  GridComponentOption,
  LegendComponent,
  LegendComponentOption,
  ToolboxComponent,
  ToolboxComponentOption,
  TooltipComponent,
  TooltipComponentOption,
} from "echarts/components";
import * as echarts from "echarts/core";
import { UniversalTransition } from "echarts/features";
import { CanvasRenderer } from "echarts/renderers";
import { onMounted, onUnmounted, reactive, shallowRef } from "vue";
echarts.use([
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
]);

type EChartsOption = echarts.ComposeOption<
  | ToolboxComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | LegendComponentOption
  | BarSeriesOption
  | LineSeriesOption
>;

interface IState {
  year: string;
  yearList: string[];
}

const state = reactive<IState>({
  year: dayjs().format("YYYY"),
  yearList: [dayjs().format("YYYY")],
});

const barEcharts = shallowRef(null);

onMounted(() => {
  barEcharts.value = echarts.init(document.getElementById("chart"));
  let currentYear = Number(dayjs().format("YYYY"));
  let newYearList = [];
  for (let i = 10; i >= 0; i--) {
    newYearList.push(currentYear - i + "");
  }
  state.yearList = newYearList;
  getPurchaseProjectStatistics(state.year).then((res: any) => {
    if (res.code === 0) {
      initEcharts(res.data.numYearMonthResList);
    } else {
      initEcharts([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);
    }
  });
});

const onChangeYear = () => {
  getPurchaseProjectStatistics(state.year).then((res: any) => {
    if (res.code === 0) {
      initEcharts(res.data.numYearMonthResList);
    } else {
      initEcharts([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]);
    }
  });
};

const initEcharts = (numYearMonthResList: number[]) => {
  let option: EChartsOption = {
    type: "category",
    grid: {
      left: "4%",
      top: "10%",
      right: "4%",
      bottom: "35%",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      bottom: 10,
    },
    xAxis: [
      {
        type: "category",
        data: [
          "1月",
          "2月",
          "3月",
          "4月",
          "5月",
          "6月",
          "7月",
          "8月",
          "9月",
          "10月",
          "11月",
          "12月",
        ],
        axisPointer: {
          type: "shadow",
        },
      },
    ],
    yAxis: [
      {
        type: "value",
      },
    ],
    series: [
      {
        name: "项目统计",
        type: "bar",
        tooltip: {
          valueFormatter: function (value) {
            return (value as number) + "";
          },
        },
        itemStyle: {
          color: "#399FFD",
        },
        data: numYearMonthResList,
      },
    ],
  };
  option && barEcharts.value.setOption(option);
};
onUnmounted(() => {
  if (barEcharts.value) {
    barEcharts.value.dispose();
  }
});
</script>
<style lang="scss" scoped>
.statistics {
  background-color: #ffffff;
  border-radius: 4px;
  &-title {
    text-align: right;
    .right {
      width: 120px;
    }
  }
  &-content {
    &-chart {
      height: 158px;
    }
  }
}
</style>
