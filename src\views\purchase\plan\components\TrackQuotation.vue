<template>
  <div class="track">
    <el-tabs v-model="state.activeName" type="card">
      <el-tab-pane label="报价清单" name="quotation">
        <QuotationList
          from="track"
          :packageId="state.quotationInfo.packageId"
          :roundNo="state.quotationInfo.roundNo"
          :roundStatus="state.quotationInfo.roundStatus"
          :maxOffer="state.maxOffer"
          :minOffer="state.minOffer"
          :quotationList="state.quotationList"
        ></QuotationList>
      </el-tab-pane>
      <el-tab-pane label="比价清单" name="compare">
        <CompareList
          :activeName="state.activeName"
          :packageId="state.quotationInfo.packageId"
          :roundNo="state.quotationInfo.roundNo"
        ></CompareList>
      </el-tab-pane>
      <el-tab-pane label="采购清单" name="buy">
        <BuyList
          :activeName="state.activeName"
          :packageId="state.quotationInfo.packageId"
        ></BuyList>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup lang="ts">
import QuotationInfo, { IQuotationInfo } from "./QuotationInfo.vue";
import QuotationList, { IQuotationItem } from "./QuotationList.vue";
import { onMounted, onUnmounted, reactive, watch } from "vue";
import { getQuotationInfo } from "@/api/purchase/plan";
import BuyList from "./BuyList.vue";
import CompareList from "./CompareList.vue";

const props = withDefaults(defineProps<{ id?: number }>(), {
  id: void 0,
});

interface IState {
  interval: any;
  id?: number;
  activeName: string;
  quotationInfo: IQuotationInfo;
  maxOffer?: number;
  minOffer?: number;
  quotationList: IQuotationItem[];
}

const state = reactive<IState>({
  interval: void 0,
  id: void 0,
  activeName: "quotation",
  quotationInfo: {
    packageId: void 0,
    packageNo: "",
    packageName: "",
    packageBudget: "",
    offerStartTime: "",
    offerEndTime: "",
    roundNo: 1,
    roundStatus: 0,
  },
  maxOffer: void 0,
  minOffer: void 0,
  quotationList: [],
});

onMounted(() => {
  state.id = props.id ? props.id : void 0;
  GetQuotationInfo();
});

onUnmounted(() => {
  if (state.interval) {
    clearInterval(state.interval);
    state.interval = void 0;
  }
});

watch(
  () => state.activeName,
  () => {
    if (state.activeName === "quotation") {
      GetQuotationInfo();
    } else {
      if (state.interval) {
        clearInterval(state.interval);
        state.interval = void 0;
      }
    }
  }
);

const GetQuotationInfo = () => {
  if (state.id) {
    getQuotationInfo(state.id).then((res: any) => {
      if (res.code === 0) {
        let newQuotationInfo = {
          packageId: res.data.id,
          packageNo: res.data.packageNo,
          packageName: res.data.packageName,
          packageBudget: res.data.packageBudget,
          offerStartTime: res.data.offerStartTime,
          offerEndTime: res.data.offerEndTime,
          roundNo: res.data.roundNo,
          roundStatus: res.data.roundStatus,
        };
        state.quotationInfo = newQuotationInfo;
        state.maxOffer = res.data?.maxOffer ? res.data?.maxOffer : void 0;
        state.minOffer = res.data?.minOffer ? res.data?.minOffer : void 0;
        state.quotationList = res.data?.quotationVOS ?? [];
      }
    });
  }
};
</script>
<style lang="scss" scoped></style>
