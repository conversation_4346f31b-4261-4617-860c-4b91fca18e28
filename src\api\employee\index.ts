import service from '@/utils/request'

export const getUserCountStatus = () => {
  return service.get('/work/user/info/userCountStatus')
}

export const useUserApi = (id: number) => {
  return service.get('/work/user/info/' + id)
}

export const useUserSubmitApi = (dataForm: any) => {
  if (dataForm.id) {
    return service.put('/work/user/info', dataForm)
  } else {
    return service.post('/work/user/info', dataForm)
  }
}

export const useUserInfoSubmitApi = (dataForm: any) => {
  if (dataForm.id) {
    return service.put('/work/user/info/updateUserInfo', dataForm)
  } else {
    return service.post('/work/user/info', dataForm)
  }
}

export const deleteUserApi = (id: number[]) => {
  return service.delete('/work/user/info/' + id)
}

export const exportUserApi = (reqData: any) => {
  return service.get('/work/user/info/export', { responseType: 'blob', params: reqData })
}

export const getUserInfo = (id: number) => {
  console.log(id)
  return service.get(`/work/user/info/${id}`)
}

export const getUserCertificateList = () => {
  return service.get('/work/userCertificate/list')
}

export const useCertificateSubmitApi = (dataForm: any) => {
  if (dataForm.id) {
    return service.put('/work/userCertificate', dataForm)
  } else {
    return service.post('/work/userCertificate', dataForm)
  }
}

export const getUserCertificateInfo = (id: number) => {
  return service.get(`/work/userCertificate/${id}`)
}

export const getUserSkillList = () => {
  return service.get('/work/userSkills/list')
}

export const useSkillSubmitApi = (dataForm: any) => {
  if (dataForm.id) {
    return service.put('/work/userSkills', dataForm)
  } else {
    return service.post('/work/userSkills', dataForm)
  }
}

export const getUserSkillInfo = (id: number) => {
  return service.get(`/work/userSkills/${id}`)
}

export const useEducationSubmitApi = (dataForm: any) => {
  if (dataForm.id) {
    return service.put('/work/userEducation/info', dataForm)
  } else {
    return service.post('/work/userEducation/info', dataForm)
  }
}

export const getUserEducationInfo = (id: number) => {
  return service.get(`/work/userEducation/info/${id}`)
}

export const useWorkSubmitApi = (dataForm: any) => {
  if (dataForm.id) {
    return service.put('/work/userWork/info', dataForm)
  } else {
    return service.post('/work/userWork/info', dataForm)
  }
}

export const getUserWorkInfo = (id: number) => {
  return service.get(`/work/userWork/info/${id}`)
}

export const useProjectSubmitApi = (dataForm: any) => {
  if (dataForm.id) {
    return service.put('/work/userProject/info', dataForm)
  } else {
    return service.post('/work/userProject/info', dataForm)
  }
}

export const getProjectWorkInfo = (id: number) => {
  return service.get(`/work/userProject/info/${id}`)
}

export const getCompanyList = () => {
  return service.get('/sys/org/listCompany')
}

export const useContractSubmitApi = (dataForm: any) => {
  if (dataForm.id) {
    return service.put('/work/userContract/info', dataForm)
  } else {
    return service.post('/work/userContract/info', dataForm)
  }
}

export const getContractWorkInfo = (id: number) => {
  return service.get(`/work/userContract/info/${id}`)
}

export const saveUserFiles = (dataForm: any) => {
  return service.post('/work/user/info/saveUserFiles', dataForm)
}

export const getUserFiles = (id: number) => {
  return service.get(`/work/user/info/findFileById/${id}`)
}

export const getCompanyInfo = (userId: number) => {
  return service.get(`/work/company/getByUserId/${userId}`)
}

export const saveCompanyInfo = (dataForm: any) => {
  if (dataForm.id) {
    return service.put('/work/company', dataForm)
  } else {
    return service.post('/work/company', dataForm)
  }
}

export const sendEntryMsg = (reqData: any) => {
  return service.post('/work/user/info/sendEntryMsg', reqData)
}






