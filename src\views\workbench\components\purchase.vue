<template>
  <div class="title-line">
    <div class="title">基本信息</div>
    <el-link type="primary" @click="lookSpList">查看审批数据</el-link>
  </div>
  <div class="content">
    <BaseInfo
      :type="state.type"
      :baseInfo="state.baseInfo"
      @on-change-org="onChangeOrg"
      @on-change-value="onChangeBaseInfo"
      @emit-ref="onEmitBaseInfoRef"
      @on-select-manager="onSelectUser('manage')"
      @on-select-project="onSelectProject"
    ></BaseInfo>
    <div class="plist">
      采购清单
      <el-button
        type="primary"
        class="ml10"
        @click="selectMaterialItem"
        :disabled="state.materialListDiaAll.length <= 0"
      >
        选择清单项
      </el-button>
    </div>
    <PurchaseList
      :attachmentList="state.attachmentList"
      :materialList="state.materialList"
      @change-quantity="onChangeQuantity"
      @change-comment="onChangeComment"
      @on-delete="onDeleteMaterial"
      @upload-field="onUploadField"
    ></PurchaseList>
    <!-- 审批流程 -->
    <approval ref="approvalRef" @emit-user="getOAuser" @emit-user-copy="getOAuserCopy" />
    <div class="btns">
      <el-button type="primary" @click="onSubmit" :loading="state.submitLoading"
        >提交</el-button
      >
    </div>
  </div>
  <UserSelect
    :show="state.userAction.show"
    :title="state.userAction.title"
    @on-close="onCloseSelectUser"
    @on-select="onConfirmSelectUser"
  ></UserSelect>
  <!-- 弹框 -->
  <ProjectSelect
    :show="state.projectActionShow"
    @on-close="onCloseSelectProject"
    @on-select="onConfirmSelectProject"
  ></ProjectSelect>
  <MaterialSelect
    :show="state.MaterialSelectShow"
    :list="state.materialListDiaAll"
    :selectlist="state.materialList"
    @on-select="onSelectMaterial"
    @on-close="onCloseSelectMaterial"
  ></MaterialSelect>
</template>

<script setup lang="ts">
import {
  IActionProjectPlan,
  getPackageNo,
  saveProjectPlan,
  getProjectPlanInfo,
  getQuotationInfo,
  updateProjectPlan,
} from "@/api/purchase/plan";
import { getProjectInfo } from "@/api/purchase/project";
import ProjectSelect from "@/views/purchase/plan/components/ProjectSelect.vue";
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import { ElMessage } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import BaseInfo from "./BaseInfo.vue";
import ApprovalProcess from "@/views/purchase/plan/components/ApprovalProcess.vue";
import PurchaseList from "./PurchaseList.vue";
import UserSelect from "@/views/purchase/plan/components/UserSelect.vue";
import approval from "./approval.vue";
import MaterialSelect from "@/views/purchase/plan/components/MaterialSelect.vue";
import { workGetStatusById } from "@/api/workbench";

export interface IBaseInfo {
  projectId?: number;
  projectName: string;
  packageNo: string;
  packageName: string;
  packageBudget?: number;
  packageType: string;
  packageMode: string;
  orgId?: number;
  orgName?: string;
  managerId?: number;
  managerName: string;
  comment: string;
  deliveryAddress: string;
  oaFlowPersonVOS: [];
}

export interface IAuditInfo {
  reviewedById?: number;
  reviewedBy: string;
}
export interface IMaterialItem {
  id: number;
  materialNo: string;
  materialName: string;
  materialSpec: string;
  materialType: string;
  materialUnit: string;
  materialQuantity?: number;
  surplusQuantity?: number;
  comment: string;
}
interface IUserAction {
  show: boolean;
  title: string;
  type: "manage" | "audit";
}
export interface IAttachmentItem {
  name: string;
  url: string;
  platform: string;
  size: number;
}

interface IState {
  type: string;
  id?: number;
  baseInfo: IBaseInfo;
  userAction: IUserAction;
  materialList: IMaterialItem[];
  materialListDiaAll: IMaterialItem[];
  attachmentList: IAttachmentItem[];
  MaterialSelectShow: boolean;
  projectActionShow: boolean;
  auditInfo: IAuditInfo;
  submitLoading: boolean;
  oaflowuser1: [];
  oaflowuser2: [];
}

const state = reactive<IState>({
  type: "add",
  id: void 0,
  baseInfo: {
    projectId: void 0,
    projectName: "",
    packageNo: "",
    packageName: "",
    packageBudget: void 0,
    packageType: "1",
    deliveryAddress: "",
    packageMode: "",
    orgId: void 0,
    orgName: "",
    managerId: void 0,
    managerName: "",
    comment: "",
    oaFlowPersonVOS: [],
  },
  MaterialSelectShow: false,
  userAction: {
    show: false,
    title: "选择项目经理",
    type: "manage",
  },
  projectActionShow: false,
  materialList: [],
  materialListDiaAll: [],
  attachmentList: [],
  auditInfo: {
    reviewedById: void 0,
    reviewedBy: "",
  },
  submitLoading: false,
  oaflowuser1: [],
  oaflowuser2: [],
});
interface IProps {
  id: number;
}
const props = withDefaults(defineProps<IProps>(), {
  id: 0,
});
const baseInfoRef = ref();
const approvalRef = ref();

const onEmitBaseInfoRef = (dataFormRef: any) => {
  baseInfoRef.value = dataFormRef;
};
import { useRouter } from "vue-router";
const router = useRouter();
// 查看审批数据
const lookSpList = () => {
  router.push({
    path: "/approvalCenter/imake/index",
    query: {
      flowType: "5",
    },
  });
};
onMounted(() => {
  if (props.id !== 0) {
    state.type = "update";
    getDetailList(props.id);
  }
});
// 获取详情数据
const getDetailList = (id) => {
  workGetStatusById(id).then((res) => {
    if (res.code !== 0) {
      return ElMessage.error(res.msg);
    }
    state.id = res.data.infoId;
    let oaPackageVo = res.data.oaPackageVo;
    state.baseInfo = { ...oaPackageVo };
    onConfirmSelectProject({ id: oaPackageVo.projectId });
    state.attachmentList = oaPackageVo.attachmentList ? oaPackageVo.attachmentList : [];
    let materialList = (oaPackageVo.materialVOS ?? []).map((item: any) => {
      return {
        id: item?.materialId ?? void 0,
        materialNo: item?.materialNo ?? "",
        materialName: item?.materialName ?? "",
        materialSpec: item?.materialSpec ?? "",
        materialType: item?.materialType ?? "",
        materialUnit: item?.materialUnit ?? "",
        materialQuantity: item.materialQuantity,
        surplusQuantity: item.surplusQuantity,
        comment: item?.comment ?? "",
      };
    });
    state.materialList = materialList;
    let oaflowuser1 = [];
    if (state.type === "update") {
      oaflowuser1 = (res.data.oaFlowPersonVOS ?? [])
        .filter((item) => {
          return item.status != 6;
        })
        .map((item: any) => {
          return {
            children: [],
            id: item.userId,
            name: item.userName,
            parentName: item.orgName,
            pid: item.orgId,
            status: item.status ? item.status : "",
          };
        });
    } else {
      oaflowuser1 = (res.data.oaFlowPersonVOS ?? []).map((item: any) => {
        return {
          children: [],
          id: item.userId,
          name: item.userName,
          parentName: item.orgName,
          pid: item.orgId,
          status: item.status ? item.status : "",
        };
      });
    }
    let oaflowuser2 = (res.data.oaFlowPersonVOSCS ?? []).map((item: any) => {
      return {
        children: [],
        id: item.userId,
        name: item.userName,
        parentName: item.orgName,
        pid: item.orgId,
        status: item.status,
      };
    });
    // state.oaflowuser1 = oaflowuser1;
    // state.oaflowuser2 = oaflowuser2;
    state.oaflowuser1 = oaflowuser1.map((item, index) => {
      return {
        type: 1,
        status: index === 0 ? 3 : 2,
        userId: item.id,
        userName: item.name,
      };
    });
    state.oaflowuser2 = oaflowuser2.map((item, index) => {
      return {
        type: 2,
        userId: item.id,
        userName: item.name,
      };
    });
    approvalRef.value.init(oaflowuser1, oaflowuser2);
  });
};
const onSelectUser = (type: "manage" | "audit") => {
  if (type == "manage") {
    state.userAction.title = "选择项目经理";
  } else {
    state.userAction.title = "选择审批人";
  }
  state.userAction.type = type;
  state.userAction.show = true;
};
const onCloseSelectUser = () => {
  state.userAction.show = false;
};
const onConfirmSelectUser = (row: any) => {
  if (state.userAction.type === "manage") {
    let newBaseInfo = JSON.parse(JSON.stringify(state.baseInfo));
    newBaseInfo.managerId = row.id;
    newBaseInfo.managerName = row.username;
    console.log(newBaseInfo);
    state.baseInfo = newBaseInfo;
  } else {
    let newAuditInfo = JSON.parse(JSON.stringify(state.auditInfo));
    newAuditInfo.reviewedById = row.id;
    newAuditInfo.reviewedBy = row.username;
    state.auditInfo = newAuditInfo;
  }
};
const onSelectProject = () => {
  state.projectActionShow = true;
};
const onCloseSelectProject = () => {
  state.projectActionShow = false;
};

const onConfirmSelectProject = (row: any) => {
  state.projectActionShow = false;
  let projectId = row.id;

  getProjectInfo(projectId).then((res: any) => {
    if (res.code === 0) {
      if (state.type === "add") {
        getPackageNo(projectId).then((resPackageNo: any) => {
          if (resPackageNo.code === 0) {
            let newBaseInfo = JSON.parse(JSON.stringify(state.baseInfo));
            newBaseInfo.projectId = projectId;
            newBaseInfo.projectName = res.data.projectName;
            newBaseInfo.packageBudget = res.data.budget;
            newBaseInfo.orgId = res.data.orgId;
            newBaseInfo.packageNo = resPackageNo.data;
            state.baseInfo = newBaseInfo;
            let newMaterialList = (res.data.tbProjectMaterialVOS ?? []).map(
              (item: any) => {
                return {
                  id: item?.materialId ?? void 0,
                  materialNo: item?.materialId ?? "",
                  materialName: item?.materialName ?? "",
                  materialSpec: item?.materialSpec ?? "",
                  materialType: item?.materialType ?? "",
                  materialUnit: item?.materialUnit ?? "",
                  materialQuantity: item.materialQuantity,
                  surplusQuantity: item.surplusQuantity,
                  comment: item?.comment ?? "",
                };
              }
            );
            state.materialListDiaAll = newMaterialList;
          } else {
            ElMessage.error("自动获取计划编号失败!");
          }
        });
      } else {
        let newBaseInfo = JSON.parse(JSON.stringify(state.baseInfo));
        newBaseInfo.projectId = projectId;
        newBaseInfo.projectName = res.data.projectName;
        newBaseInfo.packageBudget = res.data.budget;
        state.baseInfo = newBaseInfo;
        let newMaterialList = (res.data.tbProjectMaterialVOS ?? []).map((item: any) => {
          return {
            id: item?.materialId ?? void 0,
            materialNo: item?.materialId ?? "",
            materialName: item?.materialName ?? "",
            materialSpec: item?.materialSpec ?? "",
            materialType: item?.materialType ?? "",
            materialUnit: item?.materialUnit ?? "",
            materialQuantity: item.materialQuantity,
            surplusQuantity: item.surplusQuantity,
            comment: item?.comment ?? "",
          };
        });
        state.materialListDiaAll = newMaterialList;
      }
    }
  });
};

const onChangeBaseInfo = (field: string, value: string | number) => {
  let newBaseInfo = JSON.parse(JSON.stringify(state.baseInfo));
  newBaseInfo[field] = value;
  state.baseInfo = newBaseInfo;
};

const onChangeOrg = (orgId: number, orgName: string) => {
  let newBaseInfo = JSON.parse(JSON.stringify(state.baseInfo));
  newBaseInfo.orgId = orgId;
  newBaseInfo.orgName = orgName;
  state.baseInfo = newBaseInfo;
};

const onChangeQuantity = (rowIndex: number, value: string) => {
  let newMaterialList = JSON.parse(JSON.stringify(state.materialList));
  newMaterialList[rowIndex].materialQuantity = value;
  state.materialList = newMaterialList;
};

const onChangeComment = (rowIndex: number, value: string) => {
  let newMaterialList = JSON.parse(JSON.stringify(state.materialList));
  newMaterialList[rowIndex].comment = value;
  state.materialList = newMaterialList;
};

const onDeleteMaterial = (rowIndex: number) => {
  let newMaterialList = JSON.parse(JSON.stringify(state.materialList));
  newMaterialList = newMaterialList.filter((item: IMaterialItem, index: number) => {
    return index !== rowIndex;
  });
  state.materialList = newMaterialList;
};

const onUploadField = (attachmentList: IAttachmentItem[]) => {
  let newAttachmentList = JSON.parse(JSON.stringify(state.attachmentList));
  newAttachmentList = attachmentList;
  state.attachmentList = newAttachmentList;
};
const getOAuser = (val: any) => {
  let arr = val.map((item) => ({
    type: 1,
    status: 2,
    userId: item.id,
    userName: item.name,
  }));
  arr[0].status = 3;
  state.oaflowuser1 = arr;
};
const getOAuserCopy = (val: any) => {
  state.oaflowuser2 = val.map((item) => ({
    type: 2,
    userId: item.id,
    userName: item.name,
  }));
};
const onSubmit = () => {
  baseInfoRef.value.validate((valid: boolean) => {
    if (valid) {
      if (state.oaflowuser1.length <= 0) return ElMessage.error("请选择审批人");
      if (
        state.materialList
          .map((item) => item.materialQuantity || 0)
          .filter((item) => item <= 0).length > 0
      )
        return ElMessage.error("采购数量必须大于0");
      state.baseInfo.oaFlowPersonVOS = [...state.oaflowuser1, ...state.oaflowuser2];
      let reqData: IActionProjectPlan = {
        id: state.type === "update" ? state.id : void 0,
        projectId: state.baseInfo.projectId,
        projectName: state.baseInfo.projectName,
        packageName: state.baseInfo.packageName,
        packageNo: state.baseInfo.packageNo,
        packageBudget: state.baseInfo.packageBudget,
        packageType: state.baseInfo.packageType,
        packageMode: state.baseInfo.packageMode,
        managerId: state.baseInfo.managerId,
        managerName: state.baseInfo.managerName,
        comment: state.baseInfo.comment,
        auditStatus: "2",
        orgId: state.baseInfo.orgId,
        orgName: state.baseInfo.orgName,
        deliveryAddress: state.baseInfo.deliveryAddress,
        reviewedById: state.auditInfo.reviewedById,
        reviewedBy: state.auditInfo.reviewedBy,
        oaFlowPersonVOS: state.baseInfo.oaFlowPersonVOS,
        attachmentList: state.attachmentList.map((item) => {
          return {
            name: item.name,
            url: item.url,
            platform: item.platform,
            size: item.size,
          };
        }),
        materialVOS: state.materialList.map((item) => {
          return {
            materialId: item?.id ?? void 0,
            materialNo: item?.materialNo ?? "",
            materialName: item?.materialName ?? "",
            materialSpec: item?.materialSpec ?? "",
            materialType: item?.materialType ?? "",
            materialUnit: item?.materialUnit ?? "",
            materialQuantity: item.materialQuantity,
            surplusQuantity: item.surplusQuantity,
            comment: item?.comment ?? "",
          };
        }),
      };
      console.log(reqData);
      state.submitLoading = true;
      saveProjectPlan(reqData).then((res: any) => {
        state.submitLoading = false;
        if (res.code === 0) {
          ElMessage.success("操作成功");
          resetData();
          router.push({
            path: "/workbench/index",
            query: { type: "add", activeTab: "5" },
          });
        }
      });
    }
  });
};
// 重置数据
const resetData = () => {
  baseInfoRef.value.clearValidate();
  baseInfoRef.value.resetFields();
  approvalRef.value.clearInfo();
  state.materialList = [];
  state.materialListDiaAll = [];
  state.oaflowuser1 = [];
  state.oaflowuser2 = [];
  state.baseInfo = {
    projectId: void 0,
    projectName: "",
    packageNo: "",
    packageName: "",
    packageBudget: void 0,
    packageType: "1",
    deliveryAddress: "",
    packageMode: "",
    orgId: void 0,
    orgName: "",
    managerId: void 0,
    managerName: "",
    comment: "",
    oaFlowPersonVOS: [],
  };
};
const selectMaterialItem = () => {
  state.MaterialSelectShow = true;
};
const onSelectMaterial = (materialList: IMaterialItem[]) => {
  materialList.map((item) => (item.materialQuantity = item.surplusQuantity));
  state.materialList = materialList;
  state.MaterialSelectShow = false;
};
const onCloseSelectMaterial = () => {
  state.MaterialSelectShow = false;
};
</script>

<style scoped lang="scss">
.title-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .title {
    text-align: center;
    padding: 8px 0;
    width: 100px;
    border-left: 3px solid #409eff;
  }
}
.editor {
  width: 98%;
}
.btns {
  margin: 20px 0;
}

.spec {
  width: 100px;
  margin: 0 20px;
}
</style>
