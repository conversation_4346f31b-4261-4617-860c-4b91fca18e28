<template>
  <div class="info">
    <el-form
      ref="dataFormRef"
      :model="props.baseInfo"
      label-width="140px"
      class="info-form"
    >
      <el-form-item prop="projectName" label="项目">
        <div class="info-form-value">
          {{ props.baseInfo.projectName }}
        </div>
      </el-form-item>
      <el-form-item prop="packageNo" label="采购计划编号">
        <div class="info-form-value">
          {{ props.baseInfo.packageNo }}
        </div>
      </el-form-item>
      <el-form-item prop="packageName" label="采购计划名称">
        <div class="info-form-value">
          {{ props.baseInfo.packageName }}
        </div>
      </el-form-item>
      <el-form-item prop="packageBudget" label="本次预算金额（元）">
        <div class="info-form-value">
          {{ props.baseInfo.packageBudget }}
        </div>
      </el-form-item>
      <el-form-item prop="projectName" label="采购方式">
        <div class="info-form-value">
          <span
            v-html="getDictLabelList('package_type', props.baseInfo.packageType)"
          ></span>
        </div>
      </el-form-item>
      <el-form-item prop="packageMode" label="采购类别">
        <div class="info-form-value">
          <span
            v-html="getDictLabelList('package_mode', props.baseInfo.packageMode)"
          ></span>
        </div>
      </el-form-item>
      <el-form-item prop="orgName" label="申请部门">
        <div class="info-form-value">
          {{ props.baseInfo.orgName }}
        </div>
      </el-form-item>
      <el-form-item prop="managerName" label="项目经理">
        <div class="info-form-value">
          {{ props.baseInfo.managerName }}
        </div>
      </el-form-item>
      <el-form-item prop="deliveryAddress" label="送货地址">
        <div class="info-form-value">
          {{ props.baseInfo.deliveryAddress }}
        </div>
      </el-form-item>
      <el-form-item prop="comment" label="采购说明">
        <div class="info-form-value">
          <WangEditor v-model="props.baseInfo.comment" :disabled="true"></WangEditor>
        </div>
      </el-form-item>
      <el-form-item prop="materialVOS" label="采购清单">
        <div style="width: 100%">
          <el-table :data="props.baseInfo.materialVOS" border>
            <el-table-column
              type="index"
              label="序号"
              header-align="center"
              align="center"
              width="70"
            ></el-table-column>
            <el-table-column
              prop="materialName"
              label="产品名称"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="materialType"
              label="产品型号（规格参数）"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="materialUnit"
              label="计量单位"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="materialQuantity"
              label="采购数量"
              header-align="center"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="comment"
              label="备注"
              header-align="center"
              align="center"
            ></el-table-column>
          </el-table>
        </div>
      </el-form-item>
      <el-form-item prop="attachmentList" label="附件">
        <div class="info-form-list">
          <div class="info-form-list-item" v-for="item in props.baseInfo.attachmentList">
            <div class="info-form-list-item-text">
              {{ item.name }}
            </div>
            <div class="info-form-list-item-action">
              <el-icon class="action-icon" @click="onClickDownload(item)"
                ><Download
              /></el-icon>
            </div>
          </div>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { getDictLabelList } from "@/utils/tool";
import { IAttachmentItem, IBaseInfo } from "./Detail.vue";
import WangEditor from "@/components/wang-editor/index.vue";
import service from "@/utils/request";
import { Download } from "@element-plus/icons-vue";

interface IProps {
  baseInfo: IBaseInfo;
}
const props = withDefaults(defineProps<IProps>(), {
  baseInfo: () => {
    return {
      id: void 0,
      projectName: "",
      packageNo: "",
      packageName: "",
      packageBudget: void 0,
      packageType: "",
      packageTypeLabel: "",
      packageMode: "",
      packageModeLabel: "",
      orgName: "",
      managerName: "",
      deliveryAddress: "",
      comment: "",
      attachmentList: [],
      materialVOS: [],
    };
  },
});

const onClickDownload = async (item: IAttachmentItem) => {
  const response = await service.get(item.url, {
    responseType: "blob", // important
  });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", item.name);
  document.body.appendChild(link);
  link.click();
};
</script>

<style lang="scss" scoped>
.info {
  &-form {
    &-value {
      color: #000;
    }
    &-list {
      &-item {
        display: flex;
        align-items: center;
        &-text {
          color: #409eff;
          cursor: pointer;
        }
        &-action {
          color: #545252;
          cursor: pointer;
          .action-icon {
            margin-left: 4px;
          }
        }
      }
    }
  }
}
</style>
