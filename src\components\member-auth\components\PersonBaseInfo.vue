<template>
  <div class="info">
    <el-form
      ref="infoRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="140px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="contactName" label="姓名">
            <el-input
              v-model="state.dataForm.contactName"
              placeholder="姓名"
              @blur="onBlurField('contactName')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.contactName }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="auditStatus" label="认证状态" class="info_status">
            <div class="info_status_content">
              <div
                class="info_status_content_audit"
                v-if="state.dataForm.auditStatus == '2'"
              >
                审核中
              </div>
              <div
                class="info_status_content_pass"
                v-else-if="state.dataForm.auditStatus == '3'"
              >
                认证通过
              </div>
              <div
                class="info_status_content_pass_no"
                v-else-if="state.dataForm.auditStatus == '4'"
              >
                认证不通过
              </div>
              <div class="info_status_content_no" v-else>未认证</div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="idNumber" label="身份证号">
            <el-input
              v-model="state.dataForm.idNumber"
              placeholder="身份证号"
              @blur="onBlurField('idNumber')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.idNumber }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="contactPhone" label="联系电话">
            <el-input
              v-model="state.dataForm.contactPhone"
              placeholder="联系电话"
              @blur="onBlurField('contactPhone')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.contactPhone }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="email" label="电子邮箱">
            <el-input
              v-model="state.dataForm.email"
              placeholder="电子邮箱"
              @blur="onBlurField('email')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.email }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="address" label="联系地址">
            <el-input
              v-model="state.dataForm.address"
              placeholder="联系地址"
              @blur="onBlurField('address')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.address }}</div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="openingBank" label="开户银行">
            <el-input
              v-model="state.dataForm.openingBank"
              placeholder="开户银行"
              @blur="onBlurField('openingBank')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.openingBank }}</div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="accountNumber" label="基本账户账号">
            <el-input
              v-model="state.dataForm.accountNumber"
              placeholder="基本账户账号"
              @blur="onBlurField('accountNumber')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.accountNumber }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="zipCode" label="邮政编码">
            <el-input
              v-model="state.dataForm.zipCode"
              placeholder="邮政编码"
              @blur="onBlurField('zipCode')"
              v-if="props.action === 'edit'"
            ></el-input>
            <div class="info_form_value" v-else>{{ state.dataForm.zipCode }}</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="authorizedIdCardFront" label="身份证">
            <div class="info_card">
              <div class="info_card_left">
                <div class="idcard" style="margin-right: 16px">
                  <div
                    class="idcard_img"
                    v-if="(state.dataForm.authorizedIdCardFront ?? '') !== ''"
                  >
                    <el-image
                      :src="state.dataForm.authorizedIdCardFront"
                      class="idcard_img_main"
                    ></el-image>
                    <div class="idcard_img_action">
                      <el-icon
                        class="idcard_img_action_icon"
                        @click="onPreviewImage(state.dataForm.authorizedIdCardFront)"
                      >
                        <View />
                      </el-icon>
                      <el-icon
                        class="idcard_img_action_icon"
                        style="margin-left: 10px"
                        @click="onDeleteImage('authorizedIdCardFront')"
                        v-if="props.action === 'edit'"
                      >
                        <Delete />
                      </el-icon>
                    </div>
                  </div>
                  <div class="idcard_action" v-else>
                    <el-upload
                      class="idcard_action_upload"
                      :headers="{ Authorization: cache.getToken() }"
                      :action="constant.uploadUrl"
                      :before-upload="beforeUpload"
                      :on-success="
                        (response:any) => {
                          handleSuccess(response, 'authorizedIdCardFront');
                        }
                      "
                      :show-file-list="false"
                      v-if="props.action === 'edit'"
                    >
                      <div
                        style="display: flex; align-items: center; flex-direction: column"
                      >
                        <el-icon class="idcard_action_upload_icon"><Plus /></el-icon>
                        <div class="idcard_action_upload_desc">请上传身份证正面</div>
                      </div>
                    </el-upload>
                  </div>
                </div>
                <div class="idcard">
                  <div
                    class="idcard_img"
                    v-if="(state.dataForm.authorizedIdCardBack ?? '') !== ''"
                  >
                    <el-image
                      :src="state.dataForm.authorizedIdCardBack"
                      class="idcard_img_main"
                    ></el-image>
                    <div class="idcard_img_action">
                      <el-icon
                        class="idcard_img_action_icon"
                        @click="onPreviewImage(state.dataForm.authorizedIdCardBack)"
                      >
                        <View />
                      </el-icon>
                      <el-icon
                        class="idcard_img_action_icon"
                        style="margin-left: 10px"
                        @click="onDeleteImage('authorizedIdCardBack')"
                        v-if="props.action === 'edit'"
                      >
                        <Delete />
                      </el-icon>
                    </div>
                  </div>
                  <div class="idcard_action" v-else>
                    <el-upload
                      class="idcard_action_upload"
                      :headers="{ Authorization: cache.getToken() }"
                      :action="constant.uploadUrl"
                      :before-upload="beforeUpload"
                      :on-success="
                        (response:any) => {
                          handleSuccess(response, 'authorizedIdCardBack');
                        }
                      "
                      :show-file-list="false"
                      v-if="props.action === 'edit'"
                    >
                      <div
                        style="display: flex; align-items: center; flex-direction: column"
                      >
                        <el-icon class="idcard_action_upload_icon"><Plus /></el-icon>
                        <div class="idcard_action_upload_desc">请上传身份证背面</div>
                      </div>
                    </el-upload>
                  </div>
                </div>
              </div>
              <div class="info_card_right">
                请上传清晰身份证正反面照片，支持.jpg .png格式
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-image-viewer
      v-if="state.previewImage.show"
      @close="onClosePreviewImage"
      :url-list="state.previewImage.url"
    >
    </el-image-viewer>
  </div>
</template>
<script setup lang="ts">
import { ElMessage, FormRules, UploadProps } from "element-plus";
import { onMounted, reactive, ref, nextTick, watch } from "vue";
import constant from "@/utils/constant";
import cache from "@/utils/cache";
import { Plus, View, Delete } from "@element-plus/icons-vue";

export interface IPersonBaseInfo {
  contactName: string;
  auditStatus: string;
  idNumber: string;
  contactPhone: string;
  email: string;
  address: string;
  openingBank: string;
  accountNumber: string;
  zipCode: string;
  authorizedIdCardFront: string;
  authorizedIdCardBack: string;
}

const infoRef = ref();

interface IProps {
  action: string;
  personBaseInfo: IPersonBaseInfo;
}

const props = withDefaults(defineProps<IProps>(), {
  action: "edit",
  personBaseInfo: () => {
    return {
      contactName: "",
      auditStatus: "",
      idNumber: "",
      contactPhone: "",
      email: "",
      address: "",
      openingBank: "",
      accountNumber: "",
      zipCode: "",
      authorizedIdCardFront: "",
      authorizedIdCardBack: "",
    };
  },
});

interface IPreviewImage {
  show: boolean;
  url: string[];
}

interface IState {
  dataForm: IPersonBaseInfo;
  dataRules: FormRules;
  previewImage: IPreviewImage;
}

const validatorIdCard = (rule: any, value: any, callback: any) => {
  if (
    (state.dataForm.authorizedIdCardFront ?? "") === "" ||
    (state.dataForm.authorizedIdCardBack ?? "") === ""
  ) {
    callback(new Error("请上传身份证照片"));
  } else {
    callback();
  }
};

const state = reactive<IState>({
  dataForm: {
    contactName: "",
    auditStatus: "",
    idNumber: "",
    contactPhone: "",
    email: "",
    address: "",
    openingBank: "",
    accountNumber: "",
    zipCode: "",
    authorizedIdCardFront: "",
    authorizedIdCardBack: "",
  },
  dataRules: {
    contactName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
    idNumber: [{ required: true, message: "请输入身份证号", trigger: "blur" }],
    contactPhone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
    email: [{ required: true, message: "请输入邮箱", trigger: "blur" }],
    address: [{ required: true, message: "请输入地址", trigger: "blur" }],
    openingBank: [{ required: true, message: "请输入开户银行", trigger: "blur" }],
    accountNumber: [{ required: true, message: "请输入基本账户账号", trigger: "blur" }],
    authorizedIdCardFront: [
      {
        required: true,
        message: "请上传身份证照片",
        trigger: "change",
      },
      {
        validator: validatorIdCard,
        trigger: "change",
      },
    ],
  },
  previewImage: {
    show: false,
    url: [],
  },
});

watch(
  () => props.personBaseInfo,
  () => {
    let newBaseInfo = JSON.parse(JSON.stringify(props.personBaseInfo));
    state.dataForm = newBaseInfo;
  },
  { immediate: true }
);

const emit = defineEmits<{
  (e: "on-change-value", type: string, field: string, value: any): void;
  (e: "emit-ref", baseInfoRef: any): void;
}>();

onMounted(() => {
  nextTick(() => {
    emit("emit-ref", infoRef.value);
  });
});

const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  console.log(file);
  let fileNameList = file.name.split(".");
  let fileType = fileNameList[fileNameList.length - 1];
  if (["jpg", "png", "jpeg"].indexOf(fileType) == -1) {
    ElMessage.error("请上传图片文件");
    return false;
  }
  // if (file.size / 1024 / 1024 / 1024 / 1024 > 1) {
  // 	ElMessage.error('文件大小不能超过100M')
  // 	return false
  // }
  return true;
};

const handleSuccess = (res: any, type: string) => {
  if (res.code === 0) {
    let url = res.data.url;
    switch (type) {
      case "authorizedIdCardFront":
        state.dataForm.authorizedIdCardFront = url;
        break;
      case "authorizedIdCardBack":
        state.dataForm.authorizedIdCardBack = url;
        break;
      default:
        break;
    }
    // @ts-ignore
    emit("on-change-value", "personBaseInfo", type, url);
  } else {
    ElMessage.error("上传失败：" + res.msg);
  }
};

const onBlurField = (field: string) => {
  // @ts-ignore
  emit("on-change-value", "personBaseInfo", field, state.dataForm[field]);
};

//#region 预览相关

const onDeleteImage = (type: string) => {
  switch (type) {
    case "authorizedIdCardFront":
      state.dataForm.authorizedIdCardFront = "";
      break;
    case "authorizedIdCardBack":
      state.dataForm.authorizedIdCardBack = "";
      break;
    default:
      break;
  }
  // @ts-ignore
  emit("on-change-value", "personBaseInfo", type, "");
};

const onPreviewImage = (url: string) => {
  let newUrl = [];
  newUrl.push(url);
  state.previewImage.url = newUrl;
  state.previewImage.show = true;
};

const onClosePreviewImage = () => {
  state.previewImage.show = false;
  state.previewImage.url = [];
};
//#endregion
</script>
<style lang="scss" scoped>
.info {
  width: 100%;
  max-width: 1000px;
  margin-top: 10px;
  &_number {
    :deep(.el-input__inner) {
      text-align: left;
    }
  }
  &_card {
    display: flex;
    align-items: center;
    &_left {
      display: flex;
      align-items: center;
    }
    &_right {
      margin-left: 16px;
      color: #f56c6c;
    }
  }
}

.idcard {
  &_img {
    width: 180px;
    height: 120px;
    position: relative;
    border: 1px dashed #fff;
    &_main {
      width: 180px;
      height: 120px;
    }
    &_action {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      display: none;
      &_icon {
        color: #ffffff;
        font-size: 16px;
        cursor: pointer;
      }
    }
  }
  &_action {
    &_upload {
      width: 180px;
      height: 120px;
      border: 1px dashed #cdd0d6;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      &_icon {
        font-size: 24px;
        color: #909399;
      }
      &_desc {
        font-size: 14px;
        color: #909399;
      }
    }
  }
}
.idcard_img:hover {
  .idcard_img_action {
    display: flex;
  }
}
</style>
