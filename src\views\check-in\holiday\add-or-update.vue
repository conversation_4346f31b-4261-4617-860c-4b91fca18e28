<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="150px"
      @keyup.enter="submitHandle()"
    >
      <el-form-item label="请假事项" prop="ruleName">
        <el-input v-model="dataForm.ruleName" placeholder="请假事项"></el-input>
      </el-form-item>
      <el-form-item label="适用范围" prop="applicableScope">
        <div style="width: 100%">
          <div>
            <fast-radio-group
              v-model="dataForm.applicableScope"
              dict-type="applicable_scope"
            ></fast-radio-group>
          </div>
          <div v-if="dataForm.applicableScope == 1">
            <div @click="onClickSelectUser">
              <el-select
                v-model="dataForm.userList"
                multiple
                filterable
                allow-create
                placeholder="请选择人员"
                @click="onClickSelectUser"
              >
              </el-select>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="请假单位" prop="leaveRequestingUnit">
        <fast-radio-group
          v-model="dataForm.leaveRequestingUnit"
          dict-type="leave_requesting_unit"
        ></fast-radio-group>
      </el-form-item>
      <el-form-item label="时长计算方式" prop="durationCalculationMethod">
        <fast-select
          v-model="dataForm.durationCalculationMethod"
          dict-type="duration_calculation_method"
          clearable
          placeholder="时长计算方式"
        ></fast-select>
        <div style="color: #aaaaaa; font-size: 12px">
          举例：婚假、产假一般按自然日计算，事假按工作日计算
        </div>
      </el-form-item>
      <el-form-item label="是否启用" prop="status">
        <fast-radio-group
          v-model="dataForm.status"
          dict-type="enable_disable"
        ></fast-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
  <select-user-tree
    ref="selectUserTreeRef"
    @select-user="getSelectUser"
  ></select-user-tree>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { ElMessage } from "element-plus/es";
import { useHolidayRuleApi, useHolidayRuleSubmitApi } from "@/api/check-in/holiday";
import SelectUserTree from "@/views/workbench/components/selectUserTree.vue";

const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();

const options = ref([]);

const dataForm = reactive({
  id: "",
  ruleName: "",
  applicableScope: "0",
  leaveRequestingUnit: "0",
  durationCalculationMethod: "0",
  status: "1",
  userList: [],
});

const init = (id?: number) => {
  visible.value = true;
  dataForm.id = "";
  dataForm.userList = [];

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  if (id) {
    getHolidayRule(id);
  }
};

const getHolidayRule = (id: number) => {
  useHolidayRuleApi(id).then((res) => {
    Object.assign(dataForm, res.data);
    dataForm.userList = res.data.userList.map((item: any) => {
      return {
        value: item.id,
        label: item.realName,
      };
    });
    console.log(dataForm.userList);
  });
};

const validatorApplicableScope = () => {
  if (dataForm.applicableScope == 1 && dataForm.userList.length == 0) {
    return new Error("请选择人员");
  }
  return true;
};

const dataRules = ref({
  ruleName: [{ required: true, message: "请假事项不能为空", trigger: "blur" }],
  applicableScope: [
    { required: true, message: "适用范围不能为空", trigger: "blur" },
    {
      validator: validatorApplicableScope,
      trigger: "change",
    },
  ],
  leaveRequestingUnit: [{ required: true, message: "请假单位不能为空", trigger: "blur" }],
  durationCalculationMethod: [
    { required: true, message: "时长计算方式不能为空", trigger: "blur" },
  ],
});

// 表单提交
const submitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }
    let newDataForm = JSON.parse(JSON.stringify(dataForm));
    newDataForm.userIds = (newDataForm.userList || []).map((item) => {
      return item.value;
    });

    useHolidayRuleSubmitApi(newDataForm).then(() => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        },
      });
    });
  });
};

//#region 选择成员

const selectUserTreeRef = ref();

const onClickSelectUser = () => {
  let newUserList = JSON.parse(JSON.stringify(dataForm.userList));
  newUserList = newUserList.map((item) => {
    return { id: item.value, name: item.label };
  });
  selectUserTreeRef.value?.init(newUserList);
};

const getSelectUser = (val) => {
  console.log(val);
  let newUserList = JSON.parse(JSON.stringify(dataForm.userList));
  val.forEach((item) => {
    if (newUserList.findIndex((i) => i.value == item.id) == -1) {
      newUserList.push({
        value: item.id,
        label: item.name,
      });
    }
  });
  console.log(newUserList);
  dataForm.userList = newUserList;
};

const onClickCloseUser = () => {
  // dataForm.userList = []
};

//#endregion

defineExpose({
  init,
});
</script>
