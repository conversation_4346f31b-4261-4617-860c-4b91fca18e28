<!--
 * @Descripttion:
 * @Author: cuiyingchun
 * @Date: 2023-07-10 17:32:38
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2023-07-11 14:07:12
 * @Introduce::
-->
<template>
  <div class="layout-home">
    <el-alert type="warning" show-icon v-if="!userStore.isAuth" @close="onCloseAlert">
      <template #title>
        <div>
          完善企业信息，认证后可享受在线投标报价、企业服务等功能
          <span style="color: #409eff; cursor: pointer" @click="onClickAuth"
            >去认证 ></span
          >
        </div>
      </template>
    </el-alert>
    <el-dialog
      v-model="state.authVisible"
      title="提示"
      width="500"
      @close="onClickCloseAuth"
    >
      <div>
        尊敬的用户您好！为了您能够在本平台顺利开展交易业务，请先完成平台认证，谢谢合作！
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="onClickAuth()"> 立即认证 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>

  <!-- <el-card class="layout-home">
		<h2>首页</h2>
	</el-card> -->
</template>
<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { reactive } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
const userStore = useUserStore();

interface IState {
  authVisible: boolean;
}

const state = reactive<IState>({
  authVisible: !userStore.isAuthDialog,
});

const onClickAuth = () => {
  router.push("/member/info/index");
  onClickCloseAuth();
};

const onClickCloseAuth = () => {
  state.authVisible = false;
  userStore.setIsAuthDialog(true);
};

const onCloseAlert = () => {
  userStore.setIsAuth(true);
};
</script>

<style scoped>
.layout-home {
  line-height: 2;
  font-size: 15px;
}

.layout-home h2 {
  margin: 6px 0;
}
</style>
