<template>
  <el-card>
    <div class="project">
      <div class="project-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="项目编号"
              clearable
              v-model="state.queryForm.projectNo"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="项目名称"
              clearable
              v-model="state.queryForm.projectName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onResetSearch()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="project-action">
        <el-button
          type="primary"
          @click="addOrUpdateHandle(false)"
          v-auth="'purchase:project:save'"
        >
          新增
        </el-button>
      </div>
      <div class="project-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="projectNo"
            label="项目编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="projectName"
            label="项目名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="budget"
            label="预算总金额（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="creatName"
            label="创建人"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="250"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="onClickDetail(scope.row)"
                v-auth="'purchase:project:info'"
              >
                查看
              </el-button>
              <el-button
                type="primary"
                link
                @click="addOrUpdateHandle(true, scope.row.id)"
                v-if="scope.row.editStatus === 1"
                v-auth="'purchase:project:update'"
              >
                编辑
              </el-button>
              <el-button
                type="primary"
                link
                @click="deleteBatchHandle(scope.row.id)"
                v-if="scope.row.editStatus === 1"
                v-auth="'purchase:project:delete'"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="project-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <Detail
        :show="detail.show"
        :title="detail.title"
        :id="detail.id"
        @close="onCloseDetail"
      ></Detail>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive } from "vue";
import { useRouter } from "vue-router";
import Detail from "./components/Detail.vue";

const router = useRouter();

const state = reactive<IHooksOptions>({
  queryForm: {
    projectNo: "",
    projectName: "",
  },
  dataListUrl: "purchase/project/page",
  deleteUrl: "/purchase/project",
});
const { getDataList, sizeChangeHandle, currentChangeHandle, deleteBatchHandle } = useCrud(
  state
);

const addOrUpdateHandle = (isUpdate: boolean, id?: string) => {
  router.push({
    path: "/purchase/project/action",
    query: { id, type: isUpdate ? "update" : "add" },
  });
};

const onResetSearch = () => {
  state.queryForm.projectNo = "";
  state.queryForm.projectName = "";
  state.pageNo = 1;
  getDataList();
};

interface IDetail {
  show: boolean;
  title: string;
  id?: number;
}

const detail = reactive<IDetail>({
  show: false,
  title: "标题",
  id: void 0,
});

const onClickDetail = (row: any) => {
  detail.title = row.projectName;
  detail.id = row.id;
  detail.show = true;
};

const onCloseDetail = () => {
  detail.show = false;
  detail.title = "标题";
  detail.id = void 0;
};
</script>
<style lang="scss" scoped>
.project {
  &-list {
    margin-top: 16px;
  }
}
</style>
