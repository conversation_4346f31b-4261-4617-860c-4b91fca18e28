<template>
  <el-card>
    <div class="notice">
      <div class="notice-tabs">
        <el-tabs v-model="state.activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane label="待报价项目" name="first">
            <div class="notice-search">
              <el-form
                ref="elFormRef"
                :inline="true"
                :model="state.queryForm"
                @keyup.enter="getDataList()"
              >
                <el-form-item prop="packageNo">
                  <el-input
                    v-model="state.queryForm.packageNo"
                    placeholder="采购计划编号"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item prop="packageName">
                  <el-input
                    v-model="state.queryForm.packageName"
                    placeholder="采购计划名称"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item prop="time">
                  <el-date-picker
                    v-model="state.queryForm.time"
                    type="daterange"
                    value-format="YYYY-MM-DD"
                    range-separator="~"
                    start-placeholder="报价开始时间"
                    end-placeholder="报价截止时间"
                    @change="queryTime"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button @click="getDataList()" type="primary">查询</el-button>
                  <el-button @click="resetForm()">重置</el-button>
                </el-form-item>
              </el-form>
            </div>
            <div class="notice-list">
              <el-table v-loading="state.dataListLoading" :data="state.dataList" border>
                <el-table-column
                  type="index"
                  label="序号"
                  header-align="center"
                  align="center"
                  width="70"
                ></el-table-column>
                <el-table-column
                  prop="packageNo"
                  label="采购计划编号"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="packageName"
                  label="采购计划名称"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="title"
                  label="公告名称"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="companyName"
                  label="采购单位"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="bidStartDate"
                  label="首轮报价开始时间"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="bidEndDate"
                  label="首轮报价截止时间"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <fast-table-column
                  prop="quotationStatus"
                  label="首轮状态"
                  header-align="center"
                  align="center"
                  dict-type="quotation_status"
                ></fast-table-column>
                <el-table-column
                  label="操作"
                  fixed="right"
                  header-align="center"
                  align="center"
                  width="200"
                >
                  <template #default="scope">
                    <!-- 多轮 -->
                    <el-button
                      v-if="judgeStatus(scope.row) == '多轮'"
                      type="primary"
                      link
                      @click="mulofferHd(scope.row)"
                    >
                      多轮报价
                    </el-button>
                    <!-- 已撤销的，并且当前时间还在时间内的/末报价的，并且当前时间在当前时间内的 -->
                    <el-button
                      v-if="judgeStatus(scope.row) == '报价'"
                      type="primary"
                      link
                      @click="openOffer(scope.row)"
                    >
                      报价
                    </el-button>
                    <!-- 已报价的，并且当前时间还在时间内的 -->
                    <el-button
                      v-if="judgeStatus(scope.row) == '撤销'"
                      type="primary"
                      link
                      @click="cancelHd(scope.row.id)"
                    >
                      撤销报价
                    </el-button>
                    <el-button
                      v-if="scope.row.quotationStatus != 0"
                      type="primary"
                      link
                      @click="historyHd(scope.row)"
                    >
                      历史报价
                    </el-button>
                    <el-button
                      v-if="
                        scope.row.curRoundStatus == 1 &&
                        scope.row.showLastQuotation === 1 &&
                        scope.row.roundNo > 1
                      "
                      type="primary"
                      link
                      @click="onClickSupplierQuotation(scope.row)"
                    >
                      查看匿名报价
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="notice-page">
              <el-pagination
                :current-page="state.pageNo"
                :page-sizes="state.pageSizes"
                :page-size="state.pageSize"
                :total="state.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="sizeChangeHandle"
                @current-change="currentChangeHandle"
              >
              </el-pagination>
            </div>
          </el-tab-pane>
          <el-tab-pane label="已结束项目" name="second">
            <bidend ref="bidendRef"></bidend>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </el-card>
  <!-- 历史报价 -->
  <historyoffer ref="historyofferRef" v-if="state.historyofferShow"></historyoffer>
  <!-- 多轮报价 -->
  <multipleoffer ref="multipleofferRef" v-if="state.multipleofferShow"></multipleoffer>
  <!-- 查看一轮报价 -->
  <WatchSupplierQuotation
    :show="supplierQuotation.show"
    :packageId="supplierQuotation.packageId"
    :roundNo="supplierQuotation.roundNo"
    @close="supplierQuotation.show = false"
  ></WatchSupplierQuotation>
</template>
<script setup lang="ts">
import { reactive, ref, nextTick, onBeforeUnmount, onMounted } from "vue";
import { useRouter } from "vue-router";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { useCancelQuotationByIdApi } from "@/api/pnotice";
import bidend from "./bidend.vue";
import historyoffer from "./components/historyoffer.vue";
import multipleoffer from "./components/multipleoffer.vue";
import WatchSupplierQuotation from "./components/WatchSupplierQuotation.vue";
import { ElMessage, ElMessageBox } from "element-plus";

const state: IHooksOptions = reactive({
  dataListUrl: "/purchase/package/page4QuotationBid",
  queryForm: {
    packageName: "",
    packageNo: "",
    time: "",
    startTime: "",
    endTime: "",
  },
  viewVueShow: false,
  historyofferShow: false,
  multipleofferShow: false,
  activeName: "first",
});
const router = useRouter();
const historyofferRef = ref();
const multipleofferRef = ref();
const elFormRef = ref();
const bidendRef = ref();
const timer = ref();

// 判断操作
const judgeStatus = (row) => {
  if (
    row.bidderStatus != 0 &&
    row.roundNo == 1 &&
    row.curRoundStatus == 1 &&
    (row.quotationStatus == 0 || row.quotationStatus == 2)
  )
    return "报价";
  if (row.roundNo == 1 && row.curRoundStatus == 1 && row.quotationStatus == 1)
    return "撤销";
  if (row.bidderStatus != 0 && row.roundNo > 1) return "多轮";
};
const queryTime = (tt) => {
  if (tt instanceof Array) {
    state.queryForm.startTime = tt[0];
    state.queryForm.endTime = tt[1];
  } else {
    state.queryForm.startTime = state.queryForm.endTime = "";
  }
};
// 新增编辑
const openOffer = (row) => {
  if (row.id) {
    router.push({
      path: "/sppurchase/makeoffer/offer",
      query: {
        packageId: row.id,
        roundNum: row.roundNo,
        qstatus: row.quotationStatus,
      },
    });
  }
};
// 历史报价
const historyHd = (row) => {
  state.historyofferShow = true;
  nextTick(() => {
    historyofferRef.value.init(row);
  });
};
// 多轮报价
const mulofferHd = (row) => {
  state.multipleofferShow = true;
  nextTick(() => {
    multipleofferRef.value.init(row);
  });
};
// 撤销
const cancelHd = (id) => {
  ElMessageBox.confirm(
    "是否确认撤销当前报价，报价截止时间前可再次报价，报价时间截止后将无法报价。",
    "撤销报价",
    {
      confirmButtonText: "确认",
      cancelButtonText: "取消",
      type: "warning",
    }
  )
    .then(() => {
      useCancelQuotationByIdApi(id).then((res) => {
        if (res.code == 0) {
          ElMessage.success("撤销报价成功");
          getDataList();
        }
      });
    })
    .catch(() => {});
};
const resetForm = () => {
  elFormRef.value.resetFields();
  getDataList(1);
};
// tab点击
const handleClick = (tab, event) => {
  // console.log(tab.paneName)
  tab.paneName == "first" ? getDataList() : nextTick(() => bidendRef.value.getDataList());
};

// 轮询
const polling = () => {
  timer.value = setInterval(() => {
    getDataList();
  }, 1000 * 60);
};

onMounted(() => {
  polling();
});
onBeforeUnmount(() => {
  clearInterval(timer.value);
  console.log(timer.value);
});

const { getDataList, deleteBatchHandle, sizeChangeHandle, currentChangeHandle } = useCrud(
  state
);

//查看上一轮报价
const supplierQuotation = reactive({
  show: false,
  packageId: void 0,
  roundNo: 1,
});

const onClickSupplierQuotation = (row) => {
  supplierQuotation.show = true;
  supplierQuotation.packageId = row.id;
  supplierQuotation.roundNo = row.roundNo;
};
</script>
<style lang="scss" scoped>
.notice {
  &-list {
    margin-top: 16px;
  }
}
</style>
