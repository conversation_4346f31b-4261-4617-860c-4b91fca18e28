<template>
  <div class="detail-ctr" v-if="props.alldata.oaContractFlowInfoVO">
    <div class="detail-ctr-label">采购计划</div>
    <div class="detail-ctr-txt">
      {{
        props.alldata.oaContractFlowInfoVO.packageName
          ? props.alldata.oaContractFlowInfoVO.packageName
          : "-"
      }}
    </div>
    <div class="detail-ctr-label">合同名称</div>
    <div class="detail-ctr-txt">
      {{ props.alldata.oaContractFlowInfoVO.contractName }}
    </div>
    <div class="detail-ctr-label">合同编号</div>
    <div class="detail-ctr-txt">{{ props.alldata.oaContractFlowInfoVO.contractNo }}</div>
    <div class="detail-ctr-label">合同金额（元）</div>
    <div class="detail-ctr-txt">
      {{ props.alldata.oaContractFlowInfoVO.contractPrice }}
    </div>
    <div class="detail-ctr-label">开始时间</div>
    <div class="detail-ctr-txt">{{ props.alldata.oaContractFlowInfoVO.startTime }}</div>
    <div class="detail-ctr-label">结束时间</div>
    <div class="detail-ctr-txt">{{ props.alldata.oaContractFlowInfoVO.endTime }}</div>
    <div class="detail-ctr-label">用章类型</div>
    <div class="detail-ctr-txt">
      {{
        props.alldata.oaContractFlowInfoVO.signTypeName
          ? props.alldata.oaContractFlowInfoVO.signTypeName
          : "-"
      }}
    </div>
    <div class="detail-ctr-label">我方主体名称</div>
    <div class="detail-ctr-txt">
      {{ props.alldata.oaContractFlowInfoVO.mySubjectName }}
    </div>
    <div class="detail-ctr-label">我方负责人</div>
    <div class="detail-ctr-txt">
      {{
        props.alldata.oaContractFlowInfoVO.myResponsibilityName
          ? props.alldata.oaContractFlowInfoVO.myResponsibilityName
          : "-"
      }}
    </div>
    <div class="detail-ctr-label">对方主体名称</div>
    <div class="detail-ctr-txt">
      {{ props.alldata.oaContractFlowInfoVO.youSubjectName }}
    </div>
    <div class="detail-ctr-label">对方负责人</div>
    <div class="detail-ctr-txt">
      {{
        props.alldata.oaContractFlowInfoVO.youResponsibilityName
          ? props.alldata.oaContractFlowInfoVO.youResponsibilityName
          : "-"
      }}
    </div>
    <div class="detail-ctr-label">备注</div>
    <div class="detail-ctr-txt">
      {{
        props.alldata.oaContractFlowInfoVO.flowRemark
          ? props.alldata.oaContractFlowInfoVO.flowRemark
          : "-"
      }}
    </div>
    <div class="detail-ctr-label">合同附件</div>
    <div class="detail-ctr-txt">
      <el-link
        class="file"
        :underline="false"
        type="primary"
        v-for="item in props.alldata.oaContractFlowInfoVO.attachmentList"
        @click="downloadFile(item.url, item.name)"
        >{{ item.name }}</el-link
      >
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { IView } from "../types";
import { downloadFile } from "@/utils/tool";
interface IProps {
  id: number;
  alldata: IView;
}
const props = withDefaults(defineProps<IProps>(), {
  id: 0,
  alldata: {
    oaContractFlowInfoVO: {
      id: void 0,
      contractName: "",
      contractNo: "",
      mySubjectName: "",
      myResponsibilityName: "",
      youSubjectName: "",
      youResponsibilityName: "",
      signTime: "",
      attachmentList: [],
      flowRemark: "",
    },
  },
});
</script>

<style scoped lang="scss">
.detail-ctr {
  &-label {
    color: #a2a2a2;
    padding: 0 12px 0 0;
    margin: 0 0 8px 0;
    line-height: 22px;
  }
  &-txt {
    margin-bottom: 15px;
    .file {
      display: block;
      line-height: 1.8;
    }
  }
}
</style>
