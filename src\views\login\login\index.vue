<template>
  <div class="login">
    <div class="login-header">
      <Header title="用户登录"></Header>
    </div>
    <div class="login-body">
      <div class="login-body-content">
        <div class="content-form">
          <div class="content-form-title">欢迎登录</div>
          <div class="content-form-main">
            <Account></Account>
          </div>
          <div class="content-form-register">
            <el-button type="primary" link @click="caUpload">数字证书驱动下载</el-button>
            <el-button type="primary" link @click="onSkipRegister">立即注册</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="login-footer">
      <Footer></Footer>
    </div>
  </div>
</template>
<script setup lang="ts">
import Header from "../components/Header.vue";
import Footer from "../components/Footer.vue";
import Account from "../account.vue";
import { useRouter } from "vue-router";

const router = useRouter();

const onSkipRegister = () => {
  router.push("/register");
};
const caUpload = () => {
  window.open('https://hbzxqz.oss-cn-zhangjiakou.aliyuncs.com/ca/%E6%95%B0%E5%AD%97%E8%AF%81%E4%B9%A6%E9%A9%B1%E5%8A%A8.zip')
};
</script>
<style lang="scss" scoped>
.login {
  height: 100vh;
  overflow-y: auto;
  background-color: #f6f8fa;
  &-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 2;
  }
  &-body {
    margin-top: 68px;
    &-content {
      background-image: url("@/assets/image/login/login-bg.jpg");
      height: 600px;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      .content-form {
        width: 400px;
        height: 420px;
        background-color: #ffffff;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        &-title {
          font-family: AlibabaPuHuiTi;
          font-size: 24px;
          font-variation-settings: "opsz" auto;
          font-feature-settings: "kern" on;
          color: #2a2a2a;
          padding-top: 30px;
        }
        &-main {
          width: 340px;
          margin-top: 40px;
        }
        &-register {
          width: 340px;
          display: flex;
          justify-content: flex-end;
          cursor: pointer;
          font-family: AlibabaPuHuiTi;
          font-size: 14px;
          font-variation-settings: "opsz" auto;
          font-feature-settings: "kern" on;
          color: #409eff;
        }
      }
    }
  }
  &-footer {
    margin: 60px 0px;
  }
}

@media only screen and (max-width: 992px) {
  .login-body-content {
    justify-content: center;
  }
}

@media screen and (max-width: 1200px) {
  .login-body-content {
    .content-form {
      margin-right: 100px;
    }
  }
}

@media screen and (min-width: 1201px) and (max-width: 1559px) {
  .login-body-content {
    .content-form {
      margin-right: 200px;
    }
  }
}

@media screen and (min-width: 1560px) and (max-width: 1795px) {
  .login-body-content {
    .content-form {
      margin-right: 320px;
    }
  }
}

@media screen and (min-width: 1796px) {
  .login-body-content {
    .content-form {
      margin-right: 320px;
    }
  }
}
</style>
