<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-02 16:25:21
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-16 08:44:41
-->
<template>
  <el-drawer
    v-model="state.visible"
    title="查看中标服务费"
    size="60%"
    :close-on-click-modal="false"
  >
    <div class="action">
      <el-form
        ref="infoRef"
        :model="state.dataForm"
        label-width="150px"
        class="normalform"
      >
        <ContentTitle title="项目信息" class="title"></ContentTitle>
        <el-form-item label="采购计划名称">
          <span>{{ state.dataForm.packageName }}</span>
        </el-form-item>
        <el-form-item label="采购计划编号">
          <span>{{ state.dataForm.packageNo }}</span>
        </el-form-item>
        <el-form-item label="中标服务费金额(元)">
          <span>{{ state.dataForm.winPrice }}</span>
        </el-form-item>
        <ContentTitle title="供应商信息" class="title"></ContentTitle>
        <el-form-item label="供应商">
          <span>{{ state.dataForm.winBidder }}</span>
        </el-form-item>
        <el-form-item label="联系人">
          <span>{{ state.dataForm.contactName }}</span>
        </el-form-item>
        <el-form-item label="联系电话">
          <span>{{ state.dataForm.contactPhone }}</span>
        </el-form-item>
        <ContentTitle title="支付信息" class="title"></ContentTitle>
        <el-form-item label="支付方式">
          <span> {{ state.dataForm.packageTypeLabel }} </span>
          <!-- <span v-if="state.dataForm.payType == 'AlipaySaoma'">支付宝支付</span>
          <span v-if="state.dataForm.MicroNative == 'AlipaySaoma'">微信支付</span> -->
        </el-form-item>
        <el-form-item label="发票类型">
          <span v-if="state.dataForm.headerId && !state.dataForm.addressld">普票</span>
          <span v-if="state.dataForm.headerId && state.dataForm.addressld">专票</span>
          <span v-if="!state.dataForm.headerId && !state.dataForm.addressld">否</span>
        </el-form-item>
        <el-form-item label="发票信息" v-if="state.invoiceHeader">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="发票抬头">{{
              state.invoiceHeader.invoiceHeader
            }}</el-descriptions-item>
            <el-descriptions-item label="纳税人识别号">{{
              state.invoiceHeader.creditCode
            }}</el-descriptions-item>
            <el-descriptions-item label="电子邮箱">{{
              state.invoiceHeader.email
            }}</el-descriptions-item>
            <el-descriptions-item label="电话">{{
              state.invoiceHeader.registerPhone
            }}</el-descriptions-item>
            <el-descriptions-item label="注册场所地址">{{
              state.invoiceHeader.registerAddress
            }}</el-descriptions-item>
            <el-descriptions-item label="开户行">{{
              state.invoiceHeader.bankDeposit
            }}</el-descriptions-item>
            <el-descriptions-item label="开户账号">{{
              state.invoiceHeader.basicAccount
            }}</el-descriptions-item>
          </el-descriptions>
        </el-form-item>
        <el-form-item label="邮寄地址" v-if="state.defaultAddress">
          <el-descriptions :column="2" border>
            <el-descriptions-item label="收件人">{{
              state.defaultAddress.recipient
            }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{
              state.defaultAddress.telePhone
            }}</el-descriptions-item>
            <el-descriptions-item label="收件地址">{{
              state.defaultAddress.address
            }}</el-descriptions-item>
            <el-descriptions-item label="邮政编码">{{
              state.defaultAddress.postalCode
            }}</el-descriptions-item>
            <el-descriptions-item label="电子邮箱">{{
              state.defaultAddress.email
            }}</el-descriptions-item>
          </el-descriptions>
        </el-form-item>
        <el-form-item label="备注">
          <span>{{ state.dataForm.notes }}</span>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button class="btn" @click="state.visible = false">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import { reactive } from "vue";
import {
  useOrderGetPayGoodsApi,
  useGetWinBidderInfoApi,
  useInvoiceHeaderByIdApi,
  useGetMailingAddressApi,
} from "@/api/pnotice";
import { ElMessage } from "element-plus";

const state = reactive({
  visible: false,
  dataForm: {},
  nowId: "",
  invoiceHeader: null,
  defaultAddress: null,
});

const init = (id) => {
  state.visible = true;
  state.nowId = id;
  getWinInfo();
  getOrderInfo();
};

const getWinInfo = () => {
  useGetWinBidderInfoApi(state.nowId).then((res) => {
    if (res.code == 0) {
      Object.assign(state.dataForm, res.data);
      if (res.data.headerld) getHeader(res.data.headerld);
      if (res.data.addressld) getAddressInfo(res.data.addressld);
    }
  });
};
const getOrderInfo = () => {
  useOrderGetPayGoodsApi(state.nowId).then((res) => {
    if (res.code == 0) {
      Object.assign(state.dataForm, res.data);
    }
  });
};
const getHeader = (id: number) => {
  useInvoiceHeaderByIdApi(id).then((res) => {
    Object.assign(dataForm.invoiceHeader, res.data);
  });
};
const getAddressInfo = (id: number) => {
  useGetMailingAddressApi(id).then((res) => {
    Object.assign(dataForm.defaultAddress, res.data);
  });
};

defineExpose({
  init,
});
</script>
<style lang="scss" scoped>
.title {
  margin-bottom: 15px;
}
.block {
  display: block;
}
.btn {
  margin-left: 12px;
}
.action_base {
  margin-bottom: 20px;
}
</style>
