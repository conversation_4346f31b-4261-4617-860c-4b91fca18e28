<template>
  <el-drawer
    v-model="state.show"
    :title="state.dataForm.packageName"
    :size="850"
    class="picketage_enter_detail_drawer"
    @close="onClickClose"
  >
    <el-card>
      <div class="detail">
        <el-form
          ref="dataFormRef"
          :model="state.dataForm"
          label-width="140px"
          class="detail-form"
        >
          <el-form-item prop="packageName" label="采购计划">
            <div class="detail-form-value">
              {{ state.dataForm.packageName }}
            </div>
          </el-form-item>
          <el-form-item prop="winBidder" label="中标单位">
            <div class="detail-form-value">
              {{ state.dataForm.winBidder }}
            </div>
          </el-form-item>
          <el-form-item prop="contactName" label="中标单位联系人">
            <div class="detail-form-value">
              {{ state.dataForm.contactName }}
            </div>
          </el-form-item>
          <el-form-item prop="contactPhone" label="中标单位联系电话">
            <div class="detail-form-value">
              {{ state.dataForm.contactPhone }}
            </div>
          </el-form-item>
          <el-form-item prop="winPrice" label="中标金额（元）">
            <div class="detail-form-value">
              {{ state.dataForm.winPrice }}
            </div>
          </el-form-item>
          <el-form-item prop="serviceCharge" label="合同额">
            <div class="detail-form-value">
              {{ state.dataForm.serviceCharge }}
            </div>
          </el-form-item>
          <el-form-item prop="notes" label="备注">
            <div class="detail-form-value">
              {{ state.dataForm.notes }}
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </el-drawer>
</template>
<script setup lang="ts">
import { getBidderInfo } from "@/api/picketage/enter";
import { reactive, watch } from "vue";
interface IProps {
  id?: number;
  show: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
});

interface IDataForm {
  packageName: string;
  winBidder: string;
  contactName: string;
  contactPhone: string;
  winPrice: string;
  serviceCharge: string;
  notes: string;
}

interface IState {
  show: boolean;
  dataForm: IDataForm;
}

const state = reactive<IState>({
  show: false,
  dataForm: {
    packageName: "",
    winBidder: "",
    contactName: "",
    contactPhone: "",
    winPrice: "",
    serviceCharge: "",
    notes: "",
  },
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      state.show = true;
      if (props.id) {
        GetBidderInfo(props.id);
      }
    }
  }
);

const GetBidderInfo = (id: number) => {
  getBidderInfo(id).then((res: any) => {
    if (res.code === 0) {
      state.dataForm.packageName = res.data.packageName;
      state.dataForm.winBidder = res.data.winBidder;
      state.dataForm.contactName = res.data.contactName;
      state.dataForm.contactPhone = res.data.contactPhone;
      state.dataForm.winPrice = res.data.winPrice;
      state.dataForm.serviceCharge = res.data.serviceCharge;
      state.dataForm.notes = res.data.notes;
    }
  });
};

const emit = defineEmits<{
  (e: "on-close"): void;
}>();

const onClickClose = () => {
  state.show = false;
  emit("on-close");
};
</script>

<style lang="scss">
.picketage_enter_detail_drawer {
  .el-drawer__body {
    padding: 14px !important;
    background: #f5f6fa !important;
  }
}
</style>
<style lang="scss" scoped>
.detail {
  &-form {
    &-value {
      color: #000;
    }
  }
}
</style>
