<template>
  <el-drawer
    v-model="state.show"
    title="中标结果公示"
    :size="850"
    class="picketage_enter_detail_drawer"
    @close="onClickClose"
  >
    <el-card>
      <div class="detail">
        <el-form
          ref="dataFormRef"
          :model="state.dataForm"
          label-width="140px"
          class="detail-form"
        >
          <el-form-item prop="packageNo" label="采购计划编号">
            <div class="detail-form-value">
              {{ state.dataForm.packageNo }}
            </div>
          </el-form-item>
          <el-form-item prop="packageName" label="采购计划名称">
            <div class="detail-form-value">
              {{ state.dataForm.packageName }}
            </div>
          </el-form-item>
          <el-form-item prop="title" label="公告标题">
            <div class="detail-form-value">
              {{ state.dataForm.title }}
            </div>
          </el-form-item>
          <el-form-item prop="winBidder" label="中标人">
            <div class="detail-form-value">
              {{ state.dataForm.winBidder }}
            </div>
          </el-form-item>
          <el-form-item
            prop="content"
            label="公示内容"
            v-if="state.dataForm.makeType == '1'"
          >
            <div class="detail-form-value">
              <WangEditor v-model="state.dataForm.content" :disabled="true"></WangEditor>
            </div>
          </el-form-item>
          <el-form-item prop="contactPhone" label="pdf公告文件" v-else>
            <div class="detail-form-list">
              <div class="detail-form-list-item" v-for="item in state.dataForm.fileList">
                <div class="detail-form-list-item-text">
                  {{ item.name }}
                </div>
                <div class="detail-form-list-item-action">
                  <el-icon class="action-icon" @click="onClickDownload(item)"
                    ><Download
                  /></el-icon>
                </div>
              </div>
            </div>
          </el-form-item>
          <el-form-item prop="contactPhone" label="附件">
            <div class="detail-form-list">
              <div
                class="detail-form-list-item"
                v-for="item in state.dataForm.attachmentList"
              >
                <div class="detail-form-list-item-text">
                  {{ item.name }}
                </div>
                <div class="detail-form-list-item-action">
                  <el-icon class="action-icon" @click="onClickDownload(item)"
                    ><Download
                  /></el-icon>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </el-drawer>
</template>
<script setup lang="ts">
import { reactive, watch } from "vue";
import WangEditor from "@/components/wang-editor/index.vue";
import service from "@/utils/request";
import { getAnnouncementInfo } from "@/api/picketage/announcement";
import { Download } from "@element-plus/icons-vue";

interface IProps {
  id?: number;
  show: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
});

interface IAttachmentItem {
  name: string;
  url: string;
  platform: string;
  size: number;
}

interface IDataForm {
  packageNo: string;
  packageName: string;
  title: string;
  winBidder: string;
  makeType: string;
  content: string;
  fileList: IAttachmentItem[];
  attachmentList: IAttachmentItem[];
}

interface IState {
  show: boolean;
  dataForm: IDataForm;
}

const state = reactive<IState>({
  show: false,
  dataForm: {
    packageNo: "",
    packageName: "",
    title: "",
    winBidder: "",
    makeType: "1",
    content: "",
    fileList: [],
    attachmentList: [],
  },
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      state.show = true;
      if (props.id) {
        GetAnnouncementInfo(props.id);
      }
    }
  }
);

const GetAnnouncementInfo = (id: number) => {
  getAnnouncementInfo(id).then((res: any) => {
    if (res.code === 0) {
      state.dataForm.packageNo = res.data.packageNo;
      state.dataForm.packageName = res.data.packageName;
      state.dataForm.title = res.data.title;
      state.dataForm.makeType = res.data.makeType;
      state.dataForm.winBidder = res.data.winBidder;
      state.dataForm.content = res.data.content;
      if (res.data.makeType == "0" && (res.data.contentAttach?.url ?? "") !== "") {
        let newAttachmentItem = {
          name: res.data.contentAttach.name,
          url: res.data.contentAttach.url,
          platform: res.data.contentAttach.platform,
          size: res.data.contentAttach.size,
        };
        state.dataForm.fileList = [newAttachmentItem];
      }
      state.dataForm.attachmentList = res.data.bulletinAttachs ?? [];
    }
  });
};

const emit = defineEmits<{
  (e: "on-close"): void;
}>();

const onClickClose = () => {
  state.show = false;
  emit("on-close");
};

const onClickDownload = async (item: IAttachmentItem) => {
  const response = await service.get(item.url, {
    responseType: "blob", // important
  });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", item.name);
  document.body.appendChild(link);
  link.click();
};
</script>

<style lang="scss">
.picketage_enter_detail_drawer {
  .el-drawer__body {
    padding: 14px !important;
    background: #f5f6fa !important;
  }
}
</style>
<style lang="scss" scoped>
.detail {
  &-form {
    &-value {
      color: #000;
    }
    &-list {
      &-item {
        display: flex;
        align-items: center;
        &-text {
          color: #409eff;
          cursor: pointer;
        }
        &-action {
          color: #545252;
          cursor: pointer;
          .action-icon {
            margin-left: 4px;
          }
        }
      }
    }
  }
}
</style>
