<template>
  <el-drawer v-model="visible" size="1000px" destroy-on-close title="查看" @close="setVisible()">
    <div class="option">
      <el-button
        v-if="props.pageSource==='myCustomer'"
        type="primary"
        @click="addOrUpdateHandle(dataForm)"
        v-auth="'work:customer:update'"
      >
      编辑客户信息
      </el-button>
      <el-button v-if="props.pageSource==='myCustomer'" type="success" @click="transferHandle()" v-auth="'work:customer:transferHeader'">转移给他人跟进</el-button>
      <el-button v-if="props.pageSource==='subordinateCustomer'" type="success" @click="transferHandle()" v-auth="'work:jgcustomer:transferHeader'">转移给他人跟进</el-button>
    </div>
    <div class="header">
      <div class="header-top">
        <img class="header-img" src="@/assets/image/customer/head.png" alt="">
        <span class="header-name">{{dataForm.customerName}}</span>
      </div>
      <div class="header-line"></div>
      <div class="header-bottom">
        <div class="header-li">
          <div class="header-li-label">负责人</div>
          <div class="header-li-value">{{ dataForm.headerName }}</div>
        </div>
        <div class="header-li">
          <div class="header-li-label">所属部门</div>
          <div class="header-li-value">{{ dataForm.orgName }}</div>
        </div>
        <div class="header-li">
          <div class="header-li-label">最新跟进时间</div>
          <div class="header-li-value">{{ dataForm.latestFollowTime?dataForm.latestFollowTime:'-' }}</div>
        </div>
        <div class="header-li">
          <div class="header-li-label">下次跟进时间</div>
          <div class="header-li-value">{{ dataForm.nextFollowTime?dataForm.nextFollowTime:'-' }}</div>
        </div>
      </div>
    </div>
    <div class="flexdiv info-content">
      <el-tabs
        class="flexdiv-left"
        type="border-card"
        v-model="activeName"
        style="flex: 6"
        @tab-change="tabChange"
      >
        <el-tab-pane name="1" label="客户信息">
          <el-form
            label-width="120px"
            ref="dataFormRef"
            :model="dataForm"
            :rules="dataRules"
          >
            <div class="bartitle">基础信息</div>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="客户名称：" prop="customerName">
                  <div>{{ dataForm.customerName }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户类型：" prop="customerType">
                  <div>
                    {{
                      getDictLabel(
                        appStore.dictList,
                        "customer_type",
                        dataForm.customerType
                      )
                    }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户标签：" prop="customerTags">
                  <div>
                    {{
                      getDictLabel(
                        appStore.dictList,
                        "customer_tags",
                        dataForm.customerTags
                      )
                    }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户来源：" prop="customer_source">
                  <div>
                    {{
                      getDictLabel(
                        appStore.dictList,
                        "customer_type",
                        dataForm.customerSource
                      )
                    }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户地址：" prop="address">
                  <div>{{ dataForm.address }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="下次跟进时间：" prop="nextFollowTime">
                  <div>{{ dataForm.nextFollowTime?dataForm.nextFollowTime:'-' }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="备注：" prop="remark">
                  <div>{{ dataForm.remark }}</div>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="bartitle">联系人</div>
            <el-row :gutter="24">
              <el-col :span="12">
                <el-form-item label="联系人：" prop="linkMan">
                  <div>{{ dataForm.linkMan }}</div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话：" prop="linkPhone">
                  <div>{{ dataForm.linkPhone }}</div>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane name="2" label="客户历史记录">
          <el-empty
            v-if="historyList.length === 0"
            :image-size="100"
            description="暂无数据"
          />
          <el-timeline class="log">
            <el-timeline-item
              placement="top"
              v-for="(item, index) in historyList"
              :key="index"
              :timestamp="item.createTime"
            >
              <div class="username">{{ item.realName }}</div>
              <div class="content">{{ item.operation }}</div>
              <div class="reason" v-if="item.reason">{{ item.reason }}</div>
            </el-timeline-item>
          </el-timeline>
        </el-tab-pane>
        <el-tab-pane name="3" label="跟进动态" class="follow">
          <el-form :inline="true" :model="state.queryForm">
            <el-form-item>
              <fast-select
                style="width: 224px"
                clearable
                @update:modelValue="updateValue"
                v-model="state.queryForm.followType"
                dict-type="follow_type"
                placeholder="跟进类型"
              ></fast-select>
            </el-form-item>
            <el-form-item>
              <el-date-picker
                style="width: 367px"
                @change="changeDateRange"     v-model="state.rangeTime"
                start-placeholder="开始时间"
                range-separator="至"
                end-placeholder="结束时间"
                type="daterange"
                value-format="YYYY-MM-DD"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getDataListFollow()">查询</el-button>
            </el-form-item>
            <el-form-item>
              <el-button @click="onClickResetFollow()">重置</el-button>
            </el-form-item>
            <el-form-item>
              <el-button
                v-if="props.pageSource==='myCustomer'"
                :icon="Plus"
                type="success"
                @click="followUpHandle"
                v-auth="'work:customerFollowRecord:save'"
                >写跟进</el-button
              >
            </el-form-item>
          </el-form>
          <el-empty
            v-if="followList.length === 0"
            :image-size="100"
            description="暂无数据"
          />
          <div v-for="(item, index) in followList" :key="index">
            <div class="follow-date">
              <img class="follow-date-icon" src="@/assets/image/customer/date.png" alt="">
              <span class="follow-date-text">{{ index }}</span>
            </div>
            <div class="header-line"></div>
            <el-timeline>
              <el-timeline-item v-for="data in Object.values(item)" :key="data.id">
                <div class="follow-time">
                  <span class="follow-time-text">{{data.followTime?data.followTime:'-'}}</span>
                  <span class="timename">
                    <span class="follow-time-text">
                      负责人：
                    </span>{{ data.followPerson }}</span
                  >
                </div>
                <div class="follow-content">
                  <div class="timelinediv">
                    <span class="timename"> 跟进类型：</span
                    >{{ getDictLabel(appStore.dictList, "follow_type", data.followType) }}
                  </div>
                  <div class="timelinediv">
                    <span class="timename"> 跟进状态：</span
                    >{{
                      getDictLabel(appStore.dictList, "follow_status", data.followStatus)
                    }}
                  </div>
                  <div class="timelinediv">
                    <span class="timename"> 跟进内容：</span>{{ data.followContent }}
                  </div>
                  <div
                    class="timelinediv"
                    style="margin-bottom: -10px"
                    v-if="data.attachmentList && data.attachmentList.length"
                  >
                    <el-image
                      style="
                        width: 60px;
                        height: 60px;
                        margin-right: 6px;
                        margin-bottom: 6px;
                      "
                      :src="item.url"
                      :preview-src-list="[item.url]"
                      v-for="item in data.attachmentList"
                    ></el-image>
                  </div>
                  <div class="timelinediv">
                    <span class="timename"> 下次跟进时间：</span
                    ><span style="color: #ff5757">{{data.nextFollowTime?data.nextFollowTime:'-'}}</span>
                  </div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-tab-pane>
      </el-tabs>
      <follow-up ref="followUpRef" @refresh-data-list="updateDetailList()"></follow-up>
      <transfer-dialog
        ref="transferRef"
        @refresh-data-list="doTransfer"
        :idList="[{ id: dataForm.id }]"
        @set-visible="setVisible"
        fromPage="detail"
      ></transfer-dialog>
    </div>
    <template #footer>
      <el-button type="primary" @click="setVisible()">返回</el-button>
    </template>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refresh-data-list="doEdit"></add-or-update>
  </el-drawer>
</template>

<script setup lang="ts">
import { Plus } from "@element-plus/icons-vue";
import followUp from "./follow-up.vue";
import transferDialog from "./transfer-dialog.vue";
import AddOrUpdate from "./add-or-update.vue";
const emit = defineEmits(["refreshDataList"]);
// 字典渲染
import { getDictLabel } from "@/utils/tool";
import { useAppStore } from "@/store/modules/app";
const appStore = useAppStore();
import {
  customerInfoApi,
  getCustomerFollowRecordApi,
  findCustomerLogApi
} from "@/api/customerManage/common";
import { reactive, ref } from "vue";
const dataFormRef = ref();
const activeName = ref("1");
const visible = ref(false);
const dataForm = reactive({});
const followList = ref([]);
const historyList = ref([]);
const dataRules = ref({
  customerName: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  linkMan: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  linkPhone: [
    { required: true, message: "必填项不能为空", trigger: "blur" }
  ],
});
const props=defineProps({
  pageSource:{
    default:'',
    type:String
  }
})
const state = reactive({
  dataListUrl: "/sys/user/page",
  deleteUrl: "/sys/user",
  queryForm: {
    customerId: "",
    followType: "",
    startDate: "",
    endDate: "",
  },
  rangeTime: [],
  id: null,
});
const addOrUpdateRef = ref();
const addOrUpdateHandle = (row) => {
  addOrUpdateRef.value.init(row);
};
const init = (data, isSeaVal) => {
  let start=""
  let end=""
  state.queryForm.followType = "";
  state.queryForm.startDate = start; // 获取当前日期
  state.queryForm.endDate = end; // 获取最近7天的日期
  state.rangeTime = [start, end];
  activeName.value="1"
  visible.value = true;
  state.id = data.id;
  getCustomerInfo();
};

// 获取信息
const getCustomerInfo = () => {
  customerInfoApi(state.id).then((res) => {
    let resData = res.data;
    for (const key in resData) {
      if (!resData[key]) {
        resData[key] = "-";
      }
    }
    Object.assign(dataForm, resData);
  });
};

//获取跟进记录
const getCustomerFollowRecord = () => {
  state.queryForm.customerId = state.id ? state.id : dataForm.id;
  if(!state.queryForm.followType){
    state.queryForm.followType = "";
  }
  getCustomerFollowRecordApi(state.queryForm).then((res) => {
    followList.value = res.data ?? [];
  });
};
const updateValue = (val) => {
  if(val){
    state.queryForm.followType = val
  }else{
    state.queryForm.followType = ""
  }
};

const transferRef = ref();
const transferHandle = () => {
  transferRef.value.init();
};

const followUpRef = ref();
const followUpHandle = () => {
  followUpRef.value.init(dataForm.id);
};

const doTransfer = () => {
  visible.value = false;
  emit("refreshDataList");
};
const doEdit = (row) => {
  getCustomerInfo();
  emit("refreshDataList");
};

const getDataListFollow=()=>{
  getCustomerFollowRecord();
}

const onClickResetFollow=()=>{
  state.queryForm.followType = "";
  state.rangeTime = [];
  state.queryForm.startDate = "";
  state.queryForm.endDate = "";
  getCustomerFollowRecord();
}

const changeDateRange = (val) => {
  if (!val) {
    state.queryForm.startDate = "";
    state.queryForm.endDate = "";
  } else {
    state.queryForm.startDate = val[0];
    state.queryForm.endDate = val[1];
  }
};

const updateDetailList = () => {
  getCustomerInfo();
  getCustomerFollowRecord();
  emit("refreshDataList");
};

const setVisible = () => {
  visible.value = false;
};

const tabChange=((val)=>{
  if(val==="2"){
    findCustomerLog()
  }else if(val==="3"){
    getCustomerFollowRecord()
  }
})

const findCustomerLog=(()=>{
  findCustomerLogApi(state.id).then((res) => {
    historyList.value=res.data
  })
})


defineExpose({
  init,
  setVisible,
});
</script>
<style scoped lang="scss">
.option{
  text-align: right;
  margin-bottom: 20px;
}
.bartitle {
  height: 15px;
  line-height: 15px;
  position: relative;
  padding-left: 10px;
  margin-bottom: 10px;
}

.bartitle::before {
  content: "";
  width: 5px;
  height: 15px;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #409EFF;
}
.header{
  background: url("@/assets/image/customer/detail_bac.png") 0 0 no-repeat;
  background-size: 100% 100%;
  padding: 21px 24px;
  .header-top{
    display: flex;
    .header-img{
      width:46px;
      height:46px;
      margin-right: 13px;
    }
    .header-name{
      line-height:46px;
      font-size:16px;
      color:#262626;
    }
  }
  .header-bottom{
    display: flex;
  }
  .header-line{
    height: 1px;
    background: #E5E5E5;
    margin:16px 0;
  }
  .header-li{
    text-align: center;
    width:calc((100% - 60px) / 4);
    .header-li-label{
      color:#666;
    }
    .header-li-value{
      color:#333;
      margin-top: 15px;
    }
  }
}
.info-content{
  margin-top: 20px;
}
.log{
  .username{
    color:#1E2639;
  }
  .content{
    color:#666666;
    margin-top:8px;
  }
  .reason{
    background: #FFF0F0;
    border-radius: 4px 4px 4px 4px;
    padding:5px 22px;
    color:#FA5151;
    margin-top:8px;
    display: inline-block;
  }
}
.follow{
  .follow-date{
    display: flex;
    align-items: center;
    .follow-date-icon{
      width:22px;
      height:22px;
      margin-right:10px;
    }
    .follow-date-text{
      font-weight: 700;
    }
  }
  .header-line{
    height: 1px;
    background: #E5E5E5;
    margin:8px 0 30px;
  }
  .follow-time{
    display: flex;
    justify-content: space-between;
  }
  .follow-time-text{
    color:#999;
  }
  .follow-content{
    padding:15px 23px;
    border-radius: 4px;
    background: #EEF6FF;
    margin-top:10px;
    .timelinediv{
      margin-top: 10px;
    }
    .timelinediv:first-child{
      margin-top: 0;
    }
    .timename{
      color:#6C727E;
    }
  }
}
</style>
