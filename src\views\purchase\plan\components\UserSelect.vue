<template>
  <el-dialog
    v-model="dialog.show"
    :title="props.title"
    :close-on-click-modal="false"
    draggable
    width="1000px"
    @close="onClose"
  >
    <div class="user">
      <div class="user-left">
        <el-tree
          ref="treeRef"
          :data="dialog.orgList"
          :expand-on-click-node="false"
          :props="{ label: 'name', children: 'children' }"
          highlight-current
          node-key="id"
          @node-click="onSelectCategory"
        />
      </div>
      <div class="user-right">
        <div class="material-right-table">
          <el-table show-overflow-tooltip :data="dialog.dataList" border>
            <el-table-column
              prop="username"
              label="用户名"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="realName"
              label="姓名"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="mobile"
              label="手机号"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="orgName"
              label="所属机构"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="action"
              label="操作"
              header-align="center"
              align="center"
            >
              <template #default="scope">
                <el-button type="primary" link @click="onClickSelect(scope.row)"
                  >选择</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, onMounted, watch } from "vue";
import { useOrgTree } from "@/api/sys/orgs";

interface IProps {
  show: boolean;
  title: string;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  title: "选择项目经理",
});

interface IUserItem {
  id: number;
  username: string;
  realName: string;
  orgId: number;
  orgName: string;
  mobile: string;
}

interface IOrgItem {
  id: number;
  name: string;
  userVOS: IUserItem[];
  children?: IOrgItem[];
}

interface IDialog {
  show: boolean;
  orgList: IOrgItem[];
  dataList: IUserItem[];
}

const dialog = reactive<IDialog>({
  show: false,
  orgList: [],
  dataList: [],
});

const onSelectCategory = (selectCategory: IOrgItem) => {
  dialog.dataList = selectCategory.userVOS;
};

const emit = defineEmits<{
  (e: "on-close"): void;
  (e: "on-select", row: IUserItem): void;
}>();

const onClose = () => {
  dialog.show = false;
  emit("on-close");
};

const onClickSelect = (row: IUserItem) => {
  dialog.show = false;
  emit("on-select", row);
};

onMounted(() => {
  useOrgTree().then((res: any) => {
    if (res.code === 0) {
      dialog.orgList = computeTree(res.data);
    }
  });
});

const computeTree = (orgList: IOrgItem[]) => {
  let newOrgList: IOrgItem[] = orgList.map((item: IOrgItem) => {
    return {
      id: item.id,
      name: item.name,
      userVOS: item.userVOS.map((ele: IUserItem) => {
        return {
          id: ele.id,
          username: ele.username,
          realName: ele.realName,
          orgId: ele.orgId,
          orgName: ele.orgName,
          mobile: ele.mobile,
        };
      }),
      children: computeTree(item.children ?? []),
    };
  });
  return newOrgList;
};

watch(
  () => props.show,
  () => {
    if (props.show) {
      dialog.show = true;
    }
  }
);
</script>
<style lang="scss" scoped>
.user {
  display: flex;
  overflow-x: auto;
  &-left {
    width: 250px;
    height: 450px;
    border: 1px solid #d7d7d7;
    padding: 10px 0;
    flex-shrink: 0;
    overflow-y: auto;
  }
  &-right {
    flex: 1;
    margin-left: 20px;
  }
}
</style>
