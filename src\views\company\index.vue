<template>
  <el-card>
    <div class="company">
      <div class="company-title">
        <div class="company-title-text">
          <div class="company-title-text-divider"></div>
          <div class="company-title-text-title">企业信息</div>
        </div>
        <div class="company-title-action" v-if="!isEdit">
          <el-button type="primary" @click="onClickEdit">编辑</el-button>
        </div>
      </div>
      <div class="company-divider"></div>
      <div class="company-logo">
        <el-upload
          :headers="{ Authorization: cache.getToken() }"
          :action="constant.uploadUrl"
          :before-upload="beforeUpload"
          :on-success="handleSuccess"
          :show-file-list="false"
          :disabled="!isEdit"
        >
          <div v-if="(dataForm.companyImg ?? '') !== ''">
            <el-image :src="dataForm.companyImg" class="company-logo-image"></el-image>
          </div>
          <div v-else>
            <div class="company-logo-add">
              <svg-icon icon="icon-add" class="company-logo-add-icon"></svg-icon>
              <div class="company-logo-add-text">上传企业LOGO</div>
            </div>
          </div>
        </el-upload>
      </div>
      <div class="company-name">{{ dataForm.companyName }}</div>
      <el-form label-width="150px" :model="dataForm" ref="dataFormRef" :rules="dataRules">
        <div class="company-info">
          <div class="company-info-title">
            <div class="company-info-title-before"></div>
            <div class="company-info-title-text">企业简介</div>
            <div class="company-info-title-divider"></div>
          </div>
          <div class="company-info-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="企业名称" prop="companyName">
                  <div v-if="isEdit" style="width: 100%">
                    <el-input
                      v-model="dataForm.companyName"
                      placeholder="企业名称"
                      disabled
                    ></el-input>
                  </div>
                  <div class="info-form-value" v-else>
                    {{ dataForm.companyName || "-" }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="企业法定代表人">
                  <div v-if="isEdit" style="width: 100%">
                    <el-input
                      v-model="dataForm.corporation"
                      placeholder="企业法定代表人"
                    ></el-input>
                  </div>
                  <div class="info-form-value" v-else>
                    {{ dataForm.corporation || "-" }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属行业">
                  <div v-if="isEdit" style="width: 100%">
                    <el-input
                      v-model="dataForm.industry"
                      placeholder="所属行业"
                    ></el-input>
                  </div>
                  <div class="info-form-value" v-else>
                    {{ dataForm.industry || "-" }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="企业规模">
                  <div v-if="isEdit" style="width: 100%">
                    <fast-select
                      v-model="dataForm.companySize"
                      dict-type="company_size"
                      clearable
                      placeholder="企业规模"
                    ></fast-select>
                  </div>
                  <div class="info-form-value" v-else>
                    <span
                      v-html="getDictLabelList('company_size', dataForm.companySize)"
                      v-if="(dataForm.companySize ?? '') !== ''"
                    ></span>
                    <span v-else>-</span>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系人">
                  <div v-if="isEdit" style="width: 100%">
                    <el-input
                      v-model="dataForm.linkman"
                      placeholder="所属行业"
                    ></el-input>
                  </div>
                  <div class="info-form-value" v-else>
                    {{ dataForm.linkman || "-" }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邮政编码">
                  <div v-if="isEdit" style="width: 100%">
                    <el-input
                      v-model="dataForm.postcode"
                      placeholder="邮政编码"
                    ></el-input>
                  </div>
                  <div class="info-form-value" v-else>
                    {{ dataForm.postcode || "-" }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所在地区">
                  <div v-if="isEdit" style="width: 100%">
                    <el-tree-select
                      v-model="dataForm.areaId"
                      :data="areaList"
                      value-key="id"
                      check-strictly
                      :render-after-expand="false"
                      :props="{ label: 'name', children: 'children' }"
                      style="width: 100%"
                      clearable
                      placeholder="企业所在地"
                      @node-click="onChangeAreaId"
                    />
                  </div>
                  <div class="info-form-value" v-else></div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="详细地址">
                  <div v-if="isEdit" style="width: 100%">
                    <el-input
                      v-model="dataForm.address"
                      placeholder="详细地址"
                    ></el-input>
                  </div>
                  <div class="info-form-value" v-else>
                    {{ dataForm.address || "-" }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="企业简介">
                  <div v-if="isEdit" style="width: 100%">
                    <el-input
                      v-model="dataForm.companyIntroduction"
                      placeholder="企业简介"
                    ></el-input>
                  </div>
                  <div class="info-form-value" v-else>
                    {{ dataForm.companyIntroduction || "-" }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="company-invoice">
          <div class="company-info-title">
            <div class="company-info-title-before"></div>
            <div class="company-info-title-text">开票信息</div>
            <div class="company-info-title-divider"></div>
          </div>
          <div class="company-invoice-form">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开票抬头" prop="invoiceTitle">
                  <div v-if="isEdit" style="width: 100%">
                    <el-input
                      v-model="dataForm.invoiceTitle"
                      placeholder="开票抬头"
                    ></el-input>
                  </div>
                  <div class="info-form-value" v-else>
                    {{ dataForm.invoiceTitle || "-" }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="纳税人识别号" prop="taxpayerNumber">
                  <div v-if="isEdit" style="width: 100%">
                    <el-input
                      v-model="dataForm.taxpayerNumber"
                      placeholder="纳税人识别号"
                    ></el-input>
                  </div>
                  <div class="info-form-value" v-else>
                    {{ dataForm.taxpayerNumber || "-" }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="银行账号" prop="bankAccountNo">
                  <div v-if="isEdit" style="width: 100%">
                    <el-input
                      v-model="dataForm.bankAccountNo"
                      placeholder="银行账号"
                    ></el-input>
                  </div>
                  <div class="info-form-value" v-else>
                    {{ dataForm.bankAccountNo || "-" }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开户行" prop="bankAccountName">
                  <div v-if="isEdit" style="width: 100%">
                    <el-input
                      v-model="dataForm.bankAccountName"
                      placeholder="开户行"
                    ></el-input>
                  </div>
                  <div class="info-form-value" v-else>
                    {{ dataForm.bankAccountName || "-" }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册地址">
                  <div v-if="isEdit" style="width: 100%">
                    <el-input
                      v-model="dataForm.registerAddress"
                      placeholder="注册地址"
                    ></el-input>
                  </div>
                  <div class="info-form-value" v-else>
                    {{ dataForm.registerAddress || "-" }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="备注">
                  <div v-if="isEdit" style="width: 100%">
                    <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
                  </div>
                  <div class="info-form-value" v-else>
                    {{ dataForm.remark || "-" }}
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <div class="company-action" v-if="isEdit">
        <el-button type="primary" @click="onClickSave">保存</el-button>
        <el-button @click="onClickCancel">取消</el-button>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { getCompanyInfo, saveCompanyInfo } from "@/api/employee";
import { onMounted, ref } from "vue";
import { getDictLabelList } from "@/utils/tool";
import cache from "@/utils/cache";
import constant from "@/utils/constant";
import { ElMessage } from "element-plus";
import { getAreaTree } from "@/api/supplier/manage";

const user = useUserStore();

const edit = ref<Boolean>(true);

interface IAreaItem {
  id: string;
  name: string;
  disabled: boolean;
  children?: IAreaItem[];
}

const isEdit = ref<Boolean>(false);

const areaList = ref<IAreaItem[]>([]);

const dataForm = ref({
  id: undefined,
  companyImg: "",
  companyName: "",
  corporation: "",
  industry: "",
  companySize: "",
  linkman: "",
  postcode: "",
  areaId: "",
  areaName: "",
  address: "",
  companyIntroduction: "",
  invoiceTitle: "",
  taxpayerNumber: "",
  bankAccountNo: "",
  bankAccountName: "",
  registerAddress: "",
  remark: "",
});

const dataFormModel = ref({
  id: undefined,
  companyImg: "",
  companyName: "",
  corporation: "",
  industry: "",
  companySize: "",
  linkman: "",
  postcode: "",
  areaId: "",
  areaName: "",
  address: "",
  companyIntroduction: "",
  invoiceTitle: "",
  taxpayerNumber: "",
  bankAccountNo: "",
  bankAccountName: "",
  registerAddress: "",
  remark: "",
});

const dataRules = ref({
  companyName: [{ required: true, message: "请输入公司名称", trigger: "blur" }],
  invoiceTitle: [{ required: true, message: "请输入开票抬头", trigger: "blur" }],
  taxpayerNumber: [{ required: true, message: "请输入纳税人识别号", trigger: "blur" }],
  bankAccountNo: [{ required: true, message: "请输入银行账号", trigger: "blur" }],
  bankAccountName: [{ required: true, message: "请输入开户行", trigger: "blur" }],
});

onMounted(() => {
  GetCompanyInfo();
  GetAreaTree();
});

const GetCompanyInfo = () => {
  getCompanyInfo(user.user.id).then((res) => {
    console.log(res);
    if (res.code === 0) {
      let newDataForm = JSON.parse(
        JSON.stringify({
          id: undefined,
          companyImg: "",
          companyName: "",
          corporation: "",
          industry: "",
          companySize: "",
          linkman: "",
          postcode: "",
          areaId: "",
          areaName: "",
          address: "",
          companyIntroduction: "",
          invoiceTitle: "",
          taxpayerNumber: "",
          bankAccountNo: "",
          bankAccountName: "",
          registerAddress: "",
          remark: "",
        })
      );
      Object.assign(newDataForm, res.data);
      dataForm.value = JSON.parse(JSON.stringify(newDataForm));
      dataFormModel.value = JSON.parse(JSON.stringify(newDataForm));
    }
  });
};

const GetAreaTree = () => {
  getAreaTree().then((res: any) => {
    if (res.code === 0) {
      let newAreaList = computeAreaList(res.data);
      areaList.value = newAreaList;
    }
  });
};

const computeAreaList = (list: IAreaItem[]) => {
  let newAreaList: IAreaItem[] = list.map((item) => {
    return {
      id: item.id + "",
      name: item.name,
      disabled: (item.children ?? []).length > 0 ? true : false,
      children: computeAreaList(item.children ?? []),
    };
  });
  return newAreaList;
};

const dataFormRef = ref();

const onClickSave = () => {
  dataFormRef.value.validate((valid) => {
    if (!valid) {
      return false;
    }
    saveCompanyInfo(dataForm.value).then((res) => {
      if (res.code === 0) {
        ElMessage.success("保存成功");
        GetCompanyInfo();
        isEdit.value = false;
      }
    });
  });
};

const onChangeAreaId = (areaItem: IAreaItem) => {
  if (!areaItem.disabled) {
    dataForm.value.areaId = areaItem.id;
    dataForm.value.areaName = areaItem.name;
  }
};

const onClickCancel = () => {
  dataForm.value = JSON.parse(JSON.stringify(dataFormModel.value));
  isEdit.value = false;
};

const beforeUpload = (file) => {
  let fileNameList = file.name.split(".");
  let fileType = fileNameList[fileNameList.length - 1];
  if (["jpg", "png", "jpeg"].indexOf(fileType) == -1) {
    ElMessage.error("请上传图片文件");
    return false;
  }
  if (file.size / 1024 / 1024 > 20) {
    ElMessage.error("文件大小不能超过20M");
    return false;
  }
  return true;
};

const handleSuccess = (res, type) => {
  if (res.code === 0) {
    let newDataForm = JSON.parse(JSON.stringify(dataForm.value));
    newDataForm.companyImg = res.data.url;
    console.log(newDataForm);
    dataForm.value = newDataForm;
    console.log(dataForm.value);
  } else {
    ElMessage.error("上传失败：" + res.msg);
  }
};

const onClickEdit = () => {
  isEdit.value = true;
};
</script>
<style lang="scss" scoped>
.company {
  &-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-text {
      display: flex;
      align-items: center;
      &-divider {
        width: 4px;
        height: 18px;
        background-color: var(--el-color-primary);
        margin-right: 6px;
        border-radius: 4px;
      }
      &-title {
        font-size: 16px;
        font-weight: 700;
        color: #333333;
      }
    }
  }
  &-divider {
    height: 1px;
    background-color: #f0f0f0;
    margin: 10px 0;
  }
  &-logo {
    margin-top: 40px;
    display: flex;
    justify-content: center;
    &-image {
      width: 100px;
      height: 100px;
      border-radius: 10px;
    }
    &-add {
      width: 100px;
      height: 100px;
      border-radius: 10px;
      background-color: #f0f0f0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      &-icon {
        font-size: 16px;
        color: #999999;
      }
      &-text {
        font-size: 12px;
        color: #999999;
      }
    }
  }
  &-name {
    display: flex;
    justify-content: center;
    margin-top: 10px;
  }
  &-info {
    &-title {
      margin-top: 16px;
      display: flex;
      align-items: center;
      &-before {
        width: 4px;
        height: 16px;
        background-color: var(--el-color-primary);
        margin-right: 6px;
        border-radius: 4px;
      }
      &-text {
        font-size: 14px;
        font-weight: 700;
        color: #333333;
        margin-right: 10px;
      }
      &-divider {
        flex: 1;
        height: 1px;
        background-color: #f0f0f0;
      }
    }
    &-form {
      margin-top: 16px;
    }
  }
  &-invoice {
    &-form {
      margin-top: 16px;
    }
  }
  &-action {
    display: flex;
    justify-content: center;
    margin-top: 40px;
  }
}
</style>
