import service from '@/utils/request'

export const useUserInfoApi = () => {
	return service.get('/sys/user/info')
}

export const useUserInfoSubmitApi = (dataForm: any) => {
	return service.put('/sys/user/info', dataForm)
}

export const updatePasswordApi = (data: any) => {
	return service.put('/sys/user/password', data)
}

export const useUserApi = (id: number) => {
	return service.get('/sys/user/' + id)
}

export const useUserSubmitApi = (dataForm: any) => {
	if (dataForm.id) {
		return service.put('/sys/user', dataForm)
	} else {
		return service.post('/sys/user', dataForm)
	}
}

//获取用户详情信息
export const getUserDetailInfo = (id: number) => {
	return service.get(`/sys/userDetails/${id}`)
}

interface IAttachmentItem {
	name: string;
	url: string;
	size: number;
	type: string;
	platform: string;
}

//修改用户详情
export interface IActionUserDetail {
	id?: number
	subjectType: string
	companyName?: string
	creditCode?: string
	industry?: string
	areaId?: string
	legalPerson?: string
	registeredCapital?: number
	establishTime?: string
  qualificationOne?:any
	workAddress?: string
	introduction?: string
	businessScope?: string
	contactName: string
	contactPhone: string
	idNumber: string
	address: string
	email: string
	zipCode: string
	authorizationLetter?: string
	authorizedIdCardFront: string
	authorizedIdCardBack: string
	openingBank?: string
	accountNumber?: string
	submit: string
	attachmentList: IAttachmentItem[]
}
export const actionUserDetail = (reqData: IActionUserDetail) => {
	return service.put(`/sys/userDetails`, reqData)
}

//审核供应商
export interface IAuditSupplier {
	purchaserBidderId?: number
	auditResult: string
	opinion: string
}
export const auditSupplier = (reqData: IAuditSupplier) => {
	return service.post(`/sys/purchaserBidder/audit`, reqData)
}
