<!--
 * @Descripttion:
 * @Author: cuiyingchun
 * @Date: 2024-03-21 15:24:00
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-16 17:56:52
 * @Introduce::
-->
<template>
	<div class="hc-template">
		<iframe id="iframeRef" ref="iframeRef" class="frame" src="./hc/signer.html" frameborder="0"></iframe>
	</div>
	<!-- <div class="footer">
		<el-button @click="saveDraft('00', true)">保存草稿</el-button>
		<el-button type="primary" @click="nextStep()">是否签章完成</el-button>
	</div> -->
</template>

<script setup lang="ts">
import { ref, defineEmits, onMounted, defineExpose, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import store from '@/store'

const emit = defineEmits(['pre', 'next'])
const iframeRef = ref()
const signCode = ref('')
const signCompleteSrc = ref('')
const isSign = ref(false)
const info = reactive({ id: '', url: '' })

onMounted(()=>{
  getPdf()
})

const getPdf = async id => {
	info.id = id
	// info.url = import.meta.env.VITE_API_URL + '/cic/credit/getApplyPDF/' + id + '?access_token=' + store.userStore.token
  info.url = 'https://501351981.github.io/vue-office/examples/dist/static/test-files/test.pdf'
	let viewer = document.getElementById('iframeRef')
	let send = document.getElementById('iframeRef').contentWindow
	viewer.onload = () => {
		send.postMessage({ url: info.url, type: 'pp', api: import.meta.env.VITE_API_URL }, '*')
	}
}

window.addEventListener(
	'message',
	function (res) {
    // 如果没有安装ca驱动的提示。。。
		// if (res.data.error) {
		// 	ElMessageBox.alert(`${res.data.error}，请确保正确安装了数字证书驱动。如果未安装，请前往登录页下载安装。`, '提示', {
		// 		confirmButtonText: '知道了',
		// 		callback: () => {}
		// 	})
		// }
		if (res.data.code) {
			signCode.value = res.data.code
		}
		if (res.data.signCompleteSrc) {
			signCompleteSrc.value = res.data.signCompleteSrc
		}
	},
	false
)

const saveDraft = async (state, tip = false) => {
	if (signCode.value != 'signStart') {
		let params = signCompleteSrc.value
			? { id: info.id, gather: signCompleteSrc.value, state: state }
			: { id: info.id, gather: info.gather, state: state }
		// await useSignatureUpdateApi(params).then(res => {
		// 	if (res.code !== 0) {
		// 		isSign.value = false
		// 		return
		// 	}
		// 	isSign.value = true
		// 	if (tip) {
		// 		ElMessage.success('保存草稿成功！')
		// 	}
		// })
	} else {
		ElMessage.error('签章未完成！')
	}
}
const nextStep = async () => {
	if (signCode.value != 'signStart') {
		// await saveDraft('01')
		// isSign.value && emit('next', 3)
	} else {
		ElMessage.error('签章未完成！')
	}
}

defineExpose({
	getPdf
})
</script>

<style scoped lang="scss">
.hc-template {
	width: 100%;
	height: 720px;
	.frame {
		width: 100%;
		height: 100%;
	}
}
.footer {
	text-align: center;
	margin: 20px 0 0;
}
</style>
