<template>
  <el-card>
    <div class="box">
      <div class="leftBox">
        <el-tree
          :data="treeData"
          highlight-current
          node-key="id"
          default-expand-all
          :expand-on-click-node="false"
          :props="{ children: 'children', label: 'name' }"
          @node-click="handleNodeClick"
        ></el-tree>
      </div>
      <div class="notice">
        <div class="notice-search">
          <!-- 当前文件夹：{{ state.queryForm.folderName }}{{ state.queryForm.folderId }}
          <el-button
            v-if="state.queryForm.folderId"
            type="danger"
            plain
            style="margin-right: 12px"
            @click="deletefolder()"
            >删除文件夹</el-button
          > -->
          <el-input
            placeholder="文件名称"
            clearable
            v-model="state.queryForm.fileName"
            style="width: 200px; margin-right: 12px"
          ></el-input>
          <el-button @click="getDataList()" type="primary">查询</el-button>
          <el-button @click="uploadClick()" type="primary">上传文件</el-button>
          <el-button type="primary" plain style="margin-right: 12px" @click="addFolder()"
            >新建文件夹</el-button
          >
          <el-button
            type="primary"
            plain
            style="margin-right: 12px"
            @click="batchMoveClick()"
            >批量移动</el-button
          >
          <el-button
            type="danger"
            plain
            style="margin-right: 12px"
            @click="deleteBatchHandle()"
            >批量删除</el-button
          >
        </div>
        <div class="notice-list">
          <el-table
            v-loading="state.dataListLoading"
            :data="state.dataList"
            @selection-change="selectionChangeHandle"
            border
          >
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column
              prop="name"
              label="文件名称"
              header-align="center"
              align="center"
            >
              <template #default="scope">
                <span>{{ scope.row.sysAttachmentVO.name }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="realName"
              label="创建人"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="youSubjectName"
              label="文件大小"
              header-align="center"
              align="center"
            >
              <template #default="scope">
                <span>{{ convertSizeFormat(scope.row.sysAttachmentVO.size) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="updateTime"
              label="最近修改时间"
              header-align="center"
              align="center"
            >
              <template #default="scope">
                <span>{{ formatDate(scope.row.updateTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              fixed="right"
              header-align="center"
              align="center"
              width="280"
            >
              <template #default="scope">
                <el-button
                  type="primary"
                  link
                  @click="downloadFile(scope.row.sysAttachmentVO.url, '')"
                  >下载</el-button
                >
                <el-button type="primary" link @click="shareClick(scope.row)"
                  >分享</el-button
                >
                <!-- v-if="state.queryForm.attachmentId!==''" -->
                <el-button
                  type="primary"
                  link
                  @click="moveClick(scope.row)"
                  v-if="state.queryForm.folderId !== ''"
                  >移动</el-button
                >
                <el-button
                  type="primary"
                  link
                  @click="resetNameClick(scope.row)"
                  v-if="state.queryForm.folderId !== ''"
                  >重命名</el-button
                >
                <el-button type="primary" link @click="onClickDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="notice-page">
          <el-pagination
            :current-page="state.pageNo"
            :page-sizes="state.pageSizes"
            :page-size="state.limit"
            :total="state.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="sizeChangeHandle"
            @current-change="currentChangeHandle"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </el-card>
  <el-dialog
    title="新建文件夹"
    v-model="isAddFolder"
    :close-on-click-modal="false"
    width="30%"
    :before-close="resetObj"
  >
    <div class="dialogBox">
      <div style="margin-bottom: 15px">
        <span style="width: 120px; text-align: right; display: inline-block"
          ><span style="color: red">*</span>选择上级文件夹：</span
        >
        <el-cascader
          ref="cascaderRef"
          v-model="folderCascader"
          v-if="isAdmin"
          :options="treeFolderData"
          :props="{
            children: 'children',
            label: 'name',
            value: 'id',
            expandTrigger: 'hover',
            checkStrictly: true,
          }"
          :show-all-levels="false"
          style="width: 60%"
          @change="handleChange"
        ></el-cascader>
        <el-cascader
          ref="cascaderRef"
          v-model="folderCascader"
          v-else
          :options="treeMyFolderData"
          :props="{
            children: 'children',
            label: 'name',
            value: 'id',
            expandTrigger: 'hover',
            checkStrictly: true,
          }"
          :show-all-levels="false"
          style="width: 60%"
          @change="handleChange"
        ></el-cascader>
      </div>
      <div>
        <span style="width: 120px; text-align: right; display: inline-block"
          ><span style="color: red">*</span>文件夹名称：</span
        >
        <el-input
          v-model="folderValue"
          placeholder="请输入内容"
          style="width: 60%"
        ></el-input>
      </div>
    </div>
    <div slot="footer" style="text-align: right">
      <el-button @click="resetObj">取 消</el-button>
      <el-button type="primary" @click="folderQuery">确 定</el-button>
    </div>
  </el-dialog>
  <el-dialog
    title="上传文件"
    v-model="isUploadFolder"
    :close-on-click-modal="false"
    width="30%"
    :before-close="resetObj"
  >
    <div class="dialogBox">
      <div>
        <span style="width: 90px; text-align: right; display: inline-block"
          ><span style="color: red">*</span>选择文件夹：</span
        >
        <el-cascader
          ref="cascaderRef"
          v-model="folderCascader"
          v-if="isAdmin"
          :options="treeFolderData"
          :props="{
            children: 'children',
            label: 'name',
            value: 'id',
            expandTrigger: 'hover',
            checkStrictly: true,
          }"
          :show-all-levels="false"
          style="width: 60%"
          @change="handleChange"
        ></el-cascader>
        <el-cascader
          ref="cascaderRef"
          v-model="folderCascader"
          v-else
          :options="treeMyFolderData"
          :props="{
            children: 'children',
            label: 'name',
            value: 'id',
            expandTrigger: 'hover',
            checkStrictly: true,
          }"
          :show-all-levels="false"
          style="width: 60%"
          @change="handleChange"
        ></el-cascader>
      </div>
      <div style="margin: 15px 0">
        <span style="width: 90px; text-align: right; display: inline-block"
          ><span style="color: red">*</span>文件名称：</span
        >
        <el-input
          v-model="folderValue"
          placeholder="请输入内容"
          style="width: 60%"
        ></el-input>
      </div>
      <div>
        <el-upload
          class="upload"
          v-model:file-list="state.fileList"
          :headers="{ Authorization: cache.getToken() }"
          :action="constant.uploadUrl"
          :before-upload="beforeUpload"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
          accept=".jpeg,.jpg,.doc,.docx,.xls,.xlsx,.pdf,.png,.zip,.rar"
        >
          <el-button type="primary" v-if="attachmentList.length == 0">上传附件</el-button>
          <template #tip v-if="attachmentList.length == 0">
            <div class="el-upload__tip">最多支持上传30个文件</div>
          </template>
        </el-upload>
      </div>
    </div>
    <div slot="footer" style="text-align: right">
      <el-button @click="resetObj">取 消</el-button>
      <el-button type="primary" @click="uploadQuery">确 定</el-button>
    </div>
  </el-dialog>
  <el-dialog
    title="移动"
    v-model="isMoveFolder"
    :close-on-click-modal="false"
    width="30%"
    :before-close="resetObj"
  >
    <div class="dialogBox">
      <div style="margin-bottom: 15px">
        <span style="width: 120px; text-align: right; display: inline-block"
          ><span style="color: red">*</span>选择文件夹：</span
        >
        <el-cascader
          ref="cascaderRef"
          v-model="folderCascader"
          v-if="isAdmin"
          :options="treeFolderData"
          :props="{
            children: 'children',
            label: 'name',
            value: 'id',
            expandTrigger: 'hover',
            checkStrictly: true,
          }"
          :show-all-levels="false"
          style="width: 60%"
          @change="handleChange"
        ></el-cascader>
        <el-cascader
          ref="cascaderRef"
          v-model="folderCascader"
          v-else
          :options="treeMyFolderData"
          :props="{
            children: 'children',
            label: 'name',
            value: 'id',
            expandTrigger: 'hover',
            checkStrictly: true,
          }"
          :show-all-levels="false"
          style="width: 60%"
          @change="handleChange"
        ></el-cascader>
      </div>
    </div>
    <div slot="footer" style="text-align: right">
      <el-button @click="resetObj">取 消</el-button>
      <el-button type="primary" @click="moveQuery">确 定</el-button>
    </div>
  </el-dialog>
  <el-dialog
    title="重命名"
    v-model="isResetName"
    :close-on-click-modal="false"
    width="30%"
    :before-close="resetObj"
  >
    <div class="dialogBox">
      <div style="margin: 15px 0">
        <span style="width: 120px; text-align: right; display: inline-block"
          ><span style="color: red">*</span>文件名称：</span
        >
        <el-input
          v-model="folderValue"
          placeholder="请输入内容"
          style="width: 60%"
        ></el-input>
      </div>
    </div>
    <div slot="footer" style="text-align: right">
      <el-button @click="resetObj">取 消</el-button>
      <el-button type="primary" @click="resetNameQuery">确 定</el-button>
    </div>
  </el-dialog>
  <!-- 选择成员 -->
  <select-user-tree
    ref="selectUserTreeRef"
    @select-user="getSelectUser"
  ></select-user-tree>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { nextTick, reactive, ref, onMounted } from "vue";
import { useRouter } from "vue-router";
import constant from "@/utils/constant";
import cache from "@/utils/cache";
import selectUserTree from "./components/selectUserTree.vue";
import { convertSizeFormat, downloadFile } from "@/utils/tool";
import {
  getDocumentTreeList,
  submitAddFolder,
  managerIsCheck,
  documentLibrary,
  shareFile,
  moveFile,
  resetName,
  getDocumentMyTreeList,
  deleteFile,
  deleteFileShare,
  deleteFolderApi
} from "@/api/documentLib";
import { ElMessage, ElMessageBox } from "element-plus";
const selectUserTreeRef = ref<InstanceType<typeof selectUserTree>>();
const dataUrl = ref("/work/documentLibrary/getCommonFile");
const state: IHooksOptions = reactive({
  dataListUrl: dataUrl,
  deleteUrl: "/work/documentLibrary",
  value: "",
  queryForm: {
    folderId: 1001,
    folderName: '公共文件',
    fileName: "",
  },
  fileList: [],
});
const router = useRouter();
const treeData = ref([]);
const treeFolderData = ref([]);
const treeMyFolderData = ref([]);
const attachmentList = ref([]);
const isAddFolder = ref(false);
const isUploadFolder = ref(false);
const isMoveFolder = ref(false);
const isResetName = ref(false);
const folderValue = ref("");
const cascaderRef = ref();
const cascaderLabel = ref("");
const isMyFile = ref();
const cascaderValue = ref(0);
const isAdmin = ref(false);
const shareRow = ref({});
const moveRow = ref([]);
const folderCascader = ref([]);
const resetNameRow = ref({});
onMounted(() => {
  getUseType();
  getTreeList();
});

// 获取用户是否为管理员
const getUseType = () => {
  managerIsCheck().then((res) => {
    isAdmin.value = res.data;
  });
};
// 格式化日期时间
function formatDate(str) {
  function padZero(num) {
    return num < 10 ? "0" + num : num;
  }
  let date = new Date(str);
  var year = date.getFullYear();
  var month = padZero(date.getMonth() + 1);
  var day = padZero(date.getDate());
  var hours = padZero(date.getHours());
  var minutes = padZero(date.getMinutes());
  var seconds = padZero(date.getSeconds());

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
// 获取左侧文件夹树
const getTreeList = () => {
  let treeArr = [];
  let folderArr = [];
  let myFolderArr = [];
  // 获取公共文件文件夹
  getDocumentTreeList().then((res) => {
    folderArr = res.data;
    // 获取我的文件文件夹
    getDocumentMyTreeList().then((res) => {
      myFolderArr = res.data;
      treeMyFolderData.value = myFolderArr;
      treeArr = treeArr.concat(folderArr);
      treeArr = treeArr.concat(myFolderArr);
      treeFolderData.value = treeArr;
      let arr = JSON.parse(JSON.stringify(treeArr));
      arr.push({
        name: "分享给我的",
        id: 99,
        isMyFile: 2,
        children: [],
      });
      treeData.value = arr;
    });
  });
};
// 切换左侧文件夹树
const handleNodeClick = (data: any) => {
  state.pageNo = 1;
  if (data.isMyFile === 0) {
    state.queryForm["folderId"] = data.id;
    state.queryForm["folderName"] = data.name;
    dataUrl.value = "/work/documentLibrary/getCommonFile";
  } else if (data.isMyFile === 1) {
    state.queryForm["folderId"] = data.id;
    state.queryForm["folderName"] = data.name;
    dataUrl.value = "/work/documentLibrary/getMyFile";
  } else {
    state.queryForm["folderId"] = "";
    dataUrl.value = "/work/documentLibrary/getShareFile";
  }
  getDataList();
};
// 新建文件夹
const addFolder = () => {
  isAddFolder.value = true;
};
// 切换文件夹选择
const handleChange = () => {
  let row = cascaderRef.value.getCheckedNodes()[0];
  isMyFile.value = row.data.isMyFile;
  cascaderLabel.value = row.label;
  cascaderValue.value = row.value;
};
// 新建文件夹确认
const folderQuery = () => {
  if (cascaderValue.value === 0 || folderValue.value === "") {
    return ElMessage.error("请将信息填写完整！");
  }
  let params = {
    isMyFile: isMyFile.value ? isMyFile.value : 0,
    pid: cascaderValue.value,
    parentName: cascaderLabel.value,
    name: folderValue.value,
  };
  submitAddFolder(params).then((res) => {
    if (res.code !== 0) {
      return ElMessage.error(res.msg);
    }
    resetObj();
    getTreeList();
    getDataList();
    ElMessage.success("操作成功！");
  });
};
// 点击上传文件
const uploadClick = () => {
  isUploadFolder.value = true;
};
// 上传完回调
const handleSuccess = (res, file) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  attachmentList.value.push(res.data);
};
// 附件上传前判断
const beforeUpload = (file) => {
  let fileType=""
  if(file.name){
    let nameArray=file.name.split(".");
    fileType=nameArray[nameArray.length-1];
  }
  if (
    [
      "jpg",
      "png",
      "jpeg",
      "doc",
      "docx",
      "xls",
      "pdf",
      "xlsx",
      "gif",
      "zip",
      "rar",
    ].indexOf(fileType) == -1
  ) {
    ElMessage.error("格式不支持");
    return false;
  } else if (file.size / 1024 / 1024 > 10) {
    ElMessage.error("文件大小不超过10M");
    return false;
  }
  return true;
};
// 删除附件
const handleRemove = (file, files) => {
  let uploadList = [];
  for (let i of files) {
    if ("response" in i) {
      uploadList.push(i.response.data);
    } else {
      uploadList.push(i);
    }
  }
  state.fileList = uploadList;
  attachmentList.value = uploadList;
};
// 确认上传文件
const uploadQuery = () => {
  if (
    cascaderValue.value === 0 ||
    folderValue.value === "" ||
    attachmentList.value.length === 0
  ) {
    return ElMessage.error("请将信息填写完整！");
  }
  let obj = {
    isCommon: 0,
    folderId: cascaderValue.value,
    sysAttachmentVO: {
      ...attachmentList.value[0],
      name: folderValue.value,
    },
  };
  if (isAdmin.value) {
    obj["isCommon"] = 1;
  }
  documentLibrary(obj).then((res) => {
    if (res.code !== 0) {
      return ElMessage.error(res.msg);
    }
    resetObj();
    getDataList();
    ElMessage.success("操作成功！");
  });
};
// 重置数据
const resetObj = () => {
  attachmentList.value = [];
  state.fileList = [];
  folderValue.value = "";
  cascaderLabel.value = "";
  cascaderValue.value = 0;
  isResetName.value = false;
  isMoveFolder.value = false;
  isUploadFolder.value = false;
  isAddFolder.value = false;
  folderCascader.value = [];
};
// 分享
const shareClick = (row: any) => {
  selectUserTreeRef.value?.init();
  shareRow.value = row;
};
// 分享确认
const getSelectUser = (arr: any) => {
  console.log(arr);
  let shareIds = "";
  for (let item of arr) {
    shareIds += item.id + ",";
  }
  shareIds = shareIds.slice(0, shareIds.length - 1);
  console.log(shareIds);
  let obj = {
    ...shareRow.value,
    shareIds,
  };
  shareFile(obj).then((res) => {
    if (res.code !== 0) {
      return ElMessage.error(res.msg);
    }
    ElMessage.success("操作成功！");
  });
};
// 点击移动
const moveClick = (row) => {
  moveRow.value.push(row.id);
  isMoveFolder.value = true;
};
// 点击批量移动
const batchMoveClick = () => {
  moveRow.value = JSON.parse(JSON.stringify(state.dataListSelections));
  isMoveFolder.value = true;
};
// 移动确认
const moveQuery = () => {
  if (cascaderValue.value === 0) {
    return ElMessage.error("请选择文件夹！");
  }
  let obj = {
    folderId: cascaderValue.value,
    idList: moveRow.value,
  };
  moveFile(obj).then((res) => {
    if (res.code !== 0) {
      return ElMessage.error(res.msg);
    }
    resetObj();
    getDataList();
    ElMessage.success("操作成功！");
  });
};
// 点击重命名
const resetNameClick = (row) => {
  resetNameRow.value = row;
  isResetName.value = true;
};
// 重命名确认
const resetNameQuery = () => {
  if (folderValue.value === "") {
    return ElMessage.error("请填写文件名称！");
  }
  let obj = {
    fileName: folderValue.value,
    folderId: state.queryForm.attachmentId,
    attachmentId: resetNameRow.value.attachmentId,
  };
  resetName(obj).then((res) => {
    if (res.code !== 0) {
      return ElMessage.error(res.msg);
    }
    resetObj();
    getDataList();
    ElMessage.success("操作成功！");
  });
};
const {
  getDataList,
  deleteBatchHandle,
  sizeChangeHandle,
  currentChangeHandle,
  selectionChangeHandle,
} = useCrud(state);

const onClickDelete = (row) => {
  ElMessageBox.confirm("确定进行删除操作?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      let data = [];
      if (row.id) {
        data = [row.id];
      } else {
        data = state.dataListSelections ? state.dataListSelections : [];

        if (data.length === 0) {
          ElMessage.warning("请选择删除记录");
          return;
        }
      }
      console.log(data);
      if (state.queryForm.folderId !== "") {
        deleteFile(data).then(() => {
          ElMessage.success("删除成功");
          state.pageNo = 1;
          getDataList();
        });
      } else {
        deleteFileShare(data).then(() => {
          ElMessage.success("删除成功");
          state.pageNo = 1;
          getDataList();
        });
      }
    })
    .catch(() => {});
};
const deletefolder=()=>{
  ElMessageBox.confirm(`确定删除【${state.queryForm.folderName}】文件夹?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteFolderApi(state.queryForm.folderId).then((res:any) => {
      if(res.code===0){
        ElMessage.success("文件夹删除成功");
        getTreeList();
      }
    });
  })
  .catch(() => { })
}
</script>
<style lang="scss" scoped>
.box {
  display: flex;
  justify-content: space-between;
  align-items: start;
  height: 85vh;
  .leftBox {
    min-width: 200px;
    width: 15%;
    height: 100%;
    border-right: 1px solid rgb(242, 242, 242);
    padding: 15px;
    ::v-deep(.el-tree-node__label) {
      font-size: 16px;
    }
  }
  .notice {
    width: 82%;
    &-list {
      margin-top: 16px;
    }
    .notice-search {
      text-align: right;
    }
  }
}
.dialogBox {
  margin: 30px 10px;
}
</style>
