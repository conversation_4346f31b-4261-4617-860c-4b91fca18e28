<template>
  <el-card>
    <div class="watch">
      <div class="watch-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              v-model="state.queryForm.packageName"
              placeholder="采购计划名称"
              clearable
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <fast-select
              v-model="state.queryForm.packageType"
              dict-type="package_type"
              clearable
              placeholder="采购方式"
              style="width: 200px"
            ></fast-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onResetSearch()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="watch-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column prop="title" label="采购计划名称"></el-table-column>
          <el-table-column prop="packageName" label="公告名称"></el-table-column>
          <fast-table-column
            prop="packageType"
            label="采购方式"
            dict-type="package_type"
          ></fast-table-column>
          <el-table-column
            prop="bidStartDate"
            label="报价开始时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="bidEndDate"
            label="报价结束时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="regNum"
            label="报名家数"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-button type="primary" link @click="onClickDetail(scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="watch-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <Detail :show="detail.show" :info="detail.info" @on-close="onCloseDetail"></Detail>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive } from "vue";
import Detail from "./components/Detail.vue";

const state = reactive<IHooksOptions>({
  queryForm: {
    packageName: "",
    packageType: "",
    auditStatus: 3,
  },
  dataListUrl: "purchase/package/page",
});

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

const onResetSearch = () => {
  state.queryForm.packageName = "";
  state.queryForm.packageType = "";
  state.pageNo = 1;
  getDataList();
};

interface IInfo {
  id?: number;
  packageName: string;
  packageType: string;
  bidStartDate: string;
  bidEndDate: string;
}

interface IDetail {
  show: boolean;
  info: IInfo;
}

const detail = reactive<IDetail>({
  show: false,
  info: {
    id: void 0,
    packageName: "",
    packageType: "",
    bidStartDate: "",
    bidEndDate: "",
  },
});

const onClickDetail = (row: any) => {
  let newInfo = {
    id: row.id,
    packageName: row.packageName,
    packageType: row.packageType,
    bidStartDate: row.bidStartDate,
    bidEndDate: row.bidEndDate,
  };
  console.log(newInfo);
  detail.info = newInfo;
  detail.show = true;
};

const onCloseDetail = () => {
  detail.show = false;
  let newInfo = {
    id: void 0,
    packageName: "",
    packageType: "",
    bidStartDate: "",
    bidEndDate: "",
  };
  detail.info = newInfo;
};
</script>
<style lang="scss" scoped>
.apply {
  &-list {
    margin-top: 16px;
  }
}
</style>
