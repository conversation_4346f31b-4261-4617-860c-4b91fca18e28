
<template>
	<el-dialog v-model="visible" :title="!userForm.nowId ? '新增发票' : '修改发票'" :close-on-click-modal="false" draggable>
		<el-form ref="dataFormRef" :model="dataForm" :rules="dataRules" label-width="160px" v-loading="userForm.floading">
			<el-form-item label="发票抬头：" required prop="invoiceHeader">
        <el-input v-model="dataForm.invoiceHeader" placeholder="发票抬头"></el-input>
			</el-form-item>
			<el-form-item prop="headerType" label="抬头类型：" required>
				<el-radio-group v-model="dataForm.headerType">
					<el-radio label="00">企业</el-radio>
					<el-radio label="01">组织</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item prop="invoiceType" label="发票类型：" required>
				<el-radio-group v-model="dataForm.invoiceType" @change="changeType">
					<el-radio label="00">普票</el-radio>
					<el-radio label="01">专票</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="纳税人识别号：" required prop="creditCode">
        <el-input v-model="dataForm.creditCode" placeholder="纳税人识别号"></el-input>
			</el-form-item>
      <Fragment v-show="dataForm.invoiceType=='00'">
        <el-form-item label="电子邮箱：" prop="email">
          <el-input v-model="dataForm.email" placeholder="电子邮箱"></el-input>
        </el-form-item>
      </Fragment>
      <Fragment v-show="dataForm.invoiceType=='01'">
        <el-form-item label="开户行：" prop="bankDeposit">
          <el-input v-model="dataForm.bankDeposit" placeholder="开户行"></el-input>
        </el-form-item>
        <el-form-item label="开户账号：" prop="basicAccount">
          <el-input v-model="dataForm.basicAccount" placeholder="开户账号"></el-input>
        </el-form-item>
        <el-form-item label="注册场所地址：" prop="registerAddress">
          <el-input v-model="dataForm.registerAddress" placeholder="注册场所地址"></el-input>
        </el-form-item>
        <el-form-item label="电话：" prop="registerPhone">
          <el-input v-model="dataForm.registerPhone" placeholder="电话"></el-input>
        </el-form-item>
      </Fragment>
		</el-form>
		<template #footer>
			<div class="el-dialog__footer_center">
				<el-button type="primary" @click="submitHandle()">保存</el-button>
				<el-button @click="visible = false">取消</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, defineEmits, defineExpose, onMounted } from 'vue'
import { ElMessage } from 'element-plus/es'
import { useInvoiceHeaderSaveApi,useInvoiceHeaderByIdApi,useInvoiceHeaderSavePutApi } from '@/api/pnotice'
const emit = defineEmits(['refreshDataList'])

const visible = ref(false)
const dataFormRef = ref()
const dataForm = reactive({
	invoiceHeader: '',
	headerType: '00',
	invoiceType: '00',
	creditCode: '',
	bankDeposit: '',
	email: '',
	basicAccount: '',
	registerAddress: '',
	registerPhone: ''
})
const userForm = reactive({
	invoiceHeader: '',
	creditCode: '',
  nowId:"",
  floading:false
})
const dataRules = ref()
const baseRules = {
	headerType: [{ required: true, message: '必填项', trigger: 'blur' }],
	invoiceType: [{ required: true, message: '必填项', trigger: 'blur' }],
	invoiceHeader: [{ required: true, message: '必填项', trigger: 'blur' }],
	creditCode: [{ required: true, message: '必填项', trigger: 'blur' }]
}
const puRules = {
	email: [{ required: true, message: '必填项', trigger: 'blur' }]
}
const addRules = {
	bankDeposit: [{ required: true, message: '必填项', trigger: 'blur' }],
	basicAccount: [{ required: true, message: '必填项', trigger: 'blur' }],
	registerAddress: [{ required: true, message: '必填项', trigger: 'blur' }],
	registerPhone: [{ required: true, message: '必填项', trigger: 'blur' }]
}

const init = (id?: number) => {
	visible.value = true
	if (dataFormRef.value) {
		dataFormRef.value.resetFields()
	}
  changeType('00')
	if (id) {
    userForm.nowId = id
		getHeader(id)
	}
}
const changeType = (val: string) => {
  userForm.floading = true
	if (val == '01') {
		dataRules.value = Object.assign({}, baseRules, addRules)
	} else {
		dataRules.value = Object.assign({}, baseRules, puRules)
	}
  setTimeout(() => {
    if (dataFormRef.value) dataFormRef.value.clearValidate()
    userForm.floading = false
  })
}

const getHeader = (id: number) => {
	useInvoiceHeaderByIdApi(id).then(res => {
		Object.assign(dataForm, res.data)
		changeType(dataForm.invoiceType)
	})
}

// 表单提交
const submitHandle = () => {
	dataFormRef.value.validate((valid: boolean) => {
		if (!valid) {
			return false
		}
    if(userForm.nowId){
      useInvoiceHeaderSavePutApi(dataForm).then(res => {
        if (res.code != 0) {
          return
        }
        ElMessage.success({
          message: '提交成功',
          duration: 500,
          onClose: () => {
            visible.value = false
            emit('refreshDataList')
          }
        })
      })
    }else{
      useInvoiceHeaderSaveApi(dataForm).then(res => {
        if (res.code != 0) {
          return
        }
        ElMessage.success({
          message: '提交成功',
          duration: 500,
          onClose: () => {
            visible.value = false
            emit('refreshDataList')
          }
        })
      })
    }
	})
}

defineExpose({
	init
})
</script>
<style scoped lang="scss">
.tips {
	margin-left: 160px;
	color: #f07f82;
}
</style>
