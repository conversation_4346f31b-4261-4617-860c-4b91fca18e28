<template>
  <div class="info">
    <el-form
      ref="dataFormRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="140px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="projectName" label="项目名称">
            <el-input
              v-model="state.dataForm.projectName"
              placeholder="项目名称"
              @blur="onBlurField('projectName')"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="projectNo" label="项目编号">
            <el-input
              v-model="state.dataForm.projectNo"
              placeholder="项目编号"
              @blur="onBlurField('projectNo')"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="budget" label="预算总金额（元）" class="info_budget">
            <div style="display: flex; align-items: center; width: 100%">
              <el-input-number
                v-model="state.dataForm.budget"
                placeholder="预算总金额（元）"
                @blur="onBlurField('budget')"
                controls-position="right"
                style="flex: 1"
              ></el-input-number>
              <!-- <div style="font-size: 12px; color: #f56c6c; margin-left: 6px">
                已签订合同额的92%
              </div> -->
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="projectYear" label="年度">
            <el-select
              v-model="state.dataForm.projectYear"
              placeholder="请选择年度"
              style="width: 100%"
              @change="onChangeProjectYear"
            >
              <el-option
                v-for="data in state.yearList"
                :key="data.dictValue"
                :label="data.dictLabel"
                :value="data.dictValue"
              >
                {{ data.dictLabel }}
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="orgId" label="项目所属机构">
            <el-tree-select
              v-model="state.dataForm.orgId"
              :data="state.orgList"
              check-strictly
              :props="defaultProps"
              node-key="id"
              :render-after-expand="false"
              style="width: 100%"
              @change="onChangeOrgId"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="projectContent" label="项目内容">
            <WangEditor
              v-model="state.dataForm.projectContent"
              placeholder="请输入项目内容"
            ></WangEditor>
            <!-- <el-input
              v-model="state.dataForm.projectContent"
              placeholder="项目内容"
              type="textarea"
              :rows="4"
              @blur="onBlurField('projectContent')"
            ></el-input> -->
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="dictValue" label="附件">
            <div class="info_upload">
              <div class="info_upload_action">
                <el-upload
                  :action="constant.uploadUrl"
                  :headers="{ Authorization: cache.getToken() }"
                  :before-upload="beforeUpload"
                  :on-success="handleSuccess"
                  :show-file-list="false"
                >
                  <div>
                    <div style="display: flex; align-items: center">
                      <el-button type="primary">上传</el-button>
                      <div style="margin-left: 12px; font-size: 13px; color: #f56c6c">
                        请上传项目相关的合同、清单、图纸
                      </div>
                    </div>
                    <div style="font-size: 13px; color: #333333">
                      支持上传.doc .docx .xls .xlsx .pdf .jpg .png .zip .rar .dwg文件
                    </div>
                  </div>
                </el-upload>
              </div>
              <div class="info_upload_list">
                <div
                  class="info_upload_list_item"
                  v-for="(item, index) in state.dataForm.attachmentList"
                >
                  <div class="info_upload_list_item_text">
                    {{ item.name }}
                  </div>
                  <div class="info_upload_list_item_action">
                    <el-icon @click="onDeleteFile(index)"><DeleteFilled /></el-icon>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
import { DeleteFilled } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import dayjs from "dayjs";
import constant from "@/utils/constant";
import cache from "@/utils/cache";
import WangEditor from "@/components/wang-editor/index.vue";
import { getProjectCode, listOrgByCurrentUser } from "@/api/purchase/project";

export default {
  name: "BaseInfo",
  components: {
    DeleteFilled,
    WangEditor,
  },
  props: {
    type: {
      type: String,
      default: "add",
    },
    baseInfo: {
      type: Object,
      default: () => ({
        projectName: "",
        projectNameModel: "",
        projectNo: "",
        budget: undefined,
        projectYear: "2024",
        orgId: null,
        projectContent: "",
        attachmentList: [],
      }),
    },
  },
  data() {
    return {
      constant,
      cache,
      defaultProps: {
        children: "children",
        label: "name",
      },
      state: {
        dataForm: {
          projectName: "",
          projectNameModel: "",
          projectNo: "",
          budget: undefined,
          projectYear: "2024",
          orgId: null,
          projectContent: "",
          attachmentList: [],
        },
        dataRules: {
          projectName: [{ required: true, message: "项目名称不能为空", trigger: "blur" }],
          projectNo: [{ required: true, message: "项目编号不能为空", trigger: "blur" }],
          budget: [{ required: true, message: "预算总金额不能为空", trigger: "blur" }],
          projectYear: [{ required: true, message: "请选择年度", trigger: "change" }],
          orgId: [{ required: true, message: "请选择项目所属机构", trigger: "change" }],
        },
        yearList: [],
        orgList: [],
      },
    };
  },

  watch: {
    baseInfo: {
      handler(newVal) {
        let newBaseInfo = JSON.parse(JSON.stringify(newVal));
        this.state.dataForm = newBaseInfo;
      },
      deep: true,
    },
    "state.dataForm.projectContent": {
      handler() {
        this.onBlurField("projectContent");
      },
    },
  },
  mounted() {
    this.getOrgList();
    let currentYear = dayjs().year();
    this.state.dataForm.projectYear = currentYear + "";
    let newYearList = [];
    for (let i = 10; i > 0; i--) {
      newYearList.push({
        dictLabel: currentYear - i + "",
        dictValue: currentYear - i + "",
      });
    }
    for (let i = 0; i < 10; i++) {
      newYearList.push({
        dictLabel: currentYear + i + "",
        dictValue: currentYear + i + "",
      });
    }
    this.state.yearList = newYearList;
    this.$nextTick(() => {
      this.$emit("emit-ref", this.$refs.dataFormRef);
    });
  },
  methods: {

    onBlurField(field) {
      if (
        this.type === "add" &&
        field === "projectName" &&
        this.state.dataForm.projectName !== this.state.dataForm.projectNameModel
      ) {
        getProjectCode(this.state.dataForm.projectName).then((res) => {
          if (res.code === 0) {
            this.state.dataForm.projectNo = res.data;
            this.$emit("on-blur", "projectNo", this.state.dataForm.projectNo);
          }
        });
      }
      if (field === "budget") {
        let budget = this.state.dataForm.budget;
        let strNum = budget ? budget.toString() : "";
        let decimalIndex = strNum.indexOf(".");

        if (decimalIndex !== -1) {
          // 如果有小数部分，则保留两位小数
          strNum = strNum.slice(0, decimalIndex + 3);
        }
        this.state.dataForm.budget = strNum === "" ? undefined : parseFloat(strNum);
      }
      this.$emit("on-blur", field, this.state.dataForm[field]);
    },

    onChangeProjectYear() {
      this.$emit("on-blur", "projectYear", this.state.dataForm.projectYear);
    },

    onChangeOrgId() {
      this.$emit("on-blur", "orgId", this.state.dataForm.orgId);
    },

    beforeUpload(file) {
  console.log(file);
  let fileNameList = file.name.split(".");
  let fileType = fileNameList[fileNameList.length - 1];
  if (
    [
      "jpg",
      "png",
      "jpeg",
      "doc",
      "docx",
      "xls",
      "pdf",
      "xlsx",
      "zip",
      "rar",
      "dwg",
    ].indexOf(fileType) == -1
  ) {
    ElMessage.error("请上传正确的文件格式");
    return false;
  }
  // if (file.size / 1024 / 1024 / 1024 / 1024 > 1) {
  // 	ElMessage.error('文件大小不能超过100M')
  // 	return false
  // }
      return true;
    },

    handleSuccess(res, file) {
      if (res.code === 0) {
        let newAttachmentItem = {
          platform: res.data.platform,
          url: res.data.url,
          name: res.data.name,
          size: res.data.size,
        };
        let newAttachmentList = JSON.parse(JSON.stringify(this.state.dataForm.attachmentList));
        newAttachmentList.push(newAttachmentItem);
        this.$emit("upload-field", newAttachmentList);
      } else {
        ElMessage.error("上传失败：" + res.msg);
      }
    },

    onDeleteFile(index) {
      let newAttachmentList = JSON.parse(JSON.stringify(this.state.dataForm.attachmentList));
      newAttachmentList = newAttachmentList.filter((item, ele) => {
        return index !== ele;
      });
      console.log(newAttachmentList);
      this.$emit("upload-field", newAttachmentList);
    },

    getOrgList() {
      listOrgByCurrentUser().then((res) => {
        if (res.code === 0) {
          this.state.orgList = res.data;
        }
      });
    },
  },
};

</script>
<style lang="scss" scoped>
.info {
  width: 100%;
  max-width: 1000px;
  margin-top: 10px;
  &_budget {
    :deep(.el-input__inner) {
      text-align: left;
    }
  }
  &_upload {
    &_list {
      &_item {
        display: flex;
        align-items: center;
        &_text {
          color: #409eff;
          margin-right: 10px;
          cursor: pointer;
        }
        &_action {
          color: #545252;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
