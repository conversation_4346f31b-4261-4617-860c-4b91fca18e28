<template>
  <el-card>
    <div class="invoice">
      <div class="invoice-action">
        <el-button type="primary" @click="onClickAction()">新增</el-button>
      </div>
      <div class="invoice-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <fast-table-column
            prop="titleType"
            label="抬头类型"
            header-align="center"
            align="center"
            dict-type="title_type"
          ></fast-table-column>
          <el-table-column
            prop="titleName"
            label="抬头名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="invoiceCode"
            label="税号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="landLineTel"
            label="手机号码"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="emailCode"
            label="电子邮箱"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="invoiceType"
            label="发票类型"
            header-align="center"
            align="center"
            dict-type="invoice_type1"
          ></fast-table-column>
          <el-table-column
            prop="createTime"
            label="创建日期"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-button type="primary" link @click="onClickAction(scope.row.id)">
                编辑
              </el-button>
              <el-button type="primary" link @click="deleteHandle(scope.row.id)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="invoice-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <AddOrUpdate
        :show="action.show"
        :id="action.id"
        :companyName="action.companyName"
        :email="action.email"
        :mobile="action.mobile"
        @close="onCloseAction"
        @refresh="getDataList"
      ></AddOrUpdate>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { reactive, onMounted } from "vue";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import AddOrUpdate from "./add-or-update.vue";
import { useUserInfoApi } from "@/api/sys/user";
import { ElMessage, ElMessageBox } from "element-plus";
import { deleteInvoiceInfo } from "@/api/supplier/order";

const state = reactive<IHooksOptions>({
  queryForm: {},
  dataListUrl: "/work/info/page",
  deleteUrl: "/work/info",
});

const deleteHandle = (key) => {
  ElMessageBox.confirm("确定进行删除操作?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      deleteInvoiceInfo(key).then(() => {
        ElMessage.success("删除成功");
        getDataList();
      });
    })
    .catch(() => {});
};

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

onMounted(() => {
  getUserDetail();
});

interface IAction {
  show: boolean;
  id: string;
  companyName: string;
  email: string;
  mobile: string;
}

const action = reactive<IAction>({
  show: false,
  id: "",
  companyName: "",
  email: "",
  mobile: "",
});

const onClickAction = (id?: string) => {
  action.id = id || "";
  action.show = true;
};

const onCloseAction = () => {
  action.show = false;
  action.id = "";
};

const getUserDetail = () => {
  useUserInfoApi().then((res) => {
    if (res.code == 0) {
      action.companyName = res.data?.userDetailsVO?.companyName ?? "";
      action.email = res.data?.userDetailsVO?.email ?? "";
      action.mobile = res.data?.userDetailsVO?.contactPhone ?? "";
    }
  });
};
</script>
<style lang="scss" scoped>
.invoice {
  &-list {
    margin-top: 16px;
  }
}
</style>
