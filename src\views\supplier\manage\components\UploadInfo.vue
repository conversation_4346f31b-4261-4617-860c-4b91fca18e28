<template>
  <div class="upload">
    <div class="upload-title">
      <div class="upload-title-left">
        <div class="upload-title-left-text">{{ props.title }}</div>
        <div class="upload-title-left-desc" v-if="props.showDesc">
          (仅支持jpg/png/pdf/zip格式)
        </div>
      </div>
      <div class="upload-title-right">
        <el-button type="primary">上传</el-button>
      </div>
    </div>
    <div class="upload-list">
      <el-table show-overflow-tooltip :data="state.dataList" border>
        <el-table-column
          type="index"
          label="序号"
          header-align="center"
          align="center"
          width="70"
        ></el-table-column>
        <el-table-column
          prop="userDetailsName"
          label="附件名称"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="contactName"
          label="操作"
          header-align="center"
          align="center"
        ></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive } from "vue";

interface IProps {
  title: string;
  showDesc?: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  title: "标题",
  showDesc: true,
});

const state = reactive({
  dataList: [],
});
</script>
<style lang="scss" scoped>
.upload {
  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 12px;
    padding-left: 8px;
    border-bottom: 1px solid #e4e7ed;
    &-left {
      display: flex;
      align-items: center;
      &-text {
        font-size: 16px;
        font-weight: 700;
        color: #333333;
      }
      &-desc {
        margin-left: 8px;
        font-size: 12px;
        color: #f56c6c;
      }
    }
  }
  &-list {
    margin-top: 16px;
  }
}
</style>
