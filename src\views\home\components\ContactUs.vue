<template>
  <div class="contact">
    <div class="contact-title">
      <HomeTitle icon="icon-daikaibiaoxiangmu" title="联系我们"> </HomeTitle>
    </div>
    <div class="contact-content">
      <div class="item">
        <div class="item-left">
          <svg-icon icon="icon-phone-" class-name="svg-size" />
        </div>
        <div class="item-right">
          <div class="item-right-label">客服咨询电话：</div>
          <div class="item-right-value">13020856116</div>
        </div>
      </div>
      <div class="item">
        <div class="item-left">
          <svg-icon icon="icon-phone-" class-name="svg-size" />
        </div>
        <div class="item-right">
          <div class="item-right-label">保证金咨询电话：</div>
          <div class="item-right-value">13020856116</div>
        </div>
      </div>
      <div class="item">
        <div class="item-left">
          <svg-icon icon="icon-time-circle" class-name="svg-size" />
        </div>
        <div class="item-right">
          <div class="item-right-label">工作时间：8:30—17:30</div>
          <div class="item-right-label" style="margin-top: 4px">工作日：周一至周五</div>
        </div>
      </div>
    </div>
    <div class="contact-icon">
      <el-image :src="ContractUs" class="contact-icon-img"></el-image>
    </div>
  </div>
</template>
<script setup lang="ts">
import HomeTitle from "./HomeTitle.vue";
import ContractUs from "@/assets/image/home/<USER>";
</script>
<style lang="scss" scoped>
.contact {
  background-color: #ffffff;
  padding: 16px 10px;
  padding-bottom: 100px;
  border-radius: 4px;
  position: relative;
  &-content {
    margin-top: 16px;
    margin-left: 32px;
    margin-right: 32px;
    .item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      &-left {
        width: 40px;
        height: 40px;
        border-radius: 4px;
        background-color: rgba(26, 144, 254, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        :deep(.svg-size) {
          font-size: 22px;
          color: rgba(26, 144, 254, 1);
        }
      }
      &-right {
        flex: 1;
        margin-left: 8px;
        &-label {
          font-size: 14px;
          color: #555555;
        }
        &-value {
          font-size: 16px;
          color: #333333;
          font-weight: 700;
          margin-top: 4px;
        }
      }
    }
  }
  &-icon {
    position: absolute;
    right: 10px;
    bottom: 0px;
    &-img {
      width: 208px;
      height: 170px;
    }
  }
}
</style>
