<template>
  <el-card>
    <div style="display: flex; justify-content: space-between">
      <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
        <el-form-item>
          <el-date-picker
            v-model="selectDate"
            type="daterange"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="onChangeDate"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-cascader
            :options="userData"
            :props="userProps"
            collapse-tags
            collapse-tags-tooltip
            clearable
            @change="userChange"
            placeholder="请选择员工"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList()">查询</el-button>
        </el-form-item>
      </el-form>
      <div>
        <el-button type="primary" @click="onClickExport()">导出</el-button>
      </div>
    </div>
    <el-table
      v-loading="state.dataListLoading"
      :data="state.dataList"
      border
      style="width: 100%"
      @selection-change="selectionChangeHandle"
    >
      <el-table-column
        prop="realName"
        label="员工"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="orgName"
        label="部门"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column prop="postNames" label="职位" header-align="center" align="center">
        <template #default="scope">
          <span>{{ scope.row.postNames?scope.row.postNames.join("、"):'' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="attendanceDate"
        label="考勤日期"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="workStartClockTime"
        label="上班打卡时间"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="workStartClockResult"
        label="上班打卡结果"
        header-align="center"
        align="center"
        dict-type="clock_result"
      >
        <template #default="scope">
          <div
            v-if="
              (scope.row.workStartClockResult ?? '') == '0' ||
              (scope.row.workStartClockResult ?? '') == '4' ||
              (scope.row.workStartClockResult ?? '') == '5'
            "
          >
            <span>{{
              getDictLabel(
                appStore.dictList,
                "clock_result",
                scope.row.workStartClockResult
              )
            }}</span>
          </div>
          <div
            v-else-if="scope.row.workStartType == '2'"
            style="cursor: pointer"
            @click="onClickRecord(scope.row, 'workStart')"
          >
            <span style="margin-right: 6px">{{
              getDictLabel(
                appStore.dictList,
                "clock_result",
                scope.row.workStartClockResult
              )
            }}</span
            ><span>(改)</span>
          </div>
          <div
            v-else
            style="color: rgb(245, 108, 108); cursor: pointer"
            @click="onClickEdit(scope.row, 'workStart')"
          >
            <span style="margin-right: 6px">{{
              getDictLabel(
                appStore.dictList,
                "clock_result",
                scope.row.workStartClockResult
              )
            }}</span>
            <el-icon><EditPen /></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="workEndClockTime"
        label="下班打卡时间"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="workEndClockResult"
        label="下班打卡结果"
        header-align="center"
        align="center"
      >
        <template #default="scope">
          <div
            v-if="scope.row.workEndType == '2'"
            style="cursor: pointer"
            @click="onClickRecord(scope.row, 'workEnd')"
          >
            <span style="margin-right: 6px">{{
              getDictLabel(
                appStore.dictList,
                "clock_result",
                scope.row.workEndClockResult
              )
            }}</span
            ><span>(改)</span>
          </div>
          <div
            v-else-if="
              (scope.row.workEndClockResult ?? '') == '0' ||
              (scope.row.workEndClockResult ?? '') == '4' ||
              (scope.row.workEndClockResult ?? '') == '5'
            "
          >
            <span>{{
              getDictLabel(
                appStore.dictList,
                "clock_result",
                scope.row.workEndClockResult
              )
            }}</span>
          </div>
          <div
            v-else
            style="color: rgb(245, 108, 108); cursor: pointer"
            @click="onClickEdit(scope.row, 'workEnd')"
          >
            <span style="margin-right: 6px">{{
              getDictLabel(
                appStore.dictList,
                "clock_result",
                scope.row.workEndClockResult
              )
            }}</span>
            <el-icon><EditPen /></el-icon>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="beLateFrequency"
        label="迟到次数"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="earlyDepartureFrequency"
        label="早退次数"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="workStartMissClockFrequency"
        label="上班缺卡次数"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="workEndMissClockFrequency"
        label="下班缺卡次数"
        header-align="center"
        align="center"
      ></el-table-column>
    </el-table>
    <el-pagination
      :current-page="state.pageNo"
      :page-sizes="state.pageSizes"
      :page-size="state.pageSize"
      :total="state.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    >
    </el-pagination>
    <el-dialog
      v-model="abnormal.show"
      :width="450"
      :destroy-on-close="true"
      :modal="false"
      title="异常处理"
      @close="onClickCancelAbnormal"
    >
      <div class="abnormal-content">
        <div class="abnormal-content-title">
          将 <span style="color: #2d78f4">{{ abnormal.name }}</span> 的下班记录改为
        </div>
        <div class="abnormal-content-select">
          <el-select v-model="abnormal.record" placeholder="请选择">
            <el-option
              v-for="item in abnormal.recordList"
              :key="item.dictValue"
              :label="item.dictLabel"
              :value="item.dictValue"
            ></el-option>
          </el-select>
        </div>
        <div class="abnormal-content-title" style="margin-top: 10px">修改原因</div>
        <div class="abnormal-content-textarea">
          <el-input
            type="textarea"
            :rows="3"
            placeholder="请输入内容"
            v-model="abnormal.reason"
            maxlength="50"
            show-count
          >
          </el-input>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onClickCancelAbnormal">取消</el-button>
          <el-button type="primary" @click="onClickConfirmAbnormal">确定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="record.show"
      :width="450"
      :destroy-on-close="true"
      :modal="false"
      title="修正记录"
      @close="onClickCancelRecord"
    >
      <div class="record-content">
        <el-form :model="record.form" label-width="100px">
          <el-form-item label="修正前">
            <div>
              {{
                getDictLabel(
                  appStore.dictList,
                  "clock_result",
                  record.form.workClockResult
                )
              }}
            </div>
          </el-form-item>
          <el-form-item label="修正后">
            <div>
              {{
                getDictLabel(
                  appStore.dictList,
                  "clock_result",
                  record.form.workClockResultAfter
                )
              }}
            </div>
          </el-form-item>
          <el-form-item label="修改意见">
            <div>
              {{ record.form.reason }}
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="onClickCancelRecord">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </el-card>
</template>

<script setup lang="ts">
import {
  exportDayAttendanceStatistics,
  getReason,
  updateDayAttendanceStatistics,
} from "@/api/check-in/day-statistics";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import dayjs from "dayjs";
import { onMounted, reactive, ref } from "vue";
import { getDictDataList, getDictLabel } from "@/utils/tool";
import { useAppStore } from "@/store/modules/app";
import { EditPen } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { getlistOrgUserTree } from "@/api/workbench";

const appStore = useAppStore();
const userProps = { value: "id", label: "name", children: "children", multiple: true };
const userData = ref([]);

const selectDate = ref();

const state: IHooksOptions = reactive({
  dataListUrl: "/work/dayAttendanceStatistics/page",
  deleteUrl: "/work/dayAttendanceStatistics",
  queryForm: {
    startTime: "",
    endTime: "",
    userId: "",
  },
});

onMounted(() => {
  getUserList();
});

const getUserList = () => {
  getlistOrgUserTree().then((res) => {
    if (res.code == 0) {
      userData.value = res.data;
    }
  });
};
const userChange = (event) => {
  console.log(event);
  if (event && event.length > 0) {
    let arr = [];
    event.map((item) => {
      arr.push(item[item.length - 1]);
    });
    state.queryForm.userId = arr.toString();
  } else {
    state.queryForm.userId = "";
  }
};

const onChangeDate = (value: any) => {
  if (value && value.length === 2) {
    state.queryForm.startTime = dayjs(value[0]).format("YYYY-MM-DD");
    state.queryForm.endTime = dayjs(value[1]).format("YYYY-MM-DD");
  } else {
    state.queryForm.startTime = "";
    state.queryForm.endTime = "";
  }
};

const onClickExport = () => {
  // let reqData = {
  //   startTime: state.queryForm.startTime,
  //   endTime: state.queryForm.endTime,
  // };
  exportDayAttendanceStatistics(state.queryForm).then((res) => {
    const url = window.URL.createObjectURL(new Blob([res.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "每日统计.xlsx");
    document.body.appendChild(link);
    link.click();
  });
};

const {
  getDataList,
  selectionChangeHandle,
  sizeChangeHandle,
  currentChangeHandle,
  deleteBatchHandle,
} = useCrud(state);

interface IAbnormal {
  show: boolean;
  type: string;
  row: any;
  name: string;
  record: string;
  recordList: any[];
  reason: string;
}

const abnormal = reactive<IAbnormal>({
  show: false,
  type: "",
  row: {},
  name: "",
  record: "",
  recordList: [],
  reason: "",
});

const onClickEdit = (row: any, type: string) => {
  abnormal.name = row.realName;
  let newRecord = "0";
  let newRecordList = getDictDataList(appStore.dictList, "clock_result");
  if (type === "workStart") {
    if (row.workStartClockResult == "3") {
      //缺卡
      newRecordList = newRecordList.filter((item: any) => {
        return item.dictValue != "2" && item.dictValue != "4";
      });
    } else if (row.workStartClockResult == "1") {
      //迟到
      newRecordList = newRecordList.filter((item: any) => {
        return item.dictValue != "1" && item.dictValue != "4" && item.dictValue != "2";
      });
    }
  } else {
    if (row.workEndClockResult == "3") {
      //缺卡
      newRecordList = newRecordList.filter((item: any) => {
        return item.dictValue != "1" && item.dictValue != "4";
      });
    } else if (row.workEndClockResult == "2") {
      //早退
      newRecordList = newRecordList.filter((item: any) => {
        return item.dictValue != "1" && item.dictValue != "4" && item.dictValue != "2";
      });
    }
  }
  abnormal.type = type;
  abnormal.row = row;
  abnormal.record = newRecord;
  abnormal.recordList = newRecordList;
  abnormal.reason = "";
  abnormal.show = true;
};

const onClickCancelAbnormal = () => {
  abnormal.show = false;
  abnormal.type = "";
  abnormal.row = {};
  abnormal.name = "";
  abnormal.record = "";
  abnormal.recordList = [];
  abnormal.reason = "";
};

const onClickConfirmAbnormal = () => {
  let reqData = {
    ...abnormal.row,
  };

  if (abnormal.type === "workStart") {
    reqData.workStartType = 2;
    reqData.oaDayAttendanceStatisticsReasonVO = {
      attendanceStatisticsId: abnormal.row.id,
      workClockResult: reqData.workStartClockResult,
      workClockResultAfter: abnormal.record,
      clockType: 0,
      reason: abnormal.reason,
    };
    reqData.workStartClockResult = abnormal.record;
  }
  if (abnormal.type === "workEnd") {
    reqData.workEndType = 2;
    reqData.oaDayAttendanceStatisticsReasonVO = {
      attendanceStatisticsId: abnormal.row.id,
      workClockResult: reqData.workEndClockResult,
      workClockResultAfter: abnormal.record,
      clockType: 1,
      reason: abnormal.reason,
    };
    reqData.workEndClockResult = abnormal.record;
  }

  updateDayAttendanceStatistics(reqData).then((res) => {
    if (res.code === 0) {
      getDataList();
      ElMessage.success("操作成功");
      onClickCancelAbnormal();
    }
  });
};

interface IRecord {
  show: boolean;
  id: string;
  form: any;
}

const record = reactive<IRecord>({
  show: false,
  id: "",
  form: {},
});

const onClickRecord = (row: any, type: string) => {
  getReason(row.id, type === "workStart" ? 0 : 1).then((res) => {
    console.log(res);
    if (res.code === 0) {
      record.show = true;
      record.id = row.id;
      if (res.data.length > 0) {
        record.form = res.data[0];
      }
    }
  });
};
const onClickCancelRecord = () => {
  record.show = false;
  record.id = "";
  record.form = {};
};
</script>
<style lang="scss" scoped>
.abnormal-content {
  &-select {
    margin-top: 10px;
  }
  &-textarea {
    margin-top: 10px;
  }
}
</style>
