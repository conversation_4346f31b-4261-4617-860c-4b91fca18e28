<template>
  <el-card>
    <div class="notice">
      <div class="notice-search">
        <el-form
          ref="elFormRef"
          :inline="true"
          :model="state.queryForm"
          @keyup.enter="getDataList()"
        >
          <el-form-item prop="contractName">
            <el-input
              placeholder="合同名称"
              clearable
              v-model="state.queryForm.contractName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item prop="contractNo">
            <el-input
              placeholder="合同编号"
              clearable
              v-model="state.queryForm.contractNo"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="resetForm()">重置</el-button>
            <el-button type="primary" @click="newAddContract()">新增合同</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="notice-list">
        <el-table
          v-loading="state.dataListLoading"
          :data="state.dataList"
          @selection-change="handleSelectionChange"
          border
        >
          <el-table-column type="selection" width="55"> </el-table-column>
          <el-table-column
            prop="contractName"
            label="合同名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="contractNo"
            label="合同编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="youSubjectName"
            label="对方主体名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="startTime"
            label="合同开始日期"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="endTime"
            label="合同结束日期"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="signType"
            label="用章类型"
            header-align="center"
            align="center"
          >
            <template #default="scope">
              <span v-if="(scope.row.signType ?? '') === ''">-</span>
              <span v-else>
                {{
                  signTypeList
                    .filter(
                      (a) =>
                        (scope.row.signType ?? "")
                          .split(",")
                          .findIndex((item) => item === a.dictValue) !== -1
                    )
                    .map((ele) => {
                      return ele.dictLabel;
                    })
                    .join(",")
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-button type="primary" link @click="lookcontract(scope.row.id, 1)"
                >查看</el-button
              >
              <el-button
                type="primary"
                v-if="scope.row.attachmentList"
                link
                @click="downloadFile(scope.row.attachmentList[0].url, '')"
              >
                下载
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="notice-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { nextTick, reactive, ref } from "vue";
import { useRouter } from "vue-router";
import { downloadFile, getDictDataList } from "@/utils/tool";
import { useAppStore } from "@/store/modules/app";
const appStore = useAppStore();
const signTypeList = ref(getDictDataList(appStore.dictList, "sign_type"));
const state: IHooksOptions = reactive({
  dataListUrl: "/work/contract/flow/info/page",
  queryForm: {
    contractName: "",
    contractNo: "",
  },
  auditlogShow: false,
  value: "",
});
const router = useRouter();
const elFormRef = ref();
const handleSelectionChange = (val) => {
  console.info(val);
};
// 查看合同
const lookcontract = (id, type) => {
  router.push("/contractLib/lookContract?id=" + id + "&type=" + type);
};

const resetForm = () => {
  elFormRef.value.resetFields();
  getDataList(1);
};
// 新增合同
const newAddContract = () => {
  router.push("/workbench/index?activeTab=6");
};

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);
</script>
<style lang="scss" scoped>
.notice {
  &-list {
    margin-top: 16px;
  }
}
</style>
