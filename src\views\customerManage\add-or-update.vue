<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.id ? '新建客户' : '修改客户'"
    :close-on-click-modal="false"
    destroy-on-close
    width="1000px"
  >
    <el-form
      ref="dataFormRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="130px"
      @keyup.enter="submitHandle()"
    >
      <div class="bartitle">基础信息</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="customerName" label="客户名称">
            <el-input v-model="dataForm.customerName" placeholder="客户名称" clearable></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="customerType" label="客户类型">
            <fast-select
              style="width: 100%"
              v-model="dataForm.customerType"
              dict-type="customer_type"
              clearable
              placeholder="客户类型"
            ></fast-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="customerTags" label="客户标签">
            <fast-select
              style="width: 100%"
              v-model="dataForm.customerTags"
              dict-type="customer_tags"
              clearable
              placeholder="客户标签"
            ></fast-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="customerSource" label="客户来源">
            <fast-select
              style="width: 100%"
              v-model="dataForm.customerSource"
              dict-type="customer_source"
              clearable
              placeholder="客户来源"
            ></fast-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="address" label="客户地址">
            <el-input
              v-model="dataForm.address"
              placeholder="客户地址"
              maxlength="40"
              show-word-limit
              clearable
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="nextFollowTime" label="下次跟进时间">
            <el-date-picker
              type="datetime"
              v-model="dataForm.nextFollowTime"
              placeholder="下次跟进时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              time-format="HH:mm"
              style="width: 100%"
              clearable
            ></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="remark" label="备注">
            <el-input
              clearable
              v-model="dataForm.remark"
              type="textarea"
              :autosize="{ minRows: 3 }"
              placeholder="请输入备注"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="bartitle">联系人</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item prop="linkMan" label="联系人">
            <el-input v-model="dataForm.linkMan" placeholder="联系人" clearable maxlength="30"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="linkPhone" label="联系电话">
            <el-input v-model="dataForm.linkPhone" placeholder="联系电话" clearable maxlength="15"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()" :loading="submitLoad"
        >确定</el-button
      >
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, ref } from "vue";
import { ElMessage } from "element-plus/es";
import {
  customerSubmitApi,
  customerInfoApi,
} from "@/api/customerManage/common";
import dayjs from "dayjs";
import { validateMobile } from "@/utils/validate";
const emit = defineEmits(["refreshDataList"]);

const visible = ref(false);
const dataFormRef = ref();
const submitLoad = ref(false);
const dataForm = reactive({
  id: "",
  customerName: "",
  customerType: "",
  customerTags: "",
  customerSource: "",
  address:"",
  nextFollowTime: "",
  remark:"",
  linkMan: "",
  linkPhone: "",
});
const init = (row) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  
  // id 存在则为修改
  if (row) {
    getCustomerInfo(row.id);
  }
};

const getCustomerInfo = (id) => {
  customerInfoApi(id).then((res) => {
    let resData=res.data
    Object.assign(dataForm, resData);
  });
};

const dataRules = ref({
  customerName: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  linkMan: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  linkPhone: [
    { required: true, message: "必填项不能为空", trigger: "blur" },
    { validator: validateMobile, trigger: ["blur", "change"] },
  ],
});

// 表单提交
const submitHandle = () => {
  dataFormRef.value.validate((valid) => {
    if (!valid) {
      return false;
    }
    submitLoad.value = true;
    let params = JSON.parse(JSON.stringify(dataForm));
    params.nextFollowTime = params.nextFollowTime
      ? dayjs(params.nextFollowTime + ":00").format("YYYY-MM-DD HH:mm:ss")
      : "";
    customerSubmitApi(params)
      .then((res) => {
        submitLoad.value = false;
        if(res.code===0){
          ElMessage.success({
            message: "操作成功",
            duration: 500,
            onClose: () => {
              visible.value = false;
              if (dataForm.id) {
                emit("refreshDataList", "1");
              } else {
                emit("refreshDataList");
              }
            },
          });
        }
      })
      .catch(() => {
        submitLoad.value = false;
      });
  });
};

defineExpose({
  init,
});
</script>
<style scoped lang="scss">
.bartitle {
  height: 15px;
  line-height: 15px;
  position: relative;
  padding-left: 10px;
  margin-bottom: 10px;
}

.bartitle::before {
  content: "";
  width: 5px;
  height: 15px;
  position: absolute;
  top: 0;
  left: 0;
  background-color: #409EFF;
}
</style>