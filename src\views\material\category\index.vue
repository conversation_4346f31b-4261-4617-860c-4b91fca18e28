<template>
  <el-card>
    <el-form :inline="true">
      <el-form-item>
        <el-button
          type="primary"
          @click="addOrUpdateHandle(false, null)"
          v-auth="'purchase:category:save'"
        >
          新增
        </el-button>
      </el-form-item>
      <el-form-item>
        <el-button plain @click="toggleExpandAll()">
          <template v-if="!isExpandAll">
            全部展开&nbsp;<el-icon><ArrowDown /></el-icon>
          </template>
          <template v-else>
            全部收起&nbsp;<el-icon><ArrowUp /></el-icon>
          </template>
        </el-button>
      </el-form-item>
    </el-form>
    <el-table
      v-if="refreshTable"
      v-loading="state.dataListLoading"
      :default-expand-all="isExpandAll"
      :data="state.dataList"
      row-key="id"
      border
      style="width: 100%"
    >
      <el-table-column
        prop="name"
        label="物料分类名称"
        header-align="center"
      ></el-table-column>
      <el-table-column
        prop="parentName"
        label="上级"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="sort"
        label="排序"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        header-align="center"
        align="center"
        width="160"
      >
        <template #default="scope">
          <el-button
            type="primary"
            link
            @click="addOrUpdateHandle(false, scope.row)"
            v-auth="'purchase:category:save'"
            v-if="(scope.row.pid || 0) == 0"
          >
            新增
          </el-button>
          <el-button
            type="primary"
            link
            @click="addOrUpdateHandle(true, scope.row)"
            v-auth="'purchase:category:update'"
          >
            修改
          </el-button>
          <el-button
            type="primary"
            link
            @click="deleteHandle(scope.row.id)"
            v-auth="'purchase:category:delete'"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <add-or-update
      :show="action.show"
      :id="action.id"
      :info="action.info"
      @on-close="onCloseAction"
      @on-submit="onSubmitAction"
    ></add-or-update>
  </el-card>
</template>

<script setup lang="ts">
import { useCrud } from "@/hooks";
import { nextTick, reactive, ref } from "vue";
import AddOrUpdate from "./add-or-update.vue";
import { IHooksOptions } from "@/hooks/interface";
import { ArrowDown, ArrowUp } from "@element-plus/icons-vue";

const state = reactive<IHooksOptions>({
  dataListUrl: "/purchase/category/tree",
  deleteUrl: "/purchase/category",
  isPage: false,
});

interface IDataForm {
  name: string;
  pid?: number;
  parentName?: string;
  sort: number;
}

interface IAction {
  show: boolean;
  id?: number;
  info: IDataForm;
}

const action = reactive<IAction>({
  show: false,
  id: void 0,
  info: {
    name: "",
    pid: void 0,
    parentName: void 0,
    sort: 0,
  },
});

const addOrUpdateHandle = (isUpdate: Boolean, row: any) => {
  if (row?.id) {
    if (isUpdate) {
      action.id = row.id;
      let newInfo = {
        name: row.name,
        pid: row?.pid,
        parentName: row?.parentName,
        sort: row.sort,
      };
      action.info = newInfo;
    } else {
      let newInfo = {
        name: "",
        pid: row?.id,
        parentName: void 0,
        sort: 0,
      };
      action.id = void 0;
      action.info = newInfo;
    }
  }
  action.show = true;
};

const onCloseAction = () => {
  action.show = false;
  action.id = void 0;
  let newInfo = {
    name: "",
    pid: void 0,
    parentName: void 0,
    sort: 0,
  };
  action.info = newInfo;
};

const onSubmitAction = () => {
  getDataList();
  onCloseAction();
};

const { getDataList, deleteHandle } = useCrud(state);

// 是否展开，默认全部折叠
const isExpandAll = ref(false);

// 是否重新渲染表格状态
const refreshTable = ref(true);

// 切换 展开和折叠
const toggleExpandAll = () => {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
};
</script>
