<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-02 16:25:21
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-17 14:52:10
-->
<template>
  <el-drawer
    v-model="state.visible"
    title="审核记录"
    size="50%"
  >
  <el-timeline style="max-width: 600px">
    <el-timeline-item
      v-for="(item, index) in state.logs"
      :key="index"
      type="primary"
      :hollow="true"
    >
      <div class="log">
        <p class="log-state">
          <span v-if="item.auditResult=='01'"><el-icon class="line" color="#0bbd87" size='16'><CircleCheckFilled /></el-icon>审核通过</span>
          <span v-if="item.auditResult=='00'"><el-icon class="line" color="red" size='16'><CircleCloseFilled /></el-icon>审核未通过</span>
        </p>
        <div class="log-gray">
          <p class="p1">审核人：{{item.orgName}} - {{ item.reviewedBy }}</p>
          <p class="p2">审核时间：{{ item.createTime }}</p>
          <p class="p2">审核意见：{{ item.opinion }}</p>
        </div>
      </div>
    </el-timeline-item>
  </el-timeline>
    <template #footer>
      <div class="dialog-footer">
        <el-button class='btn' @click="state.visible = false">关闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import { reactive } from "vue"
import { useGetAuditRecordsByIdApi } from '@/api/pnotice'
import { ElMessage } from 'element-plus'
import { CircleCheckFilled,CircleCloseFilled } from '@element-plus/icons-vue'

const state = reactive({
  visible: false,
  logs:[]
})

const init = (id)=>{
  state.visible = true
  state.nowId = id
  getDetail(id)
}
const getDetail = (id)=>{
  useGetAuditRecordsByIdApi(id).then((res) => {
    if(res.code == 0){
      state.logs = res.data
    }
  })
}

defineExpose({
	init
})
</script>
<style lang="scss" scoped>
.block{display:block;}
.btn{margin-left: 12px;}
.log{
  &-state{
    .line{
      vertical-align: text-bottom;
      margin-right: 4px;
    }
  }
  &-gray{
    background-color: rgb(240, 240, 240);
    margin-top: 10px;
    padding: 10px;
    p{
      line-height: 1.8;
    }
    .p2{
      color:#9e9e9e;
    }
  }
}
</style>
