import service from '@/utils/request'

interface IAttachmentItem {
  name: string;
  url: string;
  size: number;
  platform: string;
}

export interface ISavaWinResultNotice {
  id?: number
  packageId?: number
  packageName: string
  packageNo: string
  packagePeople: string
  title: string
  winBidderId?: number
  winBidder: string
  content: string
  status: string
  attachmentList: IAttachmentItem[]
}

export const savaWinResultNotice = (reqData: ISavaWinResultNotice) => {
  return service.post('/purchase/winResultNotice', reqData)
}

export const updateWinResultNotice = (reqData: ISavaWinResultNotice) => {
  return service.put('/purchase/winResultNotice', reqData)
}


//获取中标通知书详情
export const getWinResultInfo = (id: number) => {
  return service.get(`/purchase/winResultNotice/${id}`)
}
