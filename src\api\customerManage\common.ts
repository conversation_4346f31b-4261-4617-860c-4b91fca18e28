import service from '@/utils/request'
export const customerSubmitApi = (dataForm: any) => {
	if (dataForm.id) {
		return service.put('/work/customer', dataForm)
	} else {
		return service.post('/work/customer', dataForm)
	}
}

export const customerInfoApi = (id: any) => {
	return service.get('/work/customer/' + id)
}

export const deleteCustomerApi = (ids: any) => {
	return service.delete('/work/customer', {
		data: ids
	})
}

export const getCustomerFollowRecordApi = (dataForm: any) => {
	return service.get(`/work/customerFollowRecord/findList?customerId=${dataForm.customerId}&followType=${dataForm.followType}&startDate=${dataForm.startDate}&endDate=${dataForm.endDate}`)
}
export const customerFollowApi = (dataForm: any) => {
	return service.post('/work/customerFollowRecord', dataForm)
}

// 客户历史记录
export const findCustomerLogApi = (id: any) => {
	return service.get(`/work/customer/findCustomerLog/${id}`)
}

export const transferHeaderApi = (reqData: any) => {
	return service.put(`/work/customer/transferHeader`, reqData)
}