<template>
  <el-card>
    <div class="watch">
      <div class="watch-info">
        <div class="watch-info-title">【20230316111111】办公用品采购</div>
        <div class="watch-info-desc">
          <div class="watch-info-desc-item">采购方式：公开招标</div>
          <div class="watch-info-desc-divider"></div>
          <div class="watch-info-desc-item">报价开始时间：2024-03-18 09:00:00</div>
          <div class="watch-info-desc-divider"></div>
          <div class="watch-info-desc-item">报价结束时间：2024-03-20 09:00:00</div>
        </div>
      </div>
      <div class="watch-divider"></div>
      <div class="watch-list">
        <div class="watch-list-title">参与列表</div>
        <div class="watch-list-table">
          <el-table
            v-loading="state.dataListLoading"
            show-overflow-tooltip
            :data="state.dataList"
            border
          >
            <el-table-column
              type="index"
              label="序号"
              header-align="center"
              align="center"
              width="70"
            ></el-table-column>
            <el-table-column
              prop="realName"
              label="供应商"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="gender"
              label="联系人"
              dict-type="user_gender"
            ></el-table-column>
            <el-table-column
              prop="mobile"
              label="联系电话"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="orgName"
              label="参与审核状态"
              header-align="center"
              align="center"
            ></el-table-column>

            <el-table-column
              label="操作"
              fixed="right"
              header-align="center"
              align="center"
              width="120"
            >
              <template #default="scope">
                <el-button
                  v-auth="'sys:user:update'"
                  type="primary"
                  link
                  @click="addOrUpdateHandle(scope.row.id)"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { reactive } from "vue";
const state = reactive({
  dataListLoading: false,
  dataList: [],
});
</script>
<style lang="scss" scoped>
.watch {
  &-info {
    &-title {
      font-size: 18px;
      color: #030303;
    }
    &-desc {
      margin-top: 6px;
      display: flex;
      align-items: center;
      &-item {
        font-size: 14px;
        color: #949494;
      }
      &-divider {
        margin: 0 8px;
        width: 1px;
        height: 12px;
        background-color: #e8e8e8;
      }
    }
  }
  &-divider {
    width: 100%;
    height: 1px;
    background-color: #e8e8e8;
    margin: 16px 0;
  }
  &-list {
    &-title {
      color: #030303;
    }
  }
}
</style>
