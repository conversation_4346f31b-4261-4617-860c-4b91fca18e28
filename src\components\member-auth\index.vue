<template>
  <el-card>
    <div class="action">
      <template v-if="state.subjectType === '02'">
        <div class="action-base">
          <div class="action-base-title">
            <ContentTitle title="基本信息"></ContentTitle>
          </div>
          <div class="action-base-content">
            <PersonBaseInfo
              :personBaseInfo="state.personBaseInfo"
              :action="allowEdit ? 'edit' : 'watch'"
              @emit-ref="onEmitPersonBaseInfoRef"
              @on-change-value="onChangeValue"
            ></PersonBaseInfo>
          </div>
        </div>
        <div class="action-certification">
          <UploadInfo
            title="其他资质信息"
            :showDesc="false"
            :fileList="state.certificationInfo"
            type="QTZZ"
            @on-change-value="onChangeValue"
            :action="allowEdit ? 'edit' : 'watch'"
          ></UploadInfo>
        </div>
        <div class="action-other">
          <OtherFile
            :fileList="state.otherFileList"
            :action="allowEdit ? 'edit' : 'watch'"
            @emit-ref="onEmitOtherFileRef"
            @on-change-value="onChangeValue"
          ></OtherFile>
        </div>
      </template>
      <template v-else-if="state.subjectType === '01'">
        <div class="action-base">
          <div class="action-base-title">
            <ContentTitle title="基本信息"></ContentTitle>
          </div>
          <div class="action-base-content">
            <BaseInfo
              :baseInfo="state.baseInfo"
              :action="allowEdit ? 'edit' : 'watch'"
              @emit-ref="onEmitBaseInfoRef"
              @on-change-value="onChangeValue"
            ></BaseInfo>
          </div>
        </div>
        <div class="action-contact">
          <div class="action-contact-title">
            <ContentTitle title="联系人信息"></ContentTitle>
          </div>
          <div class="action-contact-content">
            <ContactInfo
              :contactInfo="state.contactInfo"
              :action="allowEdit ? 'edit' : 'watch'"
              @emit-ref="onEmitContactInfoRef"
              @on-change-value="onChangeValue"
            ></ContactInfo>
          </div>
        </div>
        <div class="action-certification">
          <UploadInfo
            title="企业资质信息"
            :showDesc="false"
            :fileList="state.certificationInfo"
            type="QYZZ"
            @on-change-value="onChangeValue"
            :action="allowEdit ? 'edit' : 'watch'"
          ></UploadInfo>
        </div>
        <div class="action-auth">
          <div class="action-auth-title">
            <ContentTitle title="企业授权委托书"></ContentTitle>
          </div>
          <div class="action-auth-content">
            <LetterOfAuth
              :letterOfAuth="state.letterOfAuth"
              :action="allowEdit ? 'edit' : 'watch'"
              @emit-ref="onEmitLetterOfAuthRef"
              @on-change-value="onChangeValue"
            ></LetterOfAuth>
          </div>
        </div>
        <div class="action-case">
          <UploadInfo
            title="业绩"
            :action="allowEdit ? 'edit' : 'watch'"
            :fileList="state.caseInfo"
            :acceptFileType="['jpg', 'png', 'pdf', 'zip', 'jpeg']"
            @on-change-value="onChangeValue"
            type="ALXX"
          ></UploadInfo>
        </div>
        <div class="action-honor">
          <UploadInfo
            title="企业荣誉"
            :action="allowEdit ? 'edit' : 'watch'"
            :fileList="state.honorInfo"
            :acceptFileType="['jpg', 'png', 'pdf', 'zip', 'jpeg']"
            @on-change-value="onChangeValue"
            type="QYRY"
          ></UploadInfo>
        </div>
        <div class="action-other">
          <OtherFile
            :fileList="state.otherFileList"
            :action="allowEdit ? 'edit' : 'watch'"
            @emit-ref="onEmitOtherFileRef"
            @on-change-value="onChangeValue"
          ></OtherFile>
        </div>
      </template>
      <div class="action-audit" v-if="props.from === 'manage'">
        <div class="action-audit-title">
          <ContentTitle title="审核意见"></ContentTitle>
        </div>
        <div class="action-audit-content">
          <AuditList :bizId="state.purchaserBidderId"></AuditList>
        </div>
      </div>
      <div class="action-btn" v-if="props.from === 'member'">
        <el-button type="primary" @click="onClickSubmit('1')">提交</el-button>
        <el-button plain type="primary" @click="onClickSubmit('0')">暂存</el-button>
      </div>
      <div class="action-btn" v-if="props.from === 'manage'">
        <el-button type="primary" @click="onClickAudit()" v-if="props.type === 'audit'">
          审核
        </el-button>
        <el-button @click="onClickReturn">返回</el-button>
      </div>
      <el-dialog
        v-model="audit.show"
        title="审核"
        :width="450"
        :close-on-click-modal="false"
        draggable
        @close="onCloseAudit"
      >
        <el-form
          ref="auditRef"
          :model="audit.dataForm"
          :rules="audit.dataRules"
          label-width="80px"
          class="info-form"
        >
          <el-form-item prop="auditStatus" label="审核结果">
            <el-radio-group v-model="audit.dataForm.auditStatus">
              <el-radio key="01" label="01">通过</el-radio>
              <el-radio key="00" label="00">不通过</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="content" label="审核意见">
            <el-input
              v-model="audit.dataForm.content"
              placeholder="审核意见"
              type="textarea"
              :rows="4"
            ></el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="onCloseAudit">取消</el-button>
            <el-button type="primary" @click="onSubmitAudit">确定</el-button>
          </div>
        </template>
      </el-dialog>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import PersonBaseInfo, { IPersonBaseInfo } from "./components/PersonBaseInfo.vue";
import BaseInfo, { IBaseInfo } from "./components/BaseInfo.vue";
import ContactInfo, { IContactInfo } from "./components/ContactInfo.vue";
import LetterOfAuth, { ILetterOfAuth } from "./components/LetterOfAuth.vue";
import UploadInfo, { IAttachmentItem } from "./components/UploadInfo.vue";
import OtherFile from "./components/OtherFile.vue";
import AuditList from "./components/AuditList.vue";
import { reactive, ref, onMounted, watch, computed } from "vue";
import { ElMessage, FormRules } from "element-plus";
import {
  actionUserDetail,
  auditSupplier,
  getUserDetailInfo,
  IActionUserDetail,
  IAuditSupplier,
} from "@/api/sys/user";
import dayjs from "dayjs";
import { closeTab } from "@/utils/tabs";
import { useRoute, useRouter } from "vue-router";
import { useUserStore } from "@/store/modules/user";

const userStore = useUserStore();

const router = useRouter();
const route = useRoute();

//#region props相关
interface IProps {
  from: string;
  type?: string;
  userDetailsId?: number;
  purchaserBidderId?: number;
}

const props = withDefaults(defineProps<IProps>(), {
  from: "member",
});
//#endregion

//#region state相关
interface IState {
  detailsId?: number;
  purchaserBidderId?: number;
  subjectType: string;
  personBaseInfo: IPersonBaseInfo;
  baseInfo: IBaseInfo;
  contactInfo: IContactInfo;
  certificationInfo: IAttachmentItem[];
  letterOfAuth: ILetterOfAuth;
  caseInfo: IAttachmentItem[];
  honorInfo: IAttachmentItem[];
  otherFileList: IAttachmentItem[];
}

const state = reactive<IState>({
  detailsId: void 0,
  purchaserBidderId: void 0,
  subjectType: "",
  personBaseInfo: {
    contactName: "",
    auditStatus: "",
    idNumber: "",
    contactPhone: "",
    email: "",
    address: "",
    openingBank: "",
    accountNumber: "",
    zipCode: "",
    authorizedIdCardFront: "",
    authorizedIdCardBack: "",
  },
  baseInfo: {
    companyName: "",
    auditStatus: "",
    creditCode: "",
    industry: "",
    areaId: "",
    legalPerson: "",
    registeredCapital: void 0,
    establishTime: "",
    qualificationOne:[],
    workAddress: "",
    introduction: "",
    businessScope: "",
  },
  contactInfo: {
    contactName: "",
    contactPhone: "",
    idNumber: "",
    address: "",
    email: "",
    zipCode: "",
  },
  certificationInfo: [],
  letterOfAuth: {
    authorizationLetter: "",
    authorizedIdCardFront: "",
    authorizedIdCardBack: "",
  },
  caseInfo: [],
  honorInfo: [],
  otherFileList: [],
});
//#endregion

const GetUserDetailInfo = () => {
  if (state.detailsId) {
    getUserDetailInfo(state.detailsId).then((res: any) => {
      if (res.code === 0) {
        state.subjectType = res.data.subjectType;
        if (state.subjectType == "02") {
          let newPersonBaseInfo = {
            contactName: res.data.contactName,
            auditStatus: res.data.auditStatus,
            idNumber: res.data.idNumber ?? "",
            contactPhone: res.data.contactPhone ?? "",
            email: res.data.email ?? "",
            address: res.data.address ?? "",
            openingBank: res.data.openingBank ?? "",
            accountNumber: res.data.accountNumber ?? "",
            zipCode: res.data.zipCode ?? "",
            authorizedIdCardFront: res.data.authorizedIdCardFront ?? "",
            authorizedIdCardBack: res.data.authorizedIdCardBack ?? "",
          };
          console.log(newPersonBaseInfo);
          state.personBaseInfo = newPersonBaseInfo;
          let newCertificationInfo = (res.data?.attachmentList ?? [])
            .filter((item: any) => {
              return item.type === "QTZZ";
            })
            .map((item: any) => {
              return {
                name: item.name,
                url: item.url,
                size: item.size,
                type: item.type,
                platform: item.platform,
              };
            });
          state.certificationInfo = newCertificationInfo;
        } else if (state.subjectType == "01") {
          let qualificationOne=[]
          if(res.data.qualificationOne){
            qualificationOne=JSON.parse(res.data.qualificationOne)
          }
          let newBaseInfo = {
            companyName: res.data.companyName,
            auditStatus: res.data.auditStatus,
            creditCode: res.data.creditCode,
            industry: res.data.industry || "",
            areaId: res.data.areaId || "",
            legalPerson: res.data.legalPerson,
            registeredCapital: res.data.registeredCapital,
            establishTime: res.data.establishTime,
            qualificationOne:qualificationOne,
            workAddress: res.data.workAddress,
            introduction: res.data.introduction,
            businessScope: res.data.businessScope,
          };
          state.baseInfo = newBaseInfo;
          let newContactInfo = {
            contactName: res.data.contactName,
            contactPhone: res.data.contactPhone,
            idNumber: res.data.idNumber,
            address: res.data.address,
            email: res.data.email,
            zipCode: res.data.zipCode,
          };
          state.contactInfo = newContactInfo;
          let newCertificationInfo = (res.data?.attachmentList ?? [])
            .filter((item: any) => {
              return item.type === "QYZZ";
            })
            .map((item: any) => {
              return {
                name: item.name,
                url: item.url,
                size: item.size,
                type: item.type,
                platform: item.platform,
              };
            });
          state.certificationInfo = newCertificationInfo;
          let newLetterOfAuth = {
            authorizationLetter: res.data.authorizationLetter,
            authorizedIdCardFront: res.data.authorizedIdCardFront,
            authorizedIdCardBack: res.data.authorizedIdCardBack,
          };
          state.letterOfAuth = newLetterOfAuth;
          let newCaseInfo = (res.data?.attachmentList ?? [])
            .filter((item: any) => {
              return item.type === "ALXX";
            })
            .map((item: any) => {
              return {
                name: item.name,
                url: item.url,
                size: item.size,
                type: item.type,
                platform: item.platform,
              };
            });
          state.caseInfo = newCaseInfo;
          let newHonorInfo = (res.data?.attachmentList ?? [])
            .filter((item: any) => {
              return item.type === "QYRY";
            })
            .map((item: any) => {
              return {
                name: item.name,
                url: item.url,
                size: item.size,
                type: item.type,
                platform: item.platform,
              };
            });
          state.honorInfo = newHonorInfo;
          let newOtherFileList = (res.data?.attachmentList ?? [])
            .filter((item: any) => {
              return item.type === "FWFPZ";
            })
            .map((item: any) => {
              return {
                name: item.name,
                url: item.url,
                size: item.size,
                type: item.type,
                platform: item.platform,
              };
            });
          state.otherFileList = newOtherFileList;
        }
      }
    });
  }
};

watch(
  [() => props.from, () => props.type, () => props.userDetailsId],
  () => {
    if (props.from === "member") {
      if (props.userDetailsId) {
        state.detailsId = props.userDetailsId;
        GetUserDetailInfo();
      }
    } else if (props.from === "manage") {
      if (props.userDetailsId) {
        state.detailsId = props.userDetailsId;
        state.purchaserBidderId = props.purchaserBidderId;
        GetUserDetailInfo();
      }
    }
  },
  { immediate: true }
);

const allowEdit = computed(() => {
  let allow = true;
  if (state.subjectType === "01" && state.baseInfo.auditStatus == "2") {
    allow = false;
  }
  if (state.subjectType === "02" && state.personBaseInfo.auditStatus == "2") {
    allow = false;
  }
  if (props.from === "manage") {
    allow = false;
  }
  return allow;
});

const onChangeValue = (type: string, field: string, value: any) => {
  switch (type) {
    case "personBaseInfo":
      let newPersonBaseInfo = JSON.parse(JSON.stringify(state.personBaseInfo));
      newPersonBaseInfo[field] = value;
      state.personBaseInfo = newPersonBaseInfo;
      break;
    case "baseInfo":
      let newBaseInfo = JSON.parse(JSON.stringify(state.baseInfo));
      newBaseInfo[field] = value;
      state.baseInfo = newBaseInfo;
      break;
    case "contactInfo":
      let newContactInfo = JSON.parse(JSON.stringify(state.contactInfo));
      newContactInfo[field] = value;
      state.contactInfo = newContactInfo;
      break;
    case "certificationInfo":
      let newCertificationInfo = JSON.parse(JSON.stringify(state.certificationInfo));
      newCertificationInfo = value;
      state.certificationInfo = newCertificationInfo;
      break;
    case "letterOfAuth":
      let newLetterOfAuth = JSON.parse(JSON.stringify(state.letterOfAuth));
      newLetterOfAuth = value;
      state.letterOfAuth = newLetterOfAuth;
      break;
    case "caseInfo":
      let newCaseInfo = JSON.parse(JSON.stringify(state.caseInfo));
      newCaseInfo = value;
      state.caseInfo = newCaseInfo;
      break;
    case "honorInfo":
      let newHonorInfo = JSON.parse(JSON.stringify(state.honorInfo));
      newHonorInfo = value;
      state.honorInfo = newHonorInfo;
      break;
    case "otherFileList":
      let newOtherFileList = JSON.parse(JSON.stringify(state.otherFileList));
      newOtherFileList = value;
      state.otherFileList = newOtherFileList;
      break;
    default:
      break;
  }
};

//#region PersonBaseInfo相关
const personBaseInfoRef = ref();
const onEmitPersonBaseInfoRef = (newPersonBaseInfoRef: any) => {
  personBaseInfoRef.value = newPersonBaseInfoRef;
};

//#endregion

//#region BaseInfo相关
const baseInfoRef = ref();

const onEmitBaseInfoRef = (newBaseInfoRef: any) => {
  baseInfoRef.value = newBaseInfoRef;
};
//#endregion

//#region ContactInfo
const contactInfoRef = ref();
const onEmitContactInfoRef = (newContactInfoRef: any) => {
  contactInfoRef.value = newContactInfoRef;
};
//#endregion

//#region LetterOfAuth

const letterOfAuthRef = ref();
const onEmitLetterOfAuthRef = (newLetterOfAuthRef: any) => {
  letterOfAuthRef.value = newLetterOfAuthRef;
};
//#endregion

//#region OtherFile
const otherFileRef = ref();
const onEmitOtherFileRef = (newOtherFileRef: any) => {
  otherFileRef.value = newOtherFileRef;
};
//#endregion

const onClickSubmit = async (submit: string) => {
  let flag = true;
  if (state.subjectType === "02") {
    try {
      await personBaseInfoRef.value.validate();
    } catch (err) {
      flag = false;
    }
    try {
      await otherFileRef.value.validate();
    } catch (err) {
      flag = false;
    }
  } else if (state.subjectType === "01") {
    try {
      await baseInfoRef.value.validate();
    } catch (err) {
      flag = false;
    }
    try {
      await contactInfoRef.value.validate();
    } catch (err) {
      flag = false;
    }
    try {
      await letterOfAuthRef.value.validate();
    } catch (err) {
      flag = false;
    }
    try {
      await otherFileRef.value.validate();
    } catch (err) {
      flag = false;
    }
  }
  console.log("flag", flag);
  if (flag) {
    let newAttachmentList: IAttachmentItem[] = [];
    let reqData: IActionUserDetail;
    if (state.subjectType === "02") {
      if (state.certificationInfo.length > 0) {
        newAttachmentList = newAttachmentList.concat(state.certificationInfo);
      }
      if (state.otherFileList.length > 0) {
        newAttachmentList = newAttachmentList.concat(state.otherFileList);
      }
      reqData = {
        id: state.detailsId,
        subjectType: state.subjectType,
        contactName: state.personBaseInfo.contactName,
        idNumber: state.personBaseInfo.idNumber,
        contactPhone: state.personBaseInfo.contactPhone,
        email: state.personBaseInfo.email,
        address: state.personBaseInfo.address,
        openingBank: state.personBaseInfo.openingBank,
        accountNumber: state.personBaseInfo.accountNumber,
        zipCode: state.personBaseInfo.zipCode,
        authorizedIdCardFront: state.personBaseInfo.authorizedIdCardFront,
        authorizedIdCardBack: state.personBaseInfo.authorizedIdCardBack,
        attachmentList: newAttachmentList,
        submit: submit,
      };
      actionUserDetail(reqData).then((res: any) => {
        if (res.code === 0) {
          GetUserDetailInfo();
          userStore.getUserIsAuthAction();
          ElMessage.success("提交成功");
        }
      });
    } else if (state.subjectType === "01") {
      if (state.certificationInfo.length > 0) {
        newAttachmentList = newAttachmentList.concat(state.certificationInfo);
      }
      if (state.caseInfo.length > 0) {
        newAttachmentList = newAttachmentList.concat(state.caseInfo);
      }
      if (state.honorInfo.length > 0) {
        newAttachmentList = newAttachmentList.concat(state.honorInfo);
      }
      if (state.otherFileList.length > 0) {
        newAttachmentList = newAttachmentList.concat(state.otherFileList);
      }
      let qualificationOne=""
      if(state.baseInfo.qualificationOne){
        qualificationOne=JSON.stringify(state.baseInfo.qualificationOne)
      }
      reqData = {
        id: state.detailsId,
        subjectType: state.subjectType,
        companyName: state.baseInfo.companyName,
        creditCode: state.baseInfo.creditCode,
        industry: state.baseInfo.industry,
        areaId: state.baseInfo.areaId,
        legalPerson: state.baseInfo.legalPerson,
        registeredCapital: state.baseInfo.registeredCapital,
        establishTime: state.baseInfo.establishTime
          ? dayjs(state.baseInfo.establishTime).format("YYYY-MM-DD")
          : void 0,
        qualificationOne:qualificationOne,
        workAddress: state.baseInfo.workAddress,
        introduction: state.baseInfo.introduction,
        businessScope: state.baseInfo.businessScope,
        contactName: state.contactInfo.contactName,
        contactPhone: state.contactInfo.contactPhone,
        idNumber: state.contactInfo.idNumber,
        address: state.contactInfo.address,
        email: state.contactInfo.email,
        zipCode: state.contactInfo.zipCode,
        authorizationLetter: state.letterOfAuth.authorizationLetter,
        authorizedIdCardFront: state.letterOfAuth.authorizedIdCardFront,
        authorizedIdCardBack: state.letterOfAuth.authorizedIdCardBack,
        submit: submit,
        attachmentList: newAttachmentList,
      };
      actionUserDetail(reqData).then((res: any) => {
        if (res.code === 0) {
          GetUserDetailInfo();
          userStore.getUserIsAuthAction();
          ElMessage.success("提交成功");
        }
      });
    }
  }
};

//#region 审核相关
const auditRef = ref();
interface IAuditForm {
  auditStatus: string;
  content: string;
}
interface IAudit {
  show: boolean;
  dataForm: IAuditForm;
  dataRules: FormRules;
}
const audit = reactive<IAudit>({
  show: false,
  dataForm: {
    auditStatus: "01",
    content: "",
  },
  dataRules: {
    auditStatus: [{ required: true, message: "请选择审核结果", trigger: "change" }],
  },
});

const onCloseAudit = () => {
  auditRef.value.resetFields();
  audit.dataForm.auditStatus = "01";
  audit.dataForm.content = "";
  audit.show = false;
};

const onSubmitAudit = () => {
  auditRef.value.validate((valid: boolean) => {
    if (valid) {
      let reqData: IAuditSupplier = {
        purchaserBidderId: state.purchaserBidderId,
        auditResult: audit.dataForm.auditStatus,
        opinion: audit.dataForm.content,
      };
      auditSupplier(reqData).then((res: any) => {
        if (res.code === 0) {
          onCloseAudit();
          closeTab(router, route);
          ElMessage.success("审核成功");
        }
      });
    }
  });
};

//#endregion

const onClickAudit = () => {
  audit.show = true;
};

const onClickReturn = () => {
  closeTab(router, route);
};
</script>
<style lang="scss" scoped>
.action {
  &-base {
    &-content {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
  &-contact {
    margin-top: 16px;
    &-content {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
  &-certification {
    margin-top: 16px;
  }
  &-finance {
    margin-top: 16px;
    &-content {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
  &-auth {
    margin-top: 16px;
    &-content {
      margin-top: 16px;
    }
  }
  &-case {
    margin-top: 16px;
  }
  &-honor {
    margin-top: 16px;
  }
  &-other {
    margin-top: 16px;
  }
  &-audit {
    margin-top: 16px;
    &-content {
      margin-top: 16px;
    }
  }
  &-btn {
    margin-top: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
