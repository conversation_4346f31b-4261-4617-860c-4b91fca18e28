<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-01 10:19:02
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-16 15:33:53
-->
<template>
  <div class="action">
    <div class="action2">
      <el-form
        ref="infoRef"
        :model="state.dataForm"
        :rules="state.dataRules"
        label-width="170px"
        class="normalform"
      >
        <div class="action_base">
          <div class="action_title">
            <ContentTitle title="录入采购合同"></ContentTitle>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="title" label="采购合同名称">
                <el-input
                  v-model="state.dataForm.title"
                  placeholder="采购合同名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="title" label="合同编号">
                <el-input
                  v-model="state.dataForm.title"
                  placeholder="合同编号"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="packageName" label="采购计划名称">
                <div style="display: flex; align-items: center; width: 100%">
                  <el-input
                    readonly
                    v-model="state.dataForm.packageName"
                    placeholder="采购计划名称"
                  ></el-input>
                  <el-button type="primary" style="margin-left: 10px" @click="pickplan()"
                    >选择</el-button
                  >
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="packageNo" label="采购计划编号">
                <el-input
                  readonly
                  v-model="state.dataForm.packageNo"
                  placeholder="采购计划编号"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="title" label="合同金额（元）">
                <el-input
                  v-model="state.dataForm.title"
                  placeholder="合同金额（元）"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="title" label="合同期限（天）">
                <el-input
                  v-model="state.dataForm.title"
                  placeholder="合同期限（天）"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="signStartDate" label="预计交付时间">
                <el-date-picker
                  v-model="state.dataForm.signStartDate"
                  type="datetime"
                  placeholder="预计交付时间"
                  value-format="YYYY-MM-DD HH:mm:ss"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="signEndDate" label="合同签署时间">
                <el-date-picker
                  v-model="state.dataForm.signEndDate"
                  type="datetime"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="合同签署时间"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <div class="action_title">
            <ContentTitle title="甲方/乙方信息"></ContentTitle>
          </div>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="buyersName" label="采购人名称">
                <el-input
                  v-model="state.dataForm.buyersName"
                  placeholder="采购人名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="buyersAddress" label="采购人机构代码">
                <el-input
                  v-model="state.dataForm.buyersAddress"
                  placeholder="采购人机构代码"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="buyersName" label="供应商名称">
                <el-input
                  v-model="state.dataForm.buyersName"
                  placeholder="供应商名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="buyersAddress" label="供应商机构代码">
                <el-input
                  v-model="state.dataForm.buyersAddress"
                  placeholder="供应商机构代码"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="合同模式">
                <el-radio-group v-model="state.dataForm.makeType">
                  <el-radio :label="1">文本模式</el-radio>
                  <el-radio :label="0">上传已盖章公告文件</el-radio>
                </el-radio-group>
                <WangEditor v-model="state.dataForm.content"></WangEditor>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="24">
              <el-form-item label="附件">
                <el-upload
                  v-model:file-list="state.fileList"
                  :headers="{ Authorization: cache.getToken() }"
                  :action="constant.uploadUrl"
                  :before-upload="beforeUpload"
                  :on-success="handleSuccess"
                  :on-exceed='handleExceed'
                  accept='.jpeg,.jpg,.doc,.docx,.xls,.xlsx,.pdf,.png,.zip,.rar'
                  limit="1"
                >
                  <el-button type="primary">上传</el-button>
                  <template #tip>
                    <div class="el-upload__tip">
                      支持word/excel/pdf/jpg/png/zip/rar等格式
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </el-col> -->
          </el-row>
        </div>
      </el-form>
    </div>
    <div class="action1">
      <div class="action_title">
        <ContentTitle title="采购清单"></ContentTitle>
      </div>
      <div class="action1_content">
        <div class="list_content">
          <el-table v-loading="state.dataListLoading" :data="state.materialList" border>
            <el-table-column
              type="index"
              label="序号"
              header-align="center"
              align="center"
              width="70"
            ></el-table-column>
            <el-table-column
              prop="materialName"
              label="产品名称"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="materialType"
              label="产品型号（规格参数）"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="materialUnit"
              label="计量单位"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="materialQuantity"
              label="采购数量"
              header-align="center"
              align="center"
            ></el-table-column>
            <el-table-column
              prop="comment"
              label="备注"
              header-align="center"
              align="center"
            ></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import {
  useBulletinSaveTempApi,
  usebulletinByIdApi,
  useBulletinSaveSubmitApi,
  useGetMaterialByPackIdApi,
} from "@/api/pnotice";
import { useRoute, useRouter } from "vue-router";
import { provide, reactive, ref, onMounted } from "vue";
import WangEditor from "@/components/wang-editor/index.vue";
import { ElMessage } from "element-plus";
import cache from "@/utils/cache";
import constant from "@/utils/constant";

const route = useRoute();
const router = useRouter();
const baseInfoRef = ref();
const state = reactive({
  dataForm: {
    packageId: "",
    packageNo: "",
    packageName: "",
    title: "",
    packageMode: "2",
    supplyAddress: "",
    needAudit: 0,
    makeType: 1,
    content: "",
    signFlag: 0,
    signStartDate: "",
    signEndDate: "",
    bidStartDate: "",
    bidEndDate: "",
    bidbondFlag: 0,
    bidbond: 0,
    buyersName: "",
    buyersAddress: "",
    buyersLinkerName: "",
    buyersLinkerTel: "",
    contentAttach: null,
    bulletinAttachs: [],
  },
  dataRules: {
    packageName: [{ required: true, message: "必填项", trigger: "change" }],
    packageNo: [{ required: true, message: "必填项", trigger: "blur" }],
    title: [{ required: true, message: "必填项", trigger: "blur" }],
    packageMode: [{ required: true, message: "必填项", trigger: "change" }],
    supplyAddress: [{ required: true, message: "必填项", trigger: "blur" }],
    needAudit: [{ required: true, message: "必填项", trigger: "change" }],
    makeType: [{ required: true, message: "必填项", trigger: "change" }],
    signFlag: [{ required: true, message: "必填项", trigger: "change" }],
    signStartDate: [{ required: true, message: "必填项", trigger: "change" }],
  },
  nowId: "",
  saveFlag: false, // false 暂存 true 提交
  dataListLoading: false,
  materialList: [],
  fileList: [],
  uploadList: [],
});

onMounted(() => {
  if (route.query.id) {
    state.nowId = route.query.id;
    getDetail();
  }
});

// 返回
const closentab = () => {
  closeTab(router, route);
};
// 暂存
const saveTemp = () => {
  state.saveFlag = false;
  baseInfoRef.value.submitHandle();
};
// 提交
const saveSubmit = () => {
  state.saveFlag = true;
  baseInfoRef.value.submitHandle();
};
// 值变化
const refreshProvide = (res) => {
  console.log(state.dataForm);
  if (res) {
    if (state.saveFlag) {
      useBulletinSaveSubmitApi(state.dataForm).then((res) => {
        if (res.code == 0) {
          ElMessage.success({
            message: "保存成功",
            duration: 500,
            onClose: () => {
              closentab();
            },
          });
        }
      });
    } else {
      useBulletinSaveTempApi(state.dataForm).then((res) => {
        if (res.code == 0) {
          ElMessage.success({
            message: "保存成功",
            duration: 500,
            onClose: () => {
              closentab();
            },
          });
        }
      });
    }
  }
};
// 查看
const getDetail = () => {
  usebulletinByIdApi(state.nowId).then((res) => {
    if (res.code == 0) {
      Object.assign(state.dataForm, res.data);
      if (state.dataForm.bulletinAttachs) {
        state.fileList = state.uploadList = state.dataForm.bulletinAttachs;
      }
      getList(res.data.packageId);
    }
  });
};
// 清单id
const pickPlanres = (res) => {
  getList(res.packageId);
};
// 清单
const getList = (id) => {
  useGetMaterialByPackIdApi(id).then((result) => {
    if (result.code == 0) {
      state.materialList = result.data;
    }
  });
};

const handleSuccess = (res, file, files) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  state.uploadList = [];
  for (let i of state.fileList) {
    if ("response" in i) {
      state.uploadList.push(i.response.data);
    } else {
      state.uploadList.push(i);
    }
  }
  Object.assign(state.dataForm.bulletinAttachs, state.uploadList);
  // console.log(state.uploadList)
};
const handleRemove = (file, files) => {
  state.uploadList = [];
  for (let i of files) {
    if ("response" in i) {
      state.uploadList.push(i.response.data);
    } else {
      state.uploadList.push(i);
    }
  }
  state.dataForm.bulletinAttachs = state.uploadList;
  // Object.assign(state.dataForm.bulletinAttachs, state.uploadList);
};

const beforeUpload = (file) => {
  let types = [
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/pdf",
    "image/jpeg",
    "image/png",
    "aplication/zip",
    "application/x-compressed",
  ];
  if (!types.includes(file.type)) {
    ElMessage.error("格式不支持");
    return false;
  }
  return true;
};
</script>
<style lang="scss" scoped>
.action_title {
  margin: 20px 0;
}
.action_btn {
  text-align: center;
  margin: 20px auto;
}
</style>
