<template>
  <div class="layout-header">
    <div class="layout-header-left">
      <div class="layout-header-left-title">
        <el-image
          @click="goIndex()"
          :src="logoPic"
          style="height: 40px; margin-top: 10px"
        ></el-image>
      </div>
      <div class="layout-header-left-desc">{{ props.title }}</div>
    </div>
    <div class="layout-header-right">
      <a :href="webHref" target="_blank" class="layout-header-right-home">网站首页</a>
      <div class="layout-header-right-mobile">全国服务热线 13020856116</div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import logoPic from "@/assets/logo1.png";
import constant from "@/utils/constant";
import { useRouter } from "vue-router";

const router = useRouter();
interface IProps {
  title: string;
}

const props = withDefaults(defineProps<IProps>(), {
  title: "用户登录",
});

const webHref = ref("");

const webHrefHd = () => {
  webHref.value = constant.env.DEV
    ? "http://test-jczl.hebhzjt.com:7898/yuan-cms/webfile/"
    : "https://www.eczl.com.cn/";
};

const goIndex = () => {
  router.push(import.meta.env.VITE_WEB_URL);
};

webHrefHd();
</script>
<style lang="scss" scoped>
.layout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 68px;
  padding: 0 360px;
  background: #ffffff;
  border-bottom: 1px solid #e2e4e5;
  box-shadow: 0 1px 4px 0 rgb(0 21 41 / 8%);
  &-left {
    display: flex;
    align-items: center;
    &-title {
      font-family: DIN;
      font-size: 24px;
      font-weight: bold;
      color: #409eff;
      cursor: pointer;
    }
    &-desc {
      margin-left: 16px;
      font-family: AlibabaPuHuiTi;
      font-size: 16px;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #2a2a2a;
      cursor: default;
    }
  }
  &-right {
    display: flex;
    align-items: center;
    &-home {
      font-family: AlibabaPuHuiTi;
      font-size: 14px;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #2a2a2a;
      margin-right: 16px;
      cursor: pointer;
    }
    &-mobile {
      font-family: AlibabaPuHuiTi;
      font-size: 12px;
      font-variation-settings: "opsz" auto;
      font-feature-settings: "kern" on;
      color: #409eff;
      cursor: default;
    }
  }
}

@media only screen and (max-width: 992px) {
  .layout-header {
    padding: 0 20px;
  }
}

@media screen and (max-width: 1200px) {
  .layout-header {
    padding: 0 120px;
  }
}

@media screen and (min-width: 1201px) and (max-width: 1559px) {
  .layout-header {
    padding: 0 240px;
  }
}

@media screen and (min-width: 1560px) and (max-width: 1795px) {
  .layout-header {
    padding: 0 360px;
  }
}

@media screen and (min-width: 1796px) {
  .layout-header {
    padding: 0 360px;
  }
}
</style>
