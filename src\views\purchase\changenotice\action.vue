<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-01 10:19:02
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-17 10:36:14
-->
<template>
  <el-card>
    <div class="action">
      <div class="action_base">
        <BaseInfo
          ref="baseInfoRef"
          @refresh-provide="refreshProvide"
          @pick-planres="pickPlanres"
        ></BaseInfo>
      </div>
      <div class="list_file">
        <div class="list_file_label">附件</div>
        <el-upload
          v-model:file-list="state.fileList"
          :headers="{ Authorization: cache.getToken() }"
          :action="constant.uploadUrl"
          :before-upload="beforeUpload"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
          accept=".jpeg,.jpg,.doc,.docx,.xls,.xlsx,.pdf,.png,.zip,.rar"
          multiple
        >
          <el-button type="primary">上传</el-button>
          <template #tip>
            <div class="el-upload__tip">支持word/excel/pdf/jpg/gif/png/zip/rar等格式</div>
          </template>
        </el-upload>
      </div>
      <div class="action_btn">
        <el-button type="primary" @click="saveSubmit">发布变更公告</el-button>
        <el-button type="primary" plain @click="saveTemp">暂存</el-button>
        <el-button @click="closentab">返回</el-button>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import BaseInfo from "./components/BaseInfo.vue";
import { closeTab } from "@/utils/tabs";
import {
  useBulletinChangeSaveTempApi,
  usebulletinChangeByIdApi,
  useBulletinChangeSaveSubmitApi,
  usebulletinChangeByPackIdApi,
} from "@/api/pnotice";
import { useRoute, useRouter } from "vue-router";
import { provide, reactive, ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { objectEach } from "xe-utils";
import cache from "@/utils/cache";
import constant from "@/utils/constant";

const route = useRoute();
const router = useRouter();
const baseInfoRef = ref();
const state = reactive({
  dataForm: {
    id: 0,
    packageId: 0,
    packageNo: "",
    packageName: "",
    title: "",
    needAudit: 0,
    needAuditLabel: "",
    makeType: 1,
    makeTypeLabel: "",
    content: "",
    signStartDate: "",
    signEndDate: "",
    bidStartDate: "",
    bidEndDate: "",
    timeChange: 0,
    contentAttach: null,
    bulletinAttachs: [],
  },
  dataRules: {
    packageName: [{ required: true, message: "必填项", trigger: "change" }],
    packageNo: [{ required: true, message: "必填项", trigger: "blur" }],
    title: [{ required: true, message: "必填项", trigger: "blur" }],
    needAudit: [{ required: true, message: "必填项", trigger: "change" }],
    makeType: [{ required: true, message: "必填项", trigger: "change" }],

    contentAttach: [{ required: true, message: "必填项", trigger: "change" }],
    timeChange: [{ required: true, message: "必填项", trigger: "change" }],
    signEndDate: [{ required: true, message: "必填项", trigger: "change" }],
    bidEndDate: [{ required: true, message: "必填项", trigger: "change" }],
  },
  nowId: "",
  saveFlag: false, // false 暂存 true 提交
  dataListLoading: false,
  materialList: [],
  fileList: [],
  uploadList: [],
});

provide("dataForm", state.dataForm);
provide("dataRules", state.dataRules);

onMounted(() => {
  if (route.query.id) {
    state.nowId = route.query.id;
    getDetail();
  }
});

// 返回
const closentab = () => {
  closeTab(router, route);
};
// 暂存
const saveTemp = () => {
  state.saveFlag = false;
  baseInfoRef.value.submitHandle();
};
// 提交
const saveSubmit = () => {
  state.saveFlag = true;
  baseInfoRef.value.submitHandle();
};
// 值变化 -- 提交
const refreshProvide = (res) => {
  if (res) {
    if (state.saveFlag) {
      state.dataForm.id = void 0;
      useBulletinChangeSaveSubmitApi(state.dataForm).then((res) => {
        if (res.code == 0) {
          ElMessage.success({
            message: "保存成功",
            duration: 500,
            onClose: () => {
              closentab();
            },
          });
        }
      });
    } else {
      useBulletinChangeSaveTempApi(state.dataForm).then((res) => {
        if (res.code == 0) {
          ElMessage.success({
            message: "保存成功",
            duration: 500,
            onClose: () => {
              closentab();
            },
          });
        }
      });
    }
  }
};
// 查看
const getDetail = () => {
  usebulletinChangeByIdApi(state.nowId).then((res) => {
    if (res.code == 0) {
      Object.assign(state.dataForm, res.data);
      if (state.dataForm.bulletinAttachs) {
        state.fileList = state.uploadList = state.dataForm.bulletinAttachs;
      }
      getList(res.data.packageId);
    }
  });
};
// 挑选计划
const pickPlanres = (res) => {
  getBulletinInfo(res.packageId);
};
// 变更公告基本内容
const getBulletinInfo = (id) => {
  usebulletinChangeByPackIdApi(id).then((result) => {
    if (result.code == 0) {
      Object.assign(state.dataForm, result.data);
    }
  });
};

const handleSuccess = (res, file, files) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  state.uploadList = [];
  for (let i of state.fileList) {
    if ("response" in i) {
      state.uploadList.push(i.response.data);
    } else {
      state.uploadList.push(i);
    }
  }
  Object.assign(state.dataForm.bulletinAttachs, state.uploadList);
  // console.log(state.uploadList)
};
const handleRemove = (file, files) => {
  state.uploadList = [];
  for (let i of files) {
    if ("response" in i) {
      state.uploadList.push(i.response.data);
    } else {
      state.uploadList.push(i);
    }
  }
  state.dataForm.bulletinAttachs = state.uploadList;
  // Object.assign(state.dataForm.bulletinAttachs, state.uploadList);
};
const beforeUpload = (file) => {
  let types = [
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/pdf",
    "image/jpeg",
    "image/png",
    "aplication/zip",
    "application/x-compressed",
  ];
  if (!types.includes(file.type)) {
    ElMessage.error("格式不支持");
    return false;
  }
  return true;
};
</script>
<style lang="scss" scoped>
.action {
  &_base {
    &_content {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
  &_purchase {
    margin-top: 16px;
    &_content {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
  &_inventory {
    margin-top: 16px;
    &_content {
      margin-top: 16px;
    }
  }
  &_btn {
    text-align: center;
    margin-top: 15px;
  }
  .list_file {
    margin: 20px 0 0 30px;
    display: flex;
    &_label {
      width: 130px;
      display: flex;
      justify-content: flex-end;
      margin-right: 10px;
    }
  }
}
</style>
