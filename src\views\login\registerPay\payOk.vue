<template>
  <div class="register">
    <div class="register-header">
      <Header title="用户注册"></Header>
    </div>
    <div class="register-body">
      <div class="status">
        <img class="status-img" src="@/assets/image/register/pay_ok.png"  />
        <div class="status-label">支付成功</div>
        <div class="status-remark">{{state.timeSecond}}秒后自动返回登录页面</div>
        <el-button type="primary" class="status-btn" @click="goLogin()">返回登录</el-button>
      </div>
    </div>
    <div class="register-footer">
      <Footer></Footer>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref,onMounted,onUnmounted } from "vue";
import Header from "../components/Header.vue";
import Footer from "../components/Footer.vue";
import { useRoute,useRouter } from "vue-router";

const route = useRoute();
const router = useRouter();
const state = reactive({
  timeSecond:30,
  payInterval:null
});
const messages = ref([]);

onMounted(() => {
  clearInterval(state.payInterval);
  countdown()
})

const countdown=(seconds:any)=> {
  state.payInterval = setInterval(function() {
    if (state.timeSecond === 0) {
      clearInterval(state.payInterval);
      goLogin()
    } else {
      state.timeSecond--;
    }
  }, 1000);
}
 

const goLogin=()=>{
  router.replace({path:"/login"});
}
</script>
<style lang="scss" scoped>
.status{
  text-align: center;
  margin:120px 0;
  .status-img{
    width:220px;
    display: block;
    margin:0 auto;
  }
  .status-label{
    margin-top:30px;
    font-size: 20px;
    font-weight: bold;
    color:#000;
  }
  .status-remark{
    margin-top:15px;
    color:#999;
  }
  .status-btn{
    margin-top:15px;
  }
}
</style>
