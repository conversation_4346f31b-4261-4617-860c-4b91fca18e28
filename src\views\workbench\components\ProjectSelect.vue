<template>
  <el-dialog
    v-model="dialog.show"
    title="选择项目"
    :close-on-click-modal="false"
    draggable
    width="1000px"
    @close="onClose"
  >
    <div class="project">
      <div class="project-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="项目编号"
              clearable
              v-model="state.queryForm.projectNo"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="项目名称"
              clearable
              v-model="state.queryForm.projectName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onResetSearch()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="project-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="projectNo"
            label="项目编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="projectName"
            label="项目名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="budget"
            label="预算总金额（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="120"
          >
            <template #default="scope">
              <el-button type="primary" link @click="onClickSelect(scope.row)"
                >选择</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="project-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
    </div>
  </el-dialog>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive, watch } from "vue";
interface IProps {
  show: boolean;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
});

interface IDialog {
  show: boolean;
}

const dialog = reactive<IDialog>({
  show: false,
});

const state = reactive<IHooksOptions>({
  createdIsNeed: false,
  queryForm: {
    projectNo: "",
    projectName: "",
  },
  dataListUrl: "purchase/project/page",
  deleteUrl: "/purchase/project",
});
const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

const onResetSearch = () => {
  state.queryForm.projectNo = "";
  state.queryForm.projectName = "";
  state.pageNo = 1;
  getDataList();
};

const emit = defineEmits<{
  (e: "on-close"): void;
  (e: "on-select", row: any): void;
}>();

const onClose = () => {
  dialog.show = false;
  emit("on-close");
};

const onClickSelect = (row: any) => {
  dialog.show = false;
  emit("on-select", row);
};

watch(
  () => props.show,
  () => {
    if (props.show) {
      dialog.show = true;
      onResetSearch();
    }
  }
);
</script>
<style lang="scss" scoped>
.project {
  min-height: 450px;
}
</style>
