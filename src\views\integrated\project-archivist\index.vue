<template>
  <el-card>
    <div class="archivist">
      <div class="archivist-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="采购计划编号"
              clearable
              v-model="state.queryForm.packageNo"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="采购计划名称"
              clearable
              v-model="state.queryForm.packageName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-select
              placeholder="采购方式"
              style="width: 200px"
              clearable
              v-model="state.queryForm.packageType"
            >
              <el-option label="公开招标" value="1"></el-option>
              <el-option label="邀请招标" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <fast-select
              style="width: 200px"
              v-model="state.queryForm.auditStatus"
              dict-type="audit_status"
              clearable
              placeholder="审核状态"
            ></fast-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="selectDate"
              type="daterange"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="onChangeSelectDate"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onClickReset()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="archivist-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            dict-type="user_gender"
          ></el-table-column>
          <fast-table-column
            prop="packageType"
            label="采购方式"
            dict-type="package_type"
          ></fast-table-column>
          <el-table-column
            prop="packageBudget"
            label="开标时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="auditStatus"
            label="状态"
            dict-type="audit_status"
          ></fast-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="240"
          >
            <template #default="scope">
              <el-button type="primary" link> 查看 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="archivist-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import dayjs from "dayjs";
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();

const selectDate = ref([]);

const state = reactive<IHooksOptions>({
  queryForm: {
    packageNo: "",
    packageName: "",
    startTime: void 0,
    endTime: void 0,
  },
  dataListUrl: "/purchase/package/page",
  deleteUrl: "/purchase/package",
});

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

const onChangeSelectDate = () => {
  if (selectDate.value && selectDate.value.length === 2) {
    state.queryForm.startTime = dayjs(selectDate.value[0]).format("YYYY-MM-DD");
    state.queryForm.endTime = dayjs(selectDate.value[1]).format("YYYY-MM-DD");
  } else {
    state.queryForm.startTime = void 0;
    state.queryForm.endTime = void 0;
  }
};

const onClickReset = () => {
  let newQueryForm = {
    queryForm: {
      packageNo: "",
      packageName: "",
      startTime: void 0,
      endTime: void 0,
    },
  };
  state.queryForm = newQueryForm;
  selectDate.value = [];
  getDataList();
};
</script>
<style lang="scss" scoped></style>
