<template>
  <div class="track">
    <el-descriptions :column="1" border>
      <el-descriptions-item label-align="right">
        <template #label>
          <div class="cell-item">中标单位</div>
        </template>
        {{ state.winBidder }}
      </el-descriptions-item>
      <el-descriptions-item label-align="right">
        <template #label>
          <div class="cell-item">中标单位联系人</div>
        </template>
        {{ state.contactName }}
      </el-descriptions-item>
      <el-descriptions-item label-align="right">
        <template #label>
          <div class="cell-item">中标单位联系电话</div>
        </template>
        {{ state.contactPhone }}
      </el-descriptions-item>
      <el-descriptions-item label-align="right">
        <template #label>
          <div class="cell-item">中标金额（元）</div>
        </template>
        {{ state.winPrice }}
      </el-descriptions-item>
      <el-descriptions-item label-align="right">
        <template #label>
          <div class="cell-item">合同金额（元）</div>
        </template>
        {{ state.serviceCharge }}
      </el-descriptions-item>
      <el-descriptions-item label-align="right">
        <template #label>
          <div class="cell-item">备注</div>
        </template>
        {{ state.notes }}
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script setup lang="ts">
import { getBidderInfo } from "@/api/picketage/enter";
import { getWinBidder } from "@/api/purchase/plan";
import { onMounted, reactive } from "vue";

interface IProps {
  id?: number;
}

const props = withDefaults(defineProps<IProps>(), {
  id: void 0,
});

interface IDataForm {}

interface IState {
  packageName: string;
  winBidder: string;
  contactName: string;
  contactPhone: string;
  winPrice: string;
  serviceCharge: string;
  notes: string;
}

const state = reactive<IState>({
  packageName: "",
  winBidder: "",
  contactName: "",
  contactPhone: "",
  winPrice: "",
  serviceCharge: "",
  notes: "",
});

onMounted(() => {
  if (props.id) {
    getWinBidder(props.id).then((res: any) => {
      if (res.code === 0) {
        state.packageName = res.data.packageName;
        state.winBidder = res.data.winBidder;
        state.contactName = res.data.contactName;
        state.contactPhone = res.data.contactPhone;
        state.winPrice = res.data.winPrice;
        state.serviceCharge = res.data.serviceCharge;
        state.notes = res.data.notes;
      }
    });
  }
});
</script>
<style lang="scss" scoped></style>
