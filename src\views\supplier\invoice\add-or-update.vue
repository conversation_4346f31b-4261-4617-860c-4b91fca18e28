<template>
  <el-dialog
    v-model="state.visible"
    :title="(props.id ?? '') === '' ? '新增' : '修改'"
    :close-on-click-modal="false"
    destroy-on-close
    @closed="onClosed"
  >
    <div>
      <el-form
        ref="refDataForm"
        :model="state.dataForm"
        :rules="state.dataRule"
        label-width="120px"
      >
        <el-form-item label="抬头类型" prop="titleType">
          <fast-radio-group
            v-model="state.dataForm.titleType"
            dict-type="title_type"
            @update:modelValue="onTitleTypeChange"
          ></fast-radio-group>
        </el-form-item>
        <el-form-item label="抬头名称" prop="titleName">
          <el-input v-model="state.dataForm.titleName" placeholder="抬头名称" />
        </el-form-item>
        <el-form-item label="发票类型" prop="invoiceType">
          <el-radio
            v-model="state.dataForm.invoiceType"
            value="1"
            v-if="state.dataForm.titleType === '0'"
          >
            专票
          </el-radio>
          <el-radio v-model="state.dataForm.invoiceType" value="0" v-else>
            普票
          </el-radio>
        </el-form-item>
        <template v-if="state.dataForm.titleType === '0'">
          <el-form-item label="纳税人识别号" prop="invoiceCode">
            <el-input v-model="state.dataForm.invoiceCode" placeholder="纳税人识别号" />
          </el-form-item>
          <el-form-item label="开户银行" prop="bankName">
            <el-input v-model="state.dataForm.bankName" placeholder="开户银行" />
          </el-form-item>
          <el-form-item label="银行账号" prop="bankCode">
            <el-input v-model="state.dataForm.bankCode" placeholder="银行账号" />
          </el-form-item>
          <el-form-item label="单位注册地址" prop="invoiceAddr">
            <el-input v-model="state.dataForm.invoiceAddr" placeholder="单位注册地址" />
          </el-form-item>
          <el-form-item label="电话" prop="linkTel">
            <el-input v-model="state.dataForm.linkTel" placeholder="电话" />
          </el-form-item>
        </template>
        <el-form-item label="手机号码" prop="landLineTel">
          <el-input v-model="state.dataForm.landLineTel" placeholder="手机号码" />
        </el-form-item>
        <el-form-item label="电子邮箱" prop="emailCode">
          <el-input v-model="state.dataForm.emailCode" placeholder="电子邮箱" />
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="onClickClose()">取消</el-button>
      <el-button type="primary" :loading="state.loading" @click="onClickSubmit()"
        >确定</el-button
      >
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { actionWorkInfo, getWorkInfo } from "@/api/supplier/invoice";
import { ElMessage } from "element-plus";
import { reactive, ref, watch } from "vue";

interface IProps {
  show: boolean;
  id?: string;
  companyName?: string;
  email?: string;
  mobile?: string;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  id: "",
});

const refDataForm = ref();

interface IDataForm {
  id: string;
  titleType: string;
  titleName: string;
  invoiceType: string;
  invoiceCode: string;
  bankName: string;
  bankCode: string;
  invoiceAddr: string;
  linkTel: string;
  landLineTel: string;
  emailCode: string;
}

interface IState {
  visible: boolean;
  dataForm: IDataForm;
  dataRule: any;
  loading: boolean;
}

const state = reactive<IState>({
  visible: false,
  dataForm: {
    id: "",
    titleType: "0",
    titleName: "",
    invoiceType: "1",
    invoiceCode: "",
    bankName: "",
    bankCode: "",
    invoiceAddr: "",
    linkTel: "",
    landLineTel: "",
    emailCode: "",
  },
  dataRule: {
    titleType: [{ required: true, message: "抬头类型不能为空", trigger: "change" }],
    titleName: [{ required: true, message: "抬头名称不能为空", trigger: "blur" }],
    invoiceType: [{ required: true, message: "发票类型不能为空", trigger: "change" }],
    invoiceCode: [{ required: true, message: "纳税人识别号不能为空", trigger: "blur" }],
    bankName: [{ required: true, message: "开户银行不能为空", trigger: "blur" }],
    bankCode: [{ required: true, message: "银行账号不能为空", trigger: "blur" }],
    invoiceAddr: [{ required: true, message: "单位注册地址不能为空", trigger: "blur" }],
    linkTel: [{ required: true, message: "电话不能为空", trigger: "blur" }],
    landLineTel: [{ required: true, message: "手机号码不能为空", trigger: "blur" }],
    emailCode: [{ required: true, message: "电子邮箱不能为空", trigger: "blur" }],
  },
  loading: false,
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      state.dataForm.id = props.id || "";
      if (state.dataForm.id === "") {
        state.dataForm.titleName = props.companyName || "";
        state.dataForm.emailCode = props.email || "";
        state.dataForm.landLineTel = props.mobile || "";
      } else {
        GetWorkInfo(state.dataForm.id);
      }
      state.visible = true;
    }
  }
);

const onTitleTypeChange = () => {
  if (state.dataForm.titleType === "0") {
    state.dataForm.invoiceType = "1";
  } else {
    state.dataForm.invoiceType = "0";
  }

  state.dataForm.titleName = props.companyName || "";
  state.dataForm.invoiceCode = "";
  state.dataForm.bankName = "";
  state.dataForm.bankCode = "";
  state.dataForm.invoiceAddr = "";
  state.dataForm.linkTel = "";
  state.dataForm.landLineTel = props.mobile || "";
  state.dataForm.emailCode = props.email || "";
};

const emit = defineEmits<{
  (e: "close"): void;
  (e: "refresh"): void;
}>();

const onClickClose = () => {
  state.visible = false;
};

const onClickSubmit = () => {
  refDataForm.value.validate((valid: boolean) => {
    if (valid) {
      state.loading = true;
      actionWorkInfo(state.dataForm)
        .then(() => {
          state.loading = false;
          ElMessage.success({
            message: "操作成功",
            duration: 500,
            onClose: () => {
              onClickClose();
              emit("refresh");
            },
          });
        })
        .catch(() => {
          state.loading = false;
          ElMessage.error("操作失败");
        });
    }
  });
};

const onClosed = () => {
  state.dataForm = {
    id: "",
    titleType: "0",
    titleName: "",
    invoiceType: "1",
    invoiceCode: "",
    bankName: "",
    bankCode: "",
    invoiceAddr: "",
    linkTel: "",
    landLineTel: "",
    emailCode: "",
  };
  emit("close");
};

const GetWorkInfo = (id: string) => {
  getWorkInfo(id).then((res) => {
    if (res.code === 0) {
      state.dataForm.id = res.data.id;
      state.dataForm.titleType = res.data.titleType + "";
      state.dataForm.titleName = res.data.titleName;
      state.dataForm.invoiceType = res.data.invoiceType + "";
      state.dataForm.invoiceCode = res.data.invoiceCode;
      state.dataForm.bankName = res.data.bankName;
      state.dataForm.bankCode = res.data.bankCode;
      state.dataForm.invoiceAddr = res.data.invoiceAddr;
      state.dataForm.linkTel = res.data.linkTel;
      state.dataForm.landLineTel = res.data.landLineTel;
      state.dataForm.emailCode = res.data.emailCode;
    }
  });
};
</script>
<style lang="scss" scoped></style>
