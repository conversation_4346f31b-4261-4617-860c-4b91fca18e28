<template>
  <div class="title-line">
    <div class="title">基本信息</div>
    <el-link type="primary" @click="lookSpList">查看审批数据</el-link>
  </div>
  <div class="content">
    <el-form
      label-position="top"
      :model="state.formData"
      :rules="state.rules"
      ref="formRef"
    >
      <el-form-item label="项目" prop="projectName">
        <el-input
          v-model="state.formData.projectName"
          readonly
          class="custom-input"
          placeholder="请选择项目"
        >
          <template #append>
            <el-button type="primary" @click="selectPj">选择</el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="付款事由" prop="flowRemark">
        <el-input
          type="textarea"
          v-model="state.formData.flowRemark"
          :rows="5"
          placeholder="请输入付款事由"
        ></el-input>
      </el-form-item>
      <el-form-item label="付款金额（元）" prop="amount">
        <el-input-number
          v-model="state.formData.amount"
          :min="0"
          :precision="2"
          placeholder="请输入付款金额"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="收款账户名称" prop="receivingCompany">
        <el-input
          v-model="state.formData.receivingCompany"
          placeholder="请输入收款账户名称"
        ></el-input>
      </el-form-item>
      <el-form-item label="银行账号" prop="bankNo">
        <el-input
          v-model="state.formData.bankNo"
          maxlength="50"
          placeholder="请输入银行账号"
        ></el-input>
      </el-form-item>
      <el-form-item label="开户行" prop="openingBank">
        <el-input
          v-model="state.formData.openingBank"
          maxlength="40"
          placeholder="请输入开户行"
        ></el-input>
      </el-form-item>
      <el-form-item label="附件">
        <el-upload
          class="upload"
          v-model:file-list="state.fileList"
          :headers="{ Authorization: cache.getToken() }"
          :action="constant.uploadUrl"
          :before-upload="beforeUpload"
          :on-success="handleSuccess"
          :on-remove="handleRemove"
          accept=".jpeg,.jpg,.doc,.docx,.xls,.xlsx,.pdf,.png,.zip,.rar"
        >
          <el-button type="primary" v-if="state.fileList.length < 30">上传附件</el-button>
          <template #tip>
            <div class="el-upload__tip">最多支持上传30个文件</div>
          </template>
        </el-upload>
      </el-form-item>
    </el-form>
    <!-- 审批流程 -->
    <approval ref="approvalRef" @emit-user="getOAuser" @emit-user-copy="getOAuserCopy" />
    <div class="btns">
      <el-button type="primary" @click="submitData" :loading="state.submitLoading"
        >提交</el-button
      >
    </div>
  </div>

  <ProjectSelect
    :show="state.projectActionShow"
    @on-close="onCloseSelectProject"
    @on-select="onConfirmSelectProject"
  ></ProjectSelect>
</template>

<script setup lang="ts">
import { workPaymentFlowInfo } from "@/api/workbench";
import cache from "@/utils/cache";
import constant from "@/utils/constant";
import ProjectSelect from "@/views/purchase/plan/components/ProjectSelect.vue";
import { ElMessage } from "element-plus";
import { reactive, ref } from "vue";
import approval from "./approval.vue";

const formRef = ref();
const approvalRef = ref();

const validateBankNo = (rule: any, value: any, callback: any) => {
  if (value === "") {
    callback(new Error("必填项"));
  } else {
    if (value.match(/^[0-9]+$/)) {
      callback();
    } else {
      callback(new Error("请输入正确的银行账号"));
    }
  }
};
const state = reactive({
  oaflowuser1: [],
  oaflowuser2: [],
  fileList: [],
  formData: {
    projectId: "",
    projectName: "",
    amount: "",
    payee: "",
    receivingCompany: "",
    bankNo: "",
    openingBank: "",
    flowRemark: "",
    attachmentList: [],
    oaFlowPersonList: [],
  },
  rules: {
    // projectName: [{ required: true, message: "必填项", trigger: "change" }],
    amount: [{ required: true, message: "必填项", trigger: "blur" }],
    payee: [{ required: true, message: "必填项", trigger: "blur" }],
    receivingCompany: [{ required: true, message: "必填项", trigger: "blur" }],
    bankNo: [
      { required: true, message: "必填项", trigger: "blur" },
      { validator: validateBankNo, message: "请输入正确的银行账号", trigger: "change" },
    ],
    // openingBank: [{ required: true, message: "必填项", trigger: "blur" }],
    flowRemark: [{ required: true, message: "必填项", trigger: "blur" }],
  },
  projectActionShow: false,
  projectInfo: null,
  submitLoading: false,
});

// const rules = reactive<FormRules<typeof ruleForm>>({
//   projectName: [{ required: true, message: "必填项", trigger: "change" }],
//   amount: [{ required: true, message: "必填项", trigger: "blur" }],
//   payee: [{ required: true, message: "必填项", trigger: "blur" }],
//   receivingCompany: [{ required: true, message: "必填项", trigger: "blur" }],
//   bankNo: [{ required: true, message: "必填项", trigger: "blur" },{validator: validateBankNo, message: "请输入正确的银行账号",trigger: 'change'}],
//   openingBank: [{ required: true, message: "必填项", trigger: "blur" }],
//   flowRemark: [{ required: true, message: "必填项", trigger: "blur" }],
// })

import { useRouter } from "vue-router";
const router = useRouter();
// 查看审批数据
const lookSpList = () => {
  router.push({
    path: "/approvalCenter/imake/index",
    query: {
      flowType: "4",
    },
  });
};
const selectPj = () => {
  state.projectActionShow = true;
};
const onConfirmSelectProject = (row: any) => {
  state.projectActionShow = false;
  state.formData.projectId = row.id;
  state.formData.projectName = row.projectName;
};
const onCloseSelectProject = () => {
  state.projectActionShow = false;
};
const handleSuccess = (res, file) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  state.formData.attachmentList.push(res.data);
};
const beforeUpload = (file) => {
  let fileType=""
  if(file.name){
    let nameArray=file.name.split(".");
    fileType=nameArray[nameArray.length-1];
  }
  if (
    [
      "jpg",
      "png",
      "jpeg",
      "doc",
      "docx",
      "xls",
      "pdf",
      "xlsx",
      "gif",
      "zip",
      "rar",
    ].indexOf(fileType) == -1
  ) {
    ElMessage.error("格式不支持");
    return false;
  }
  return true;
};
const handleRemove = (file, files) => {
  let uploadList = [];
  for (let i of files) {
    if ("response" in i) {
      uploadList.push(i.response.data);
    } else {
      uploadList.push(i);
    }
  }
  state.formData.attachmentList = uploadList;
  // Object.assign(state.formData.attachmentList, uploadList);
};
const getOAuser = (val) => {
  let arr = val.map((item) => ({
    type: 1,
    status: 2,
    userId: item.id,
    userName: item.name,
  }));
  arr[0].status = 3;
  state.oaflowuser1 = arr;
};
const getOAuserCopy = (val) => {
  state.oaflowuser2 = val.map((item) => ({
    type: 2,
    userId: item.id,
    userName: item.name,
  }));
};
const submitData = () => {
  formRef.value.validate((valid) => {
    if (!valid) return;
    if (state.oaflowuser1.length <= 0) return ElMessage.error("请选择审批人");
    state.formData.oaFlowPersonList = [...state.oaflowuser1, ...state.oaflowuser2];
    state.submitLoading = true;
    workPaymentFlowInfo(state.formData).then((res) => {
      state.submitLoading = false;
      if (res.code !== 0) {
        ElMessage.error(res.msg);
        return;
      } else {
        ElMessage.success("提交成功");
        state.formData.attachmentList = [];
        state.formData.oaCostFlowInfoVOS = [
          {
            costMoney: "",
            startTime: "",
          },
        ];
        state.oaflowuser1 = [];
        state.oaflowuser2 = [];
        state.fileList = [];
        state.formData.projectId = "";
        state.formData.oaFlowPersonList = [];
        approvalRef.value.clearInfo();
        formRef.value.clearValidate();
        formRef.value.resetFields();
      }
    });
  });
};
</script>

<style scoped lang="scss">
.title-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .title {
    text-align: center;
    padding: 8px 0;
    width: 100px;
    border-left: 3px solid #409eff;
  }
}
.btns {
  margin: 20px 0;
}
.upload {
  width: 100%;
}
.custom-input :deep(.el-input-group__append button.el-button) {
  color: var(--el-color-white);
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary);
}
</style>
