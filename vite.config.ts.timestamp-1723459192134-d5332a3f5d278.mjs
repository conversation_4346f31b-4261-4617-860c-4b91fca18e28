// vite.config.ts
import { resolve } from "path";
import { defineConfig } from "file:///F:/project/hzjt/yuan-oa-ui/node_modules/vite/dist/node/index.js";
import vue from "file:///F:/project/hzjt/yuan-oa-ui/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import { createSvgIconsPlugin } from "file:///F:/project/hzjt/yuan-oa-ui/node_modules/vite-plugin-svg-icons/dist/index.mjs";
import vueSetupExtend from "file:///F:/project/hzjt/yuan-oa-ui/node_modules/vite-plugin-vue-setup-extend/dist/index.mjs";
var __vite_injected_original_dirname = "F:\\project\\hzjt\\yuan-oa-ui";
var vite_config_default = defineConfig({
  base: "./",
  resolve: {
    // 配置别名
    alias: {
      "@": resolve(__vite_injected_original_dirname, "./src"),
      "vue-i18n": "vue-i18n/dist/vue-i18n.cjs.js"
    }
  },
  plugins: [
    vue(),
    vueSetupExtend(),
    createSvgIconsPlugin({
      iconDirs: [resolve(__vite_injected_original_dirname, "src/icons/svg")],
      symbolId: "icon-[dir]-[name]"
    })
  ],
  server: {
    host: "0.0.0.0",
    port: 3e3,
    // 端口号
    open: false
    // 是否自动打开浏览器
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
