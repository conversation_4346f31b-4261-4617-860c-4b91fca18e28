<template>
  <div class="info">
    <el-form
      ref="infoRef"
      :model="state.dataForm"
      :rules="state.dataRules"
      label-width="140px"
    >
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="areThreeCertificates" label="三证合一">
            <el-input
              v-model="state.dataForm.areThreeCertificates"
              placeholder="三证合一"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="creditCode" label="统一社会信用代码">
            <el-input
              v-model="state.dataForm.creditCode"
              placeholder="统一社会信用代码"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="businessLicense" label="营业执照扫描件">
            <el-input
              v-model="state.dataForm.businessLicense"
              placeholder="营业执照扫描件"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item prop="logo" label="企业LOGO">
            <el-input v-model="state.dataForm.logo" placeholder="企业LOGO"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script setup lang="ts">
import { reactive } from "vue";
const state = reactive({
  dataForm: {},
  dataRules: {},
});
</script>
<style lang="scss" scoped>
.info {
  width: 100%;
  max-width: 1000px;
  margin-top: 10px;
}
</style>
