<template>
  <el-dialog
    v-model="state.visible"
    title="查看一轮报价"
    width="800px"
    :close-on-click-modal="false"
    @close="onClickClose"
  >
    <div class="supplier">
      <div class="supplier-tabs">
        <el-tabs
          v-model="supplier.queryForm.roundNum"
          type="card"
          @tab-click="onClickTab"
        >
          <el-tab-pane
            :label="`第${props.roundNo - 1}轮`"
            :name="props.roundNo - 1"
          ></el-tab-pane>
        </el-tabs>
      </div>
      <div class="supplier-table">
        <el-table
          v-loading="supplier.dataListLoading"
          show-overflow-tooltip
          :data="supplier.dataList"
          border
          max-height="600px"
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="bidderName"
            label="供应商名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="quotationPrice"
            label="报价（元）"
            header-align="center"
            align="center"
          ></el-table-column>
        </el-table>
      </div>
      <div class="supplier-page">
        <el-pagination
          :current-page="supplier.pageNo"
          :page-sizes="supplier.pageSizes"
          :page-size="supplier.pageSize"
          :total="supplier.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="onClickClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive, watch } from "vue";

const props = withDefaults(
  defineProps<{
    show: boolean;
    packageId?: number;
    roundNo?: number;
  }>(),
  {
    show: false,
    roundNo: 1,
  }
);

interface IState {
  visible: boolean;
}

const state = reactive<IState>({
  visible: false,
});

watch(
  () => props.show,
  (val) => {
    state.visible = val;
    supplier.page = 1;
    supplier.queryForm.packageId = props.packageId;
    supplier.queryForm.roundNum = props.roundNo - 1;
    if (supplier.queryForm.packageId) {
      getDataList();
    }
  }
);

const onClickTab = (value) => {
  supplier.queryForm.roundNum = value.paneName;
  supplier.page = 1;
  if (supplier.queryForm.packageId) {
    getDataList();
  }
};

const supplier = reactive<IHooksOptions>({
  createdIsNeed: false,
  queryForm: {
    packageId: void 0,
    roundNum: 1,
  },
  dataListUrl: "/purchase/quotation/pageWithoutName",
});

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(supplier);

const emit = defineEmits<{
  (e: "close"): void;
}>();

const onClickClose = () => {
  state.visible = false;
  emit("close");
};
</script>
<style lang="scss" scoped></style>
