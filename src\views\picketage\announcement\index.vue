<template>
  <el-card>
    <div class="announcement">
      <div class="announcement-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="采购计划编号"
              clearable
              v-model="state.queryForm.packageNo"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="采购计划名称"
              clearable
              v-model="state.queryForm.packageName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="公告标题"
              clearable
              v-model="state.queryForm.title"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="selectDate"
              type="daterange"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="onChangeSelectDate"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onResetSearch()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="announcement-action">
        <el-button
          type="primary"
          @click="addOrUpdateHandle(false)"
          v-auth="'purchase:winBulletin:save'"
          >新增</el-button
        >
      </div>
      <div class="announcement-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="title"
            label="公告标题"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="relDate"
            label="发布时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="auditStatus"
            label="状态"
            dict-type="audit_status"
          ></fast-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="250"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="onClickDetail(scope.row)"
                v-auth="'purchase:winBulletin:info'"
                v-if="scope.row.auditStatus != '1' && scope.row.auditStatus != '4'"
              >
                查看
              </el-button>
              <el-button
                type="primary"
                link
                @click="addOrUpdateHandle(true, scope.row.id)"
                v-if="scope.row.auditStatus == '1' || scope.row.auditStatus == '4'"
                v-auth="'purchase:winBulletin:update'"
              >
                编辑
              </el-button>
              <el-button
                type="primary"
                link
                @click="deleteBatchHandle(scope.row.id)"
                v-if="scope.row.auditStatus == '1' || scope.row.auditStatus == '4'"
                v-auth="'purchase:winBulletin:delete'"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="announcement-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <Detail :show="detail.show" :id="detail.id" @on-close="onCloseDetail"></Detail>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import dayjs from "dayjs";
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";
import Detail from "./components/Detail.vue";

const router = useRouter();

const state = reactive<IHooksOptions>({
  queryForm: {
    packageNo: "",
    packageName: "",
    title: "",
    startTime: "",
    endTime: "",
  },
  dataListUrl: "/purchase/winBulletin/page",
  deleteUrl: "/purchase/winBulletin",
});

const { getDataList, sizeChangeHandle, currentChangeHandle, deleteBatchHandle } = useCrud(
  state
);

const selectDate = ref([]);

const onChangeSelectDate = () => {
  if (selectDate.value && selectDate.value.length === 2) {
    state.queryForm.startTime = dayjs(selectDate.value[0]).format("YYYY-MM-DD");
    state.queryForm.endTime = dayjs(selectDate.value[1]).format("YYYY-MM-DD");
  } else {
    state.queryForm.startTime = void 0;
    state.queryForm.endTime = void 0;
  }
};

const onResetSearch = () => {
  state.queryForm.packageNo = "";
  state.queryForm.packageName = "";
  state.queryForm.title = "";
  state.queryForm.startTime = void 0;
  state.queryForm.endTime = void 0;
  selectDate.value = [];
  state.pageNo = 1;
  getDataList();
};

const addOrUpdateHandle = (isUpdate: boolean, id?: number) => {
  router.push({
    path: "/picketage/announcement/action",
    query: { type: isUpdate ? "update" : "add", id },
  });
};

interface IDetail {
  show: boolean;
  id?: number;
}

const detail = reactive<IDetail>({
  show: false,
  id: void 0,
});

const onClickDetail = (row: any) => {
  detail.id = row.id;
  detail.show = true;
};

const onCloseDetail = () => {
  detail.show = false;
  detail.id = void 0;
};
</script>
<style lang="scss" scoped>
.announcement {
  &-list {
    margin-top: 16px;
  }
}
</style>
