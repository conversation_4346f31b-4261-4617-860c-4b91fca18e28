<template>
  <el-card>
    <div class="notice">
      <div class="notice-search">
        <el-form
          ref="elFormRef"
          :inline="true"
          :model="state.queryForm"
          @keyup.enter="getDataList()"
        >
          <el-form-item prop="goodsOrderId">
            <el-input
              v-model="state.queryForm.goodsOrderId"
              placeholder="商品订单号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="status">
            <fast-select
              style="width:200px"
              v-model="state.queryForm.status"
              dict-type="pay_status"
              clearable
              placeholder="订单状态"
            ></fast-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="resetForm()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="notice-list">
        <el-table
          v-loading="state.dataListLoading"
          :data="state.dataList"
          border
          stripe
          show-overflow-tooltip
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="goodsId"
            label="商品ID"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="goodsName"
            label="商品名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="amount"
            label="金额/元"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="status"
            label="订单状态"
            header-align="center"
            align="center"
            dict-type="pay_status"
          ></fast-table-column>
          <el-table-column
            width="250"
            prop="goodsOrderId"
            label="商品订单号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            header-align="center"
            align="center"
          ></el-table-column>
        </el-table>
      </div>
      <div class="notice-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive, ref } from "vue";
import { useRouter } from "vue-router";

const state: IHooksOptions = reactive({
  dataListUrl: "/pay/goodsOrder/page",
  queryForm: {
    goodsOrderId: "",
    status: "",
  },
});
const router = useRouter();
const elFormRef = ref();

const resetForm = () => {
  elFormRef.value.resetFields();
  getDataList(1);
};

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);
</script>
<style lang="scss" scoped>
.notice {
  &-list {
    margin-top: 16px;
  }
}
</style>
