<template>
  <el-card>
    <div class="action">
      <div class="action-title">中标结果公示</div>
      <div class="action-content">
        <el-form
          ref="dataFormRef"
          :model="state.dataForm"
          :rules="state.dataRules"
          label-width="140px"
          class="action-content-form"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="packageName" label="采购计划">
                <div style="display: flex; align-items: center; width: 100%">
                  <el-input
                    readonly
                    v-model="state.dataForm.packageName"
                    placeholder="采购计划"
                    @click="onSelectPlan"
                    disabled
                  ></el-input>
                  <el-button
                    type="primary"
                    style="margin-left: 10px"
                    @click="onSelectPlan"
                  >
                    选择
                  </el-button>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="packageNo" label="采购计划编号">
                <el-input
                  v-model="state.dataForm.packageNo"
                  placeholder="采购计划编号"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item prop="winBidder" label="中标人">
                <el-input
                  v-model="state.dataForm.winBidder"
                  placeholder="中标人"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item prop="title" label="公示标题">
                <el-input
                  v-model="state.dataForm.title"
                  placeholder="公示标题"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item prop="makeType" label="公告公示">
                <el-radio-group v-model="state.dataForm.makeType">
                  <el-radio label="1">文本模式</el-radio>
                  <el-radio label="0">上传pdf公告文件</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-if="state.dataForm.makeType === '1'">
            <el-col :span="24">
              <el-form-item prop="content" label="公示内容">
                <WangEditor
                  v-model="state.dataForm.content"
                  placeholder="公示内容"
                ></WangEditor>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20" v-else>
            <el-col :span="24">
              <el-form-item prop="fileList" label="pdf公告文件">
                <div class="info_upload">
                  <div class="info_upload_action">
                    <el-upload
                      :action="constant.uploadUrl"
                      :headers="{ Authorization: cache.getToken() }"
                      :before-upload="beforeUploadFile"
                      :on-success="handleSuccessFile"
                      :show-file-list="false"
                    >
                      <el-button
                        type="primary"
                        :disabled="(state.dataForm.fileList ?? []).length >= 1"
                        >上传</el-button
                      >
                      <template #tip>
                        <div class="el-upload__tip">支持pdf格式</div>
                      </template>
                    </el-upload>
                  </div>
                  <div class="info_upload_list">
                    <div
                      class="info_upload_list_item"
                      v-for="(item, index) in state.dataForm.fileList"
                    >
                      <div class="info_upload_list_item_text">
                        {{ item.name }}
                      </div>
                      <div class="info_upload_list_item_action">
                        <el-icon @click="onDeleteFile(index)"><DeleteFilled /></el-icon>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item prop="attachmentList" label="附件">
                <div class="info_upload">
                  <div class="info_upload_action">
                    <el-upload
                      :action="constant.uploadUrl"
                      :headers="{ Authorization: cache.getToken() }"
                      :before-upload="beforeUpload"
                      :on-success="handleSuccess"
                      :show-file-list="false"
                    >
                      <el-button type="primary">上传</el-button>
                      <template #tip>
                        <div class="el-upload__tip">
                          支持word/excel/pdf/jpg/gif/png/zip/rar等格式
                        </div>
                      </template>
                    </el-upload>
                  </div>
                  <div class="info_upload_list">
                    <div
                      class="info_upload_list_item"
                      v-for="(item, index) in state.dataForm.attachmentList"
                    >
                      <div class="info_upload_list_item_text">
                        {{ item.name }}
                      </div>
                      <div class="info_upload_list_item_action">
                        <el-icon @click="onDeleteAttachment(index)"
                          ><DeleteFilled
                        /></el-icon>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="action-content-action">
          <el-button type="primary" @click="onClickSubmit">提交</el-button>
          <el-button type="primary" plain @click="onClickTemp">暂存</el-button>
          <el-button @click="onClickReturn">返回</el-button>
        </div>
      </div>
      <PlanSelect
        :show="state.planSelect.show"
        from="announcement"
        @on-select="onSelectedPlan"
      ></PlanSelect>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import PlanSelect from "@/views/picketage/components/PlanSelect.vue";
import { FormRules, UploadProps, ElMessage } from "element-plus";
import WangEditor from "@/components/wang-editor/index.vue";
import constant from "@/utils/constant";
import cache from "@/utils/cache";
import { DeleteFilled } from "@element-plus/icons-vue";
import {
  getAnnouncementInfo,
  IActionAnnouncement,
  saveAnnouncement,
  saveTempAnnouncement,
} from "@/api/picketage/announcement";
import { useRoute, useRouter } from "vue-router";
import { closeTab } from "@/utils/tabs";

const router = useRouter();
const route = useRoute();

interface IAttachmentItem {
  name: string;
  url: string;
  size: number;
  platform: string;
}

interface IDataForm {
  id?: number;
  packageId?: number;
  packageNo: string;
  packageName: string;
  title: string;
  winBidder: string;
  makeType: string;
  content: string;
  fileList: IAttachmentItem[];
  attachmentList: IAttachmentItem[];
}

interface ICompanyItem {
  bidderId: number;
  bidderName: string;
  contactName: string;
  contactPhone: string;
  finalQuotation?: number;
  serviceCharge?: number;
}

interface IState {
  type: string;
  dataForm: IDataForm;
  dataRules: FormRules;
  planSelect: {
    show: boolean;
  };
  companyList: ICompanyItem[];
}

const state = reactive<IState>({
  type: "add",
  dataForm: {
    id: void 0,
    packageId: void 0,
    packageNo: "",
    packageName: "",
    title: "",
    winBidder: "",
    makeType: "1",
    content: "",
    fileList: [],
    attachmentList: [],
  },
  dataRules: {
    packageName: [
      { required: true, message: "请选择采购计划", trigger: ["change", "blur"] },
    ],
    packageNo: [{ required: true, message: "请输入采购计划编号", trigger: "blur" }],
    title: [{ required: true, message: "请输入通知书标题", trigger: "blur" }],
    winBidder: [{ required: true, message: "请输入中标人", trigger: "blur" }],
    content: [{ required: true, message: "请输入公示内容", trigger: "blur" }],
    fileList: [{ required: true, message: "请上传pdf公告文件", trigger: "blur" }],
  },
  planSelect: {
    show: false,
  },
  companyList: [],
});

const dataFormRef = ref();

onMounted(() => {
  const id = route.query?.id;
  const type = route.query?.type;
  state.type = type as string;

  if (type === "update" && id) {
    state.dataForm.id = (id as unknown) as number;
    GetAnnouncementInfo(state.dataForm.id);
  }
});

const GetAnnouncementInfo = (id: number) => {
  getAnnouncementInfo(id).then((res: any) => {
    if (res.code === 0) {
      state.dataForm.packageId = res.data.packageId;
      state.dataForm.packageNo = res.data.packageNo;
      state.dataForm.packageName = res.data.packageName;
      state.dataForm.title = res.data.title;
      state.dataForm.winBidder = res.data.winBidder;
      state.dataForm.makeType = res.data.makeType + "";
      state.dataForm.content = res.data.content;
      if (res.data.makeType == "0" && (res.data.contentAttach?.url ?? "") !== "") {
        let newAttachmentItem = {
          name: res.data.contentAttach.name,
          url: res.data.contentAttach.url,
          size: res.data.contentAttach.size,
          platform: res.data.contentAttach.platform,
        };
        state.dataForm.attachmentList.push(newAttachmentItem);
      }
      state.dataForm.attachmentList = res.data.bulletinAttachs ?? [];
    }
  });
};

const onSelectPlan = () => {
  state.planSelect.show = true;
};

const onSelectedPlan = (row: any) => {
  state.dataForm.packageId = row.packageId;
  state.dataForm.packageNo = row.packageNo;
  state.dataForm.packageName = row.packageName;
  state.dataForm.winBidder = row.winBidder;
  state.planSelect.show = false;
};

const onClickSubmit = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      dataFormRef.value.validate((valid: boolean) => {
        if (valid) {
          let reqData: IActionAnnouncement = {
            id: state.type === "add" ? void 0 : state.dataForm.id,
            packageId: state.dataForm.packageId,
            packageNo: state.dataForm.packageNo,
            packageName: state.dataForm.packageName,
            title: state.dataForm.title,
            winBidder: state.dataForm.winBidder,
            makeType: state.dataForm.makeType,
            content: state.dataForm.makeType === "1" ? state.dataForm.content : "",
            contentAttach:
              state.dataForm.makeType === "0" ? state.dataForm.fileList[0] : void 0,
            bulletinAttachs: state.dataForm.attachmentList,
          };
          saveAnnouncement(reqData).then((res: any) => {
            if (res.code === 0) {
              closeTab(router, route);
              ElMessage.success("发送成功");
            }
          });
        }
      });
    }
  });
};

const onClickTemp = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      let reqData: IActionAnnouncement = {
        id: state.type === "add" ? void 0 : state.dataForm.id,
        packageId: state.dataForm.packageId,
        packageNo: state.dataForm.packageNo,
        packageName: state.dataForm.packageName,
        title: state.dataForm.title,
        winBidder: state.dataForm.winBidder,
        makeType: state.dataForm.makeType,
        content: state.dataForm.makeType === "1" ? state.dataForm.content : "",
        contentAttach:
          state.dataForm.makeType === "0" ? state.dataForm.attachmentList[0] : void 0,
      };
      saveTempAnnouncement(reqData).then((res: any) => {
        if (res.code === 0) {
          closeTab(router, route);
          ElMessage.success("暂存成功");
        }
      });
    }
  });
};

const onClickReturn = () => {
  closeTab(router, route);
};

const beforeUploadFile: UploadProps["beforeUpload"] = (file) => {
  console.log(file);
  let fileNameList = file.name.split(".");
  let fileType = fileNameList[fileNameList.length - 1];
  if (["pdf"].indexOf(fileType) == -1) {
    ElMessage.error("请上传正确的文件格式");
    return false;
  }
  // if (file.size / 1024 / 1024 / 1024 / 1024 > 1) {
  // 	ElMessage.error('文件大小不能超过100M')
  // 	return false
  // }
  return true;
};

const handleSuccessFile: UploadProps["onSuccess"] = (res, file) => {
  if (res.code === 0) {
    let newAttachmentItem = {
      platform: res.data.platform,
      url: res.data.url,
      name: res.data.name,
      size: res.data.size,
    };
    let newFileList = JSON.parse(JSON.stringify(state.dataForm.fileList));
    newFileList.push(newAttachmentItem);
    state.dataForm.fileList = newFileList;
  } else {
    ElMessage.error("上传失败：" + res.msg);
  }
};

const beforeUpload: UploadProps["beforeUpload"] = (file) => {
  console.log(file);
  let fileNameList = file.name.split(".");
  let fileType = fileNameList[fileNameList.length - 1];
  if (
    [
      "jpg",
      "png",
      "jpeg",
      "doc",
      "docx",
      "xls",
      "pdf",
      "xlsx",
      "gif",
      "zip",
      "rar",
    ].indexOf(fileType) == -1
  ) {
    ElMessage.error("请上传正确的文件格式");
    return false;
  }
  // if (file.size / 1024 / 1024 / 1024 / 1024 > 1) {
  // 	ElMessage.error('文件大小不能超过100M')
  // 	return false
  // }
  return true;
};

const handleSuccess: UploadProps["onSuccess"] = (res, file) => {
  if (res.code === 0) {
    let newAttachmentItem = {
      platform: res.data.platform,
      url: res.data.url,
      name: res.data.name,
      size: res.data.size,
    };
    let newAttachmentList = JSON.parse(JSON.stringify(state.dataForm.attachmentList));
    newAttachmentList.push(newAttachmentItem);
    state.dataForm.attachmentList = newAttachmentList;
  } else {
    ElMessage.error("上传失败：" + res.msg);
  }
};

const onDeleteFile = (index: number) => {
  let newFileList = JSON.parse(JSON.stringify(state.dataForm.fileList));
  newFileList = newFileList.filter((item: IAttachmentItem, ele: number) => {
    return index !== ele;
  });
  state.dataForm.fileList = newFileList;
};

const onDeleteAttachment = (index: number) => {
  let newAttachmentList = JSON.parse(JSON.stringify(state.dataForm.attachmentList));
  newAttachmentList = newAttachmentList.filter((item: IAttachmentItem, ele: number) => {
    return index !== ele;
  });
  state.dataForm.attachmentList = newAttachmentList;
};
</script>
<style lang="scss" scoped>
.action {
  &-title {
    font-size: 16px;
    font-weight: 700;
  }
  &-content {
    margin: 0 100px;
    margin-top: 16px;
    &-form {
      &-number {
        :deep(.el-input__inner) {
          text-align: left;
        }
      }
    }
    &-action {
      margin-top: 16px;
      display: flex;
      justify-content: center;
    }
  }
}

.info_upload {
  &_list {
    &_item {
      display: flex;
      align-items: center;
      &_text {
        color: #409eff;
        margin-right: 10px;
        cursor: pointer;
      }
      &_action {
        color: #545252;
        cursor: pointer;
      }
    }
  }
}
</style>
