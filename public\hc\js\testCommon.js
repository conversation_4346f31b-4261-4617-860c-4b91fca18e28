/*
 * @Descripttion: 
 * @Author: cui<PERSON><PERSON><PERSON>
 * @Date: 2023-09-08 15:20:29
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2023-09-15 17:01:58
 */
/**
 * 通用测试数据及常用方法，不同demo可能仅用到其中一部分
 * */
const $testCommon = {
  
  /**
   * 签署
   *
   * @param fileDto 原文件
   */
  onSignFile: function(fileDto) {
    var res = {
      /**
       * 已签署文件回传业务系统文件服务的请求头 不传则默认取apiDto.fileHeader
       * */
      uploadHeader: {},
      /**
       * 已签署文件回传业务系统文件服务的表单项map
       * */
      uploadForm: {},
      /**
       * 已签署文件回传业务系统文件服务的文件表单项
       * */
      uploadFormFile: ''
    };
    let getUUID = function() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
        return (c === 'x' ? (Math.random() * 16) | 0 : 'r&0x3' | '0x8').toString(16)
      })
    }
    switch (fileDto.kind) {
      // pdf
      case 1:
        // 服务端模式下已签署文件显示名称
        res.pdfName = fileDto.pdfName;
        // 回传已签署文件时的表单上传文件名称 不传则取pdfName
        res.formName = getUUID() + '.pdf';
        // 已签署文件下载相对路径 此项若不传则会调用onSignFileParseSrc获取已签署文件下载路径
        res.pdfSrc = res.formName;
        break;
      // ofd
      case 2:
        // 服务端模式下已签署文件显示名称
        res.ofdName = fileDto.ofdName;
        // 回传已签署文件时的表单上传文件名称 不传则取ofdName
        res.formName = getUUID() + '.ofd';
        // 已签署文件下载相对路径 此项若不传则会调用onSignFileParseSrc获取已签署文件下载路径
        res.ofdSrc = res.formName;
        break;
    }
    return res;
  }
};