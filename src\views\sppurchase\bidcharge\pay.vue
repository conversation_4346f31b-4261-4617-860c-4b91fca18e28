<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-04-01 10:19:02
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-04-17 09:48:27
-->
<template>
  <el-card>
    <div class="action">
      <el-form
        ref="infoRef"
        :model="state.dataForm"
        :rules="state.dataRules"
        label-width="150px"
        class="normalform"
      >
        <ContentTitle title="项目信息" class="title"></ContentTitle>
        <el-form-item prop="packageName" label="采购计划名称">
          <el-select
            v-model="state.dataForm.packageName"
            placeholder="采购计划名称"
            @change="changePlan"
          >
            <el-option
              v-for="data in state.dataList"
              :key="data.packageId"
              :label="data.packageName"
              :value="data.packageId"
            >
              {{ data.packageName }}
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="packageNo" label="采购计划编号">
          {{ state.dataForm.packageNo }}
        </el-form-item>
        <el-form-item prop="winPrice" label="中标服务费金额(元)">
          {{ state.dataForm.winPrice }} 元
        </el-form-item>
        <ContentTitle title="供应商信息" class="title"></ContentTitle>
        <el-form-item label="供应商">
          {{ state.dataForm.winBidder }}
        </el-form-item>
        <el-form-item label="联系人">
          {{ state.dataForm.contactName }}
        </el-form-item>
        <el-form-item label="联系电话">
          {{ state.dataForm.contactPhone }}
        </el-form-item>
        <ContentTitle title="支付信息" class="title"></ContentTitle>
        <el-form-item prop="payType" label="支付方式">
          <el-radio-group v-model="state.dataForm.payType" size="large">
            <el-radio-button value="AlipaySaoma" label="AlipaySaoma"
              ><svg-icon class="icon" icon="icon-alipay-circle-fill"></svg-icon
              >支付宝支付</el-radio-button
            >
            <el-radio-button value="MicroNative" label="MicroNative"
              ><svg-icon class="icon" icon="icon-weixin"></svg-icon
              >微信支付</el-radio-button
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="invoiceType" label="发票类型">
          <fast-radio-group
            v-model="state.dataForm.invoiceType"
            dict-type="invoice_type"
            @change="changeType"
          ></fast-radio-group>
        </el-form-item>
        <div
          class="invoiceBox"
          v-if="state.dataForm.invoiceType != '99'"
          v-loading="state.invoiceLoading"
        >
          <el-button
            link
            type="primary"
            v-if="!state.invoiceHeader"
            @click="openInvoiceTitle"
            >设置发票信息</el-button
          >
          <el-button
            v-if="state.dataForm.invoiceType == '01' && !state.defaultAddress"
            link
            type="primary"
            @click="openInvoiceAddress"
            >设置邮寄地址</el-button
          >
          <el-descriptions :column="2" border v-if="state.invoiceHeader">
            <template #title> <p class="titled">发票信息</p> </template>
            <el-descriptions-item label="发票抬头">{{
              state.invoiceHeader.invoiceHeader
            }}</el-descriptions-item>
            <el-descriptions-item label="纳税人识别号">{{
              state.invoiceHeader.creditCode
            }}</el-descriptions-item>
            <el-descriptions-item label="电子邮箱">{{
              state.invoiceHeader.email
            }}</el-descriptions-item>
            <el-descriptions-item label="电话">{{
              state.invoiceHeader.registerPhone
            }}</el-descriptions-item>
            <el-descriptions-item label="注册场所地址">{{
              state.invoiceHeader.registerAddress
            }}</el-descriptions-item>
            <el-descriptions-item label="开户行">{{
              state.invoiceHeader.bankDeposit
            }}</el-descriptions-item>
            <el-descriptions-item label="开户账号">{{
              state.invoiceHeader.basicAccount
            }}</el-descriptions-item>
          </el-descriptions>
          <el-descriptions
            :column="2"
            border
            v-if="state.dataForm.invoiceType == '01' && state.defaultAddress"
          >
            <template #title> <p class="titled">邮寄地址</p> </template>
            <el-descriptions-item label="收件人">{{
              state.defaultAddress.recipient
            }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{
              state.defaultAddress.telePhone
            }}</el-descriptions-item>
            <el-descriptions-item label="收件地址">{{
              state.defaultAddress.address
            }}</el-descriptions-item>
            <el-descriptions-item label="邮政编码">{{
              state.defaultAddress.postalCode
            }}</el-descriptions-item>
            <el-descriptions-item label="电子邮箱">{{
              state.defaultAddress.email
            }}</el-descriptions-item>
          </el-descriptions>
        </div>
        <el-form-item prop="notes" label="备注">
          <el-input
            type="textarea"
            v-model="state.dataForm.notes"
            placeholder="备注"
          ></el-input>
        </el-form-item>
        <div class="action_btn">
          <el-tag type="success" v-if="flag">已支付</el-tag>
          <el-button v-else type="primary" @click="submitHandle">立即支付</el-button>
          <el-button @click="closentab">返回</el-button>
        </div>
      </el-form>
    </div>
  </el-card>
  <!-- 支付 -->
  <paycode ref="paycodeRef" v-if="state.paycodeShow" @callbk="closentab()"></paycode>
  <!-- 发票抬头 -->
  <title-addorupdate
    ref="titleAddorupdateRef"
    v-if="state.titleAddorupdateShow"
    @refresh-data-list="getInvoiceHeader(state.dataForm.invoiceType)"
  ></title-addorupdate>
  <!-- 邮寄地址 -->
  <address-addorupdate
    ref="addressAddorupdateRef"
    v-if="state.addressAddorupdateShow"
    @refresh-data-list="getDefaultAddress()"
  ></address-addorupdate>
</template>
<script setup lang="ts">
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import { closeTab } from "@/utils/tabs";
import {
  useGetBidderListApi,
  useOrderScanCodeBuyApi,
  useOrderGetPayStatusApi,
  useGetDefaultAddressApi,
  useGetInvoiceHeaderApi,
} from "@/api/pnotice";
import { useRoute, useRouter } from "vue-router";
import { nextTick, reactive, ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import paycode from "./paycode.vue";
import titleAddorupdate from "../invoice/title-addorupdate.vue";
import addressAddorupdate from "../invoice/address-addorupdate.vue";

const router = useRouter();
const route = useRoute();
const paycodeRef = ref();
const titleAddorupdateRef = ref();
const addressAddorupdateRef = ref();
const infoRef = ref();
const state = reactive({
  dataForm: {
    invoiceType: "99",
    payType: "",
    notes: "",
  },
  dataList: [],
  dataRules: {
    invoiceType: [{ required: true, message: "必填项", trigger: "change" }],
    payType: [{ required: true, message: "必填项", trigger: "change" }],
  },
  paycodeShow: false,
  titleAddorupdateShow: false,
  addressAddorupdateShow: false,
  flag: false,
  defaultAddress: null,
  invoiceHeader: null,
  invoiceLoading: false,
});

onMounted(() => {
  state.dataForm.packageId = route.query.pid;
  getDetail();
});

// 支付
const topay = (res) => {
  state.paycodeShow = true;
  nextTick(() => {
    paycodeRef.value.init(res, state.dataForm.id);
  });
};
// 返回
const closentab = () => {
  closeTab(router, route);
};
// 获取支付状态
const getPayStatus = () => {
  useOrderGetPayStatusApi(state.dataForm.id).then((res) => {
    if (res.code == 0) {
      state.flag = res.data == 0 ? false : true;
    }
  });
};
// 计划分页
const getDetail = () => {
  useGetBidderListApi().then((res) => {
    if (res.code == 0) {
      state.dataList = res.data;
      nowPlan(state.dataForm.packageId);
      console.log(state.dataForm);

      getPayStatus();
    }
  });
};
// 当前 计划
const nowPlan = (id) => {
  let res = state.dataList.filter((item) => item.packageId == id);
  Object.assign(state.dataForm, res[0]);
};
// 发票类型
const changeType = (val) => {
  state.invoiceLoading = true;
  if (val == "01") {
    getDefaultAddress();
    getInvoiceHeader("01");
  } else if (val == "00") {
    getInvoiceHeader("00");
  } else {
    state.dataForm.addressId = "";
    state.dataForm.headerId = "";
  }
  state.invoiceLoading = false;
};
// 切换计划
const changePlan = (val) => {
  nowPlan(val);
};
// 表单提交
const submitHandle = () => {
  infoRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    } else {
      let params = {
        goodsId: state.dataForm.id,
        payType: state.dataForm.payType,
        headerId: state.dataForm.headerId,
        addressId: state.dataForm.addressId,
        notes: state.dataForm.notes,
      };
      if (state.dataForm.invoiceType == "99") {
        delete params.headerId;
        delete params.addressId;
      } else {
        // 发票赋值
      }
      useOrderScanCodeBuyApi(params).then((res) => {
        if (res.code == 0) {
          if (state.dataForm.payType == "AlipaySaoma") {
            // form提交
            if (res.data.urlCode) {
              document.write(res.data.urlCode);
              document.querySelector("form[name=punchout_form]").submit();
            }
          } else {
            // 二维码
            topay(res.data);
          }
        }
      });
    }
  });
};

// 默认地址
const getDefaultAddress = () => {
  useGetDefaultAddressApi().then((res) => {
    if (res.code == 0) {
      state.defaultAddress = res.data ? res.data : null;
      state.dataForm.addressId = state.defaultAddress ? state.defaultAddress.id : "";
    }
  });
};
// 发票信息
const getInvoiceHeader = (type) => {
  useGetInvoiceHeaderApi(type).then((res) => {
    if (res.code == 0) {
      state.invoiceHeader = res.data ? res.data[0] : null;
      if (type == "00") {
        state.dataForm.addressId = "";
      }
      state.dataForm.headerId = state.invoiceHeader ? state.invoiceHeader.id : "";
    }
  });
};
const openInvoiceTitle = () => {
  state.titleAddorupdateShow = true;
  nextTick(() => {
    titleAddorupdateRef.value.init();
  });
};
const openInvoiceAddress = () => {
  state.addressAddorupdateShow = true;
  nextTick(() => {
    addressAddorupdateRef.value.init();
  });
};
</script>
<style lang="scss" scoped>
.action {
  .title {
    margin-bottom: 20px;
  }
  .action_btn {
    margin-left: 150px;
  }
}
.icon {
  margin-right: 5px;
}
.invoiceBox {
  margin: 20px 0 20px 150px;
}
.titled {
  margin: 20px 0 0;
}
</style>
