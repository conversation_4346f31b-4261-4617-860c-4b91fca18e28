<template>
  <el-card>
    <div class="project">
      <div class="project-search">
        <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
          <el-form-item>
            <el-input
              placeholder="采购计划编号"
              clearable
              v-model="state.queryForm.packageNo"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              placeholder="采购计划名称"
              clearable
              v-model="state.queryForm.packageName"
              style="width: 200px"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-select
              placeholder="采购方式"
              style="width: 200px"
              clearable
              v-model="state.queryForm.packageType"
            >
              <el-option label="公开招标" value="1"></el-option>
              <el-option label="邀请招标" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <fast-select
              style="width: 200px"
              v-model="state.queryForm.auditStatus"
              dict-type="audit_status"
              clearable
              placeholder="审核状态"
            ></fast-select>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="selectDate"
              type="daterange"
              unlink-panels
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="onChangeSelectDate"
            />
          </el-form-item>
          <el-form-item>
            <el-button @click="getDataList()" type="primary">查询</el-button>
            <el-button @click="onClickReset()">重置</el-button>
            <!-- <el-button type="primary" @click="addOrUpdateHandle(false)" v-auth="'purchase:package:save'">新增</el-button> -->
          </el-form-item>
        </el-form>
      </div>
      <div class="project-list">
        <el-table
          v-loading="state.dataListLoading"
          show-overflow-tooltip
          :data="state.dataList"
          border
        >
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="projectName"
            label="项目"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageNo"
            label="采购计划编号"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="packageName"
            label="采购计划名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="packageType"
            label="采购方式"
            dict-type="package_type"
          ></fast-table-column>
          <el-table-column
            prop="packageBudget"
            label="本次预算金额（元）"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="managerName"
            label="项目经理"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="creatorName"
            label="发起人"
            header-align="center"
            align="center"
          ></el-table-column>
          <fast-table-column
            prop="auditStatus"
            label="状态"
            dict-type="audit_status"
          ></fast-table-column>
          <el-table-column
            prop="createTime"
            label="提交时间"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            label="操作"
            fixed="right"
            header-align="center"
            align="center"
            width="240"
          >
            <template #default="scope">
              <el-button
                v-if="
                  scope.row.auditStatus == '1' ||
                  scope.row.auditStatus == '4' ||
                  scope.row.auditStatus == '6'
                "
                v-auth="'purchase:package:update'"
                type="primary"
                link
                @click="addOrUpdateHandle(true, scope.row.flowId)"
              >
                编辑
              </el-button>
              <el-button type="primary" link @click="onClickDetail('watch', scope.row)">
                查看
              </el-button>
              <el-button
                v-if="scope.row.auditStatus == '3'"
                type="primary"
                link
                @click="onClickQuotation(scope.row.id)"
              >
                报价信息
              </el-button>
              <el-button type="primary" link @click="onClickTrack(scope.row.id)">
                计划追踪
              </el-button>
              <el-button
                v-if="scope.row.display"
                type="primary"
                link
                @click="onClickDetail('audit', scope.row)"
                v-auth="'purchase:package:audit'"
              >
                审核
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="project-page">
        <el-pagination
          :current-page="state.pageNo"
          :page-sizes="state.pageSizes"
          :page-size="state.pageSize"
          :total="state.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
        >
        </el-pagination>
      </div>
      <Detail
        :show="detail.show"
        :title="detail.title"
        :id="detail.id"
        :type="detail.type"
        @audit-success="onAuditSuccess"
        @close="onCloseDetail"
      ></Detail>
    </div>
  </el-card>
</template>
<script setup lang="ts">
import { reactive, ref } from "vue";
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { useRouter } from "vue-router";
import Detail from "./components/Detail.vue";
import dayjs from "dayjs";

const router = useRouter();

const selectDate = ref([]);

interface IDetail {
  show: boolean;
  title: string;
  id?: number;
  type: string;
}

const detail = reactive<IDetail>({
  show: false,
  title: "标题",
  id: void 0,
  type: "watch",
});

const state = reactive<IHooksOptions>({
  queryForm: {
    packageNo: "",
    packageName: "",
    packageType: "",
    auditStatus: "",
    startTime: void 0,
    endTime: void 0,
  },
  dataListUrl: "/purchase/package/page",
  deleteUrl: "/purchase/package",
});

const { getDataList, sizeChangeHandle, currentChangeHandle, deleteBatchHandle } = useCrud(
  state
);

const onChangeSelectDate = () => {
  if (selectDate.value && selectDate.value.length === 2) {
    state.queryForm.startTime = dayjs(selectDate.value[0]).format("YYYY-MM-DD");
    state.queryForm.endTime = dayjs(selectDate.value[1]).format("YYYY-MM-DD");
  } else {
    state.queryForm.startTime = void 0;
    state.queryForm.endTime = void 0;
  }
};

const addOrUpdateHandle = (isUpdate: boolean, id?: number) => {
  router.push({
    path: "/workbench/index",
    query: { id, type: isUpdate ? "update" : "add", activeTab: "5" },
  });
};

const onClickDetail = (type: string, row: any) => {
  detail.title = row.packageName;
  detail.id = row.id;
  detail.show = true;
  detail.type = type;
};

const onClickTrack = (id: number) => {
  router.push({
    path: "/purchase/plan/track",
    query: { id },
  });
};

const onAuditSuccess = () => {
  detail.show = false;
  detail.title = "标题";
  detail.id = void 0;
  detail.type = "watch";
  getDataList();
};

const onCloseDetail = () => {
  detail.show = false;
  detail.title = "标题";
  detail.id = void 0;
  detail.type = "watch";
};

const onClickReset = () => {
  let newQueryForm = {
    queryForm: {
      packageNo: "",
      packageName: "",
      packageType: "",
      auditStatus: "",
      startTime: void 0,
      endTime: void 0,
    },
  };
  state.queryForm = newQueryForm;
  selectDate.value = [];
  getDataList();
};

const onClickQuotation = (id: number) => {
  router.push({
    path: "/purchase/plan/quotation",
    query: { id },
  });
};
</script>
<style lang="scss" scoped>
.project {
  &-list {
    margin-top: 16px;
  }
}
</style>
