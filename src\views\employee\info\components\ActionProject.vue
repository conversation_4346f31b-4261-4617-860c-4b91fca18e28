<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    draggable
  >
    <div class="action-project">
      <el-form :model="dataForm" :rules="dataRule" ref="dataFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="startTime" label="开始时间">
              <el-date-picker
                v-model="dataForm.startTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="开始时间"
                style="width: 100%"
                :disabled-date="disabledStartTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="endTime" label="结束时间">
              <el-date-picker
                v-model="dataForm.endTime"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="结束时间"
                style="width: 100%"
                :disabled-date="disabledEndTime"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="projectName" label="项目名称">
              <el-input v-model="dataForm.projectName" placeholder="项目名称"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="projectRole" label="项目角色">
              <el-input v-model="dataForm.projectRole" placeholder="项目角色"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="projectRemark" label="项目描述">
              <el-input
                v-model="dataForm.projectRemark"
                placeholder="项目描述"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="projectAchievement" label="项目业绩">
              <el-input
                v-model="dataForm.projectAchievement"
                placeholder="项目业绩"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { useProjectSubmitApi, getProjectWorkInfo } from "@/api/employee";
import { ref, reactive } from "vue";
import { ElMessage } from "element-plus";

const dataForm = reactive({
  id: "",
  startTime: "",
  endTime: "",
  projectName: "",
  projectRole: "",
  projectRemark: "",
  projectAchievement: "",
});

const visible = ref(false);
const dataFormRef = ref();

const dataRule = ref({
  projectName: [{ required: true, message: "项目名称不能为空", trigger: "blur" }],
});

const init = (id) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  // id 存在则为修改
  if (id) {
    GetProjectWorkInfo(id);
  }
};

const disabledStartTime = (time) => {
  if (time && dataForm.endTime) {
    return time.getTime() > new Date(dataForm.endTime).getTime();
  } else {
    return false;
  }
};

const disabledEndTime = (time) => {
  if (time && dataForm.startTime) {
    return time.getTime() < new Date(dataForm.startTime).getTime();
  } else {
    return false;
  }
};

const GetProjectWorkInfo = (id) => {
  getProjectWorkInfo(id).then((response) => {
    Object.assign(dataForm, response.data);
  });
};

const emit = defineEmits(["refreshDataList"]);

const submitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    useProjectSubmitApi(dataForm).then(() => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        },
      });
    });
  });
};

defineExpose({
  init,
});
</script>
<style lang="scss" scoped></style>
