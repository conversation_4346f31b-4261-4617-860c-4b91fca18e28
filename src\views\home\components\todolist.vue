<template>
  <div class="todo">
    <div class="todo-title">
      <HomeTitle icon="icon-daishenpi" title="OA待办事项">
        <template #right>
          <el-link type="primary" :underline="false" @click="moreSpClick()">更多</el-link>
        </template>
      </HomeTitle>
    </div>
    <div class="todo-content">
      <el-tabs class="todo-tabs">
        <el-tab-pane>
          <template #label>
            <el-badge :value="dspNum" class="item" v-if="dspNum > 0">
              <span>待审批</span>
            </el-badge>
            <span v-else>待审批</span>
          </template>
          <div v-if="dspList.length > 0">
            <div
              class="todo-content-item"
              v-for="(item, index) in dspList"
              :key="index"
              @click="onClickDetail(item, 'pending')"
            >
              <el-avatar
                shape="circle"
                :size="30"
                :src="item.avatar ?? defaultAvatar"
              ></el-avatar>
              <div class="user">
                <p class="type">{{ item.infoTypeLabel }}</p>
                <p class="name">发起人： {{ item.creatorName }}</p>
              </div>
              <div class="time">{{ item.createTime }}</div>
            </div>
          </div>
          <div style="margin: 20px auto; width: 100px" v-else>
            <svg-icon icon="icon-zanwuxiangguansousuoneirong" size="100px"></svg-icon>
          </div>
        </el-tab-pane>
        <el-tab-pane>
          <template #label>
            <el-badge :value="cswdNum" class="item" v-if="cswdNum > 0">
              <span>抄送我的</span>
            </el-badge>
            <span v-else>抄送我的</span>
          </template>
          <div v-if="cswdList.length > 0">
            <div
              class="todo-content-item"
              v-for="(item, index) in cswdList"
              :key="index"
              @click="onClickDetail(item, 'duplicate')"
            >
              <el-avatar
                shape="circle"
                :size="30"
                :src="item.avatar ?? defaultAvatar"
              ></el-avatar>
              <div class="user">
                <p class="type">{{ item.infoTypeLabel }}</p>
                <p class="name">发起人： {{ item.creatorName }}</p>
              </div>
              <div class="time">{{ item.createTime }}</div>
            </div>
          </div>
          <div style="margin: 20px auto; width: 100px" v-else>
            <svg-icon icon="icon-zanwuxiangguansousuoneirong" size="100px"></svg-icon>
          </div>
        </el-tab-pane>
        <el-tab-pane>
          <template #label>
            <el-badge :value="atwdNum" class="item" v-if="atwdNum > 0">
              <span>@我的</span>
            </el-badge>
            <span v-else>@我的</span>
          </template>
          <div v-if="atwdList.length > 0">
            <div
              class="todo-content-item"
              v-for="(item, index) in atwdList"
              :key="index"
              @click="onClickDetail(item, 'atme')"
            >
              <el-avatar
                shape="circle"
                :size="30"
                :src="item.avatar ?? defaultAvatar"
              ></el-avatar>
              <div class="user">
                <p class="type">{{ item.infoTypeLabel }}</p>
                <p class="name">发起人： {{ item.creatorName }}</p>
              </div>
              <div class="time">{{ item.createTime }}</div>
            </div>
          </div>
          <div style="margin: 20px auto; width: 100px" v-else>
            <svg-icon icon="icon-zanwuxiangguansousuoneirong" size="100px"></svg-icon>
          </div>
        </el-tab-pane>
      </el-tabs>
      <PendingDetail
        ref="pendingDetailRef"
        @refresh="onRefreshData('pending')"
      ></PendingDetail>
      <DuplicateDetail ref="duplicateDetailRef"></DuplicateDetail>
      <AtmeDetail ref="atmeDetailRef"></AtmeDetail>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useRouter } from "vue-router";
import HomeTitle from "./HomeTitle.vue";
import { nextTick, reactive, ref, onMounted } from "vue";
import { getWorkFlowPage } from "@/api/pnotice";
import defaultAvatar from "@/assets/avatar.png";
import PendingDetail from "@/views/approvalCenter/pending/detail.vue";
import DuplicateDetail from "@/views/approvalCenter/duplicate/detail.vue";
import AtmeDetail from "@/views/approvalCenter/atme/detail.vue";

const router = useRouter();
const dspList = ref([]);
const dspNum = ref(0);
const cswdList = ref([]);
const cswdNum = ref(0);
const atwdList = ref([]);
const atwdNum = ref(0);
onMounted(() => {
  GetPendingList();
  GetDuplicateList();
  GetAtmeList();
});

const GetPendingList = () => {
  let params = {
    order: "",
    asc: false,
    pageNo: 1,
    pageSize: 3,
    flowNo: "",
    date: "",
    startTime: "",
    endTime: "",
    flowType: "",
    queryType: 1,
  };
  getWorkFlowPage(params).then((res) => {
    dspList.value = res.data.list;
    dspNum.value = res.data.total;
  });
};

const GetDuplicateList = () => {
  let params2 = {
    order: "",
    asc: false,
    pageNo: 1,
    pageSize: 3,
    flowNo: "",
    date: "",
    startTime: "",
    endTime: "",
    flowType: "",
    queryType: 4,
    isRead: 0,
  };
  getWorkFlowPage(params2).then((res) => {
    cswdList.value = res.data.list;
    cswdNum.value = res.data.total;
  });
};

const GetAtmeList = () => {
  let params3 = {
    order: "",
    asc: false,
    pageNo: 1,
    pageSize: 3,
    flowNo: "",
    date: "",
    startTime: "",
    endTime: "",
    flowType: "",
    queryType: 5,
    isRead: 0,
  };
  getWorkFlowPage(params3).then((res) => {
    atwdList.value = res.data.list;
    atwdNum.value = res.data.total;
  });
};

// 审批点击更多
const moreSpClick = () => {
  router.push("/approvalCenter/pending/index");
};

const pendingDetailRef = ref();
const duplicateDetailRef = ref();
const atmeDetailRef = ref();

const onClickDetail = (row, type) => {
  if (type === "pending") {
    pendingDetailRef.value.init(row);
  } else if (type === "duplicate") {
    duplicateDetailRef.value.init(row);
  } else if (type === "atme") {
    atmeDetailRef.value.init(row);
  }
};

const onRefreshData = (type) => {
  if (type === "pending") {
    GetPendingList();
  } else if (type === "duplicate") {
    GetDuplicateList();
  } else if (type === "atme") {
    GetAtmeList();
  }
};
</script>
<style lang="scss" scoped>
.todo {
  background-color: #ffffff;
  padding: 16px 10px;
  border-radius: 4px;
  min-height: 283px;
  &-content {
    margin-top: 16px;
    &-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      border: 1px solid #eee;
      padding: 10px;
      margin-bottom: 4px;
      .user {
        margin: 0 10px;
        font-size: 14px;
        color: #333333;
        flex: 1;
        .name {
          color: #999;
        }
      }
      .time {
        font-size: 12px;
        color: #999;
      }
    }
  }
}
</style>
