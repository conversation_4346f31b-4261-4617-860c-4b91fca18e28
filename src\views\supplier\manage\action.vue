<template>
  <div class="action-info">
    <MemberAuth
      from="manage"
      :type="state.type"
      :userDetailsId="state.userDetailsId"
      :purchaserBidderId="state.purchaserBidderId"
    ></MemberAuth>
  </div>
</template>
<script setup lang="ts">
import { onMounted, reactive } from "vue";
import MemberAuth from "@/components/member-auth/index.vue";
import { useRoute } from "vue-router";

const route = useRoute();

interface IState {
  type: string;
  userDetailsId?: number;
  purchaserBidderId?: number;
}

const state = reactive<IState>({
  type: "watch",
  userDetailsId: void 0,
  purchaserBidderId: void 0,
});

onMounted(() => {
  state.type = route.query.type as string;
  if (route.query.userDetailsId) {
    state.userDetailsId = (route.query.userDetailsId as unknown) as number;
  }
  if (route.query.id) {
    state.purchaserBidderId = (route.query.id as unknown) as number;
  }
});
</script>
