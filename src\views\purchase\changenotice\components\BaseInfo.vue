<template>
  <div class="info">
    <el-form
      ref="infoRef"
      :model="dataForm"
      :rules="dataRules"
      label-width="170px"
      class="normalform"
    >
      <div class="action_base">
        <div class="action_title">
          <ContentTitle title="基本信息"></ContentTitle>
        </div>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="packageName" label="采购计划">
              <div style="display: flex; align-items: center; width: 100%">
                <el-input
                  readonly
                  v-model="dataForm.packageName"
                  placeholder="采购计划"
                ></el-input>
                <el-button type="primary" style="margin-left: 10px" @click="pickplan()"
                  >选择</el-button
                >
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="packageNo" label="采购计划编号">
              <el-input
                readonly
                v-model="dataForm.packageNo"
                placeholder="采购计划编号"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item prop="title" label="变更公告名称">
              <el-input v-model="dataForm.title" placeholder="变更公告名称"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="needAudit" label="是否内部审核">
              <el-radio-group v-model="dataForm.needAudit">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="timeChange" label="是否变更时间">
              <el-radio-group v-model="dataForm.timeChange">
                <el-radio :label="1">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <Fragment v-if="dataForm.timeChange == 1">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="signStartDate" label="报名开始时间">
                {{ dataForm.signStartDate }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="signEndDate" label="报名截止时间">
                <el-date-picker
                  v-model="dataForm.signEndDate"
                  type="datetime"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="报名截止时间"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="bidStartDate" label="报价开始时间">
                {{ dataForm.bidStartDate }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="bidEndDate" label="报价截止时间">
                <el-date-picker
                  v-model="dataForm.bidEndDate"
                  type="datetime"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="投标截止时间"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </Fragment>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item prop="makeType" label="公告公示">
              <el-radio-group v-model="dataForm.makeType">
                <el-radio :label="1">文本模式</el-radio>
                <el-radio :label="0">上传pdf公告文件</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-if="dataForm.makeType == '1'">
          <el-col :span="24">
            <el-form-item prop="content" label="公示内容">
              <WangEditor v-model="dataForm.content" placeholder="公示内容"></WangEditor>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20" v-else>
          <el-col :span="24">
            <el-form-item prop="contentAttach" label="pdf公告文件">
              <el-upload
                v-model:file-list="state.fileList"
                :headers="{ Authorization: cache.getToken() }"
                :action="constant.uploadUrl"
                :before-upload="beforeUpload"
                :on-success="handleSuccess"
                :on-remove="handleRemove"
                accept=".pdf"
                :limit="1"
              >
                <el-button type="primary" :disabled="state.fileList.length >= 1"
                  >上传</el-button
                >
                <template #tip>
                  <div class="el-upload__tip">支持pdf格式</div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <!-- 选择计划 -->
    <selectplan
      v-if="state.selectplanShow"
      ref="selectplanRef"
      @select-result="selectPlanres"
    ></selectplan>
  </div>
</template>
<script setup lang="ts">
import { reactive, nextTick, ref, inject, onMounted, watch } from "vue";
import ContentTitle from "@/views/purchase/project/components/ContentTitle.vue";
import selectplan from "./selectplan.vue";
import WangEditor from "@/components/wang-editor/index.vue";
import constant from "@/utils/constant";
import { ElMessage } from "element-plus";
import cache from "@/utils/cache";
import { DeleteFilled } from "@element-plus/icons-vue";

const state = reactive({
  selectplanShow: false,
  materialList: [],
  fileList: [],
});
const dataForm = inject("dataForm");
console.log(dataForm);

const dataRules = inject("dataRules");
const selectplanRef = ref();
const infoRef = ref();
const emit = defineEmits(["refreshProvide", "pickPlanres"]);

watch(dataForm, (newValue, oldValue) => {
  if (dataForm.contentAttach) {
    state.fileList = [].concat(dataForm.contentAttach);
  }
});

const handleSuccess = (res, file) => {
  if (res.code !== 0) {
    ElMessage.error("上传失败：" + res.msg);
    return false;
  }
  let newFileList = [].concat(res.data);
  state.fileList = newFileList;
  dataForm.contentAttach = Object.assign({}, res.data);
};
const beforeUpload = (file) => {
  let fileType = file.type.split("/")[1];
  if (["pdf"].indexOf(fileType) == -1) {
    ElMessage.error("格式不支持");
    return false;
  }
  return true;
};
const handleExceed = () => {
  ElMessage.error("文件超出限制个数");
};

// 选择计划
const pickplan = () => {
  state.selectplanShow = true;
  nextTick(() => {
    selectplanRef.value.init();
  });
};

// 选取采购计划回调
const selectPlanres = (res) => {
  if (res.packageId) {
    dataForm.packageId = res.packageId;
    dataForm.packageNo = res.packageNo;
    dataForm.packageName = res.packageName;
    emit("pickPlanres", res);
  }
};

// 表单提交
const submitHandle = () => {
  infoRef.value.validate((valid: boolean) => {
    if (!valid) {
      emit("refreshProvide", false);
      return false;
    }
    emit("refreshProvide", true);
  });
};

const handleRemove = (file, files) => {
  state.fileList = [];
  dataForm.contentAttach = null;
};

defineExpose({
  submitHandle,
});
</script>

<style lang="scss" scoped>
.info {
  width: 100%;
  margin-top: 10px;
}
::v-deep(.normalform .el-form-item__content) {
  width: 100%;
}
::v-deep(.el-date-editor.el-input) {
  width: 100%;
}
.list_file_content {
  margin-right: 5px;
}
.action_title {
  margin-bottom: 25px;
}

.info_upload {
  &_list {
    &_item {
      display: flex;
      align-items: center;
      &_text {
        color: #409eff;
        margin-right: 10px;
        cursor: pointer;
      }
      &_action {
        color: #545252;
        cursor: pointer;
      }
    }
  }
}
</style>
