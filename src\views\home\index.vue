<template>
  <div class="home">
    <div v-if="userStore?.user?.username === 'admin'"></div>
    <!-- 招采中心 -->
    <div v-else-if="userStore.authorityList.some((v: string) => v === 'portal:zczx')">
      <el-row :gutter="20" class="mb20">
        <el-col :span="8">
          <MyAudit> </MyAudit>
        </el-col>
        <el-col :span="8">
          <TODOList></TODOList>
        </el-col>
        <el-col :span="8">
          <Notice></Notice>
        </el-col>
        <el-col :span="16">
          <div class="home-left">
            <div class="home-left-quick">
              <QuickAccess></QuickAccess>
            </div>
            <div class="home-left-statistics">
              <PlanStatistics></PlanStatistics>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="home-right">
            <div class="home-right-bid home-left-quick">
              <BidProject></BidProject>
            </div>
            <div class="home-right-contact">
              <WinBidStatistics></WinBidStatistics>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 投标人 -->
    <div v-else-if="userStore.authorityList.some((v: string) => v === 'portal:tbr')">
      <el-alert type="warning" show-icon v-if="!userStore.isAuth" @close="onCloseAlert">
        <template #title>
          <div>
            完善企业信息，认证后可享受在线投标报价、企业服务等功能
            <span style="color: #409eff; cursor: pointer" @click="onClickAuth"
              >去认证 ></span
            >
          </div>
        </template>
      </el-alert>
      <el-dialog
        v-model="state.authVisible"
        title="提示"
        width="500"
        @close="onClickCloseAuth"
      >
        <div>
          尊敬的用户您好！为了您能够在本平台顺利开展交易业务，请先完成平台认证，谢谢合作！
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="onClickAuth()"> 立即认证 </el-button>
          </div>
        </template>
      </el-dialog>
      <el-row :gutter="20">
        <el-col :span="16">
          <div class="home-left">
            <div class="home-left-audit">
              <SupplierStatistics> </SupplierStatistics>
            </div>
            <div class="home-left-quick">
              <SupplierQuickAccess></SupplierQuickAccess>
            </div>
            <div class="home-left-statistics">
              <SupplierBidStatistics></SupplierBidStatistics>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="home-right">
            <div class="home-right-bid">
              <SupplierBidProject></SupplierBidProject>
            </div>
            <div class="home-right-contact">
              <ContactUs></ContactUs>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <!-- 采购人经理 -->
    <div v-else-if="userStore.authorityList.some((v: string) => v === 'portal:cgrjl')">
      <el-row :gutter="20" class="mb20">
        <el-col :span="8">
          <QuickAccessCgr></QuickAccessCgr>
        </el-col>
        <el-col :span="8">
          <TODOList></TODOList>
        </el-col>
        <el-col :span="8">
          <Notice></Notice>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <PurchaseLeaderCenter></PurchaseLeaderCenter>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <PurchasePlan></PurchasePlan>
        </el-col>
      </el-row>
    </div>
    <!-- 采购人实施 -->
    <div v-else-if="userStore.authorityList.some((v: string) => v === 'portal:cgrss')">
      <el-row :gutter="20" class="mb20">
        <el-col :span="8">
          <QuickAccessCgr></QuickAccessCgr>
        </el-col>
        <el-col :span="8">
          <TODOList></TODOList>
        </el-col>
        <el-col :span="8">
          <Notice></Notice>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <PurchaseLeaderCenter></PurchaseLeaderCenter>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <PurchasePlan></PurchasePlan>
        </el-col>
      </el-row>
    </div>
  </div>
</template>
<script setup lang="ts">
import { useUserStore } from "@/store/modules/user";
import { reactive } from "vue";
import { useRouter } from "vue-router";
import BidProject from "./components/BidProject.vue";
import ContactUs from "./components/ContactUs.vue";
import MyAudit from "./components/MyAudit.vue";
import PlanStatistics from "./components/PlanStatistics.vue";
import PurchaseLeaderCenter from "./components/PurchaseLeaderCenter.vue";
import PurchasePlan from "./components/PurchasePlan.vue";
import QuickAccessCgr from "./components/QuickAccess-cgr.vue";
import QuickAccess from "./components/QuickAccess.vue";
import SupplierBidProject from "./components/SupplierBidProject.vue";
import SupplierBidStatistics from "./components/SupplierBidStatistics.vue";
import SupplierQuickAccess from "./components/SupplierQuickAccess.vue";
import SupplierStatistics from "./components/SupplierStatistics.vue";
import WinBidStatistics from "./components/WinBidStatistics.vue";
import Notice from './components/notice.vue';
import TODOList from './components/todolist.vue';

const router = useRouter();
const userStore = useUserStore();

interface IState {
  authVisible: boolean;
}

const state = reactive<IState>({
  authVisible: !userStore.isAuthDialog,
});

const onClickAuth = () => {
  router.push("/member/info/index");
  onClickCloseAuth();
};

const onClickCloseAuth = () => {
  state.authVisible = false;
  userStore.setIsAuthDialog(true);
};

const onCloseAlert = () => {
  userStore.setIsAuth(true);
};
</script>
<style lang="scss" scoped>
.home {
  width: 100%;
  overflow-x: hidden;
  &-left {
    &-quick {
      margin-top: 16px;
    }
    &-statistics {
      margin-top: 16px;
    }
  }
  &-right {
    &-contact {
      margin-top: 16px;
    }
  }
}
.mb20{
  margin-bottom: 20px;
}
</style>
