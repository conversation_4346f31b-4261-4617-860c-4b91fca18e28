<template>
  <el-dialog v-model="visible" title="写跟进" :close-on-click-modal="false" width="800px">
    <el-form ref="dataFormRef" :model="dataForm" :rules="dataRules" label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="followType" label="跟进类型">
            <fast-select
              style="width: 100%"
              v-model="dataForm.followType"
              dict-type="follow_type"
              placeholder="跟进类型"
            ></fast-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="followStatus" label="跟进状态">
            <fast-select
              style="width: 100%"
              v-model="dataForm.followStatus"
              dict-type="follow_status"
              placeholder="跟进状态"
            ></fast-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="followContent" label="跟进内容">
            <el-input
              v-model="dataForm.followContent"
              type="textarea"
              :autosize="{ minRows: 4 }"
              placeholder="跟进内容"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" class="uploadimg">
          <el-form-item prop="projectBackgroundUrl" label="上传图片">
            <!-- 多文件上传 -->
            <div class="clearfix">
                <div
                  v-if="projectBackgroundUrl && projectBackgroundUrl.length>0"
                  v-for="item in projectBackgroundUrl"
                  :key="item.id"
                >
                  <div class="multipleImg">
                    <el-image
                      class="minioimg"
                      :src="item.url"
                      :preview-src-list="[item.url]"
                    ></el-image>
                    <div class="imgdeldiv" v-if="!isDisplay">
                      <el-button type="text" icon="el-icon-delete" @click="delImg(item.url)"
                        >删除</el-button
                      >
                    </div>
                  </div>
              </div>
              <el-upload
                multiple
                accept=".png,.jpg"
                v-if="projectBackgroundUrl?.length < 10"
                class="avatar-uploader"
                :headers="{ Authorization: cache.getToken() }"
                :action="constant.uploadUrl"
                :show-file-list="false"
                :on-progress="onProgress"
                :on-success="handleAvatarSuccess"
                :before-upload="beforeAvatarUpload"
                :on-error="onerror"
              >
                <el-icon class="avatar-uploader-icon">+</el-icon>
              </el-upload>
            </div>
            <div class="upload-tip">支持格式：jpg、png、jpge、gif</div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="followTime" label="跟进时间">
            <el-date-picker
              style="width: 100%"
              v-model="dataForm.followTime"
              type="datetime"
              format="YYYY-MM-DD HH:mm:ss"
              time-format="HH:mm"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="跟进时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="nextFollowTime" label="下次跟进时间">
            <el-date-picker
              style="width: 100%"
              v-model="dataForm.nextFollowTime"
              type="datetime"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              time-format="HH:mm"
              placeholder="下次跟进时间"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()" :loading="submitLoad"
        >确定</el-button
      >
    </template>
  </el-dialog>
</template>

<script setup>
import dayjs from "dayjs";
import { reactive, ref } from "vue";
import { ElMessage } from "element-plus/es";
import { customerFollowApi } from "@/api/customerManage/common";
import { ElLoading } from "element-plus";
import constant from "@/utils/constant";
import cache from "@/utils/cache";
const emit = defineEmits(["refreshDataList"]);
const visible = ref(false);
const dataFormRef = ref();
const submitLoad = ref(false);
const dataForm = reactive({
  customerId: "",
  followType: "",
  followStatus: "",
  followContent: "",
  attachmentList: [],
  followTime: "",
  nextFollowTime: "",
});
const dataRules = ref({
  followType: [{ required: true, message: "必填项不能为空", trigger: "change" }],
  followStatus: [{ required: true, message: "必填项不能为空", trigger: "change" }],
  followContent: [{ required: true, message: "必填项不能为空", trigger: "blur" }],
  followTime: [{ required: true, message: "必填项不能为空", trigger: "change" }],
});

const projectBackgroundUrl = ref([]);
const projectBackgroundLength = ref(0);
const loadNum = ref(0);
const fileLoading = ref();

const onProgress = () => {
  loadNum.value++;
  if (loadNum.value === 1) {
    fileLoading.value = ElLoading.service({
      lock: true,
      text: "上传中，请稍等...",
      background: "rgba(0, 0, 0, 0.7)",
    });
  }
};

const onerror = () => {
  loadNum.value = 0;
  fileLoading.value.close();
  this.$message.error("上传失败");
};
const handleAvatarSuccess = (res) => {
  if (res.code !== 0) {
    loadNum.value = 0;
    fileLoading.value.close();
    ElMessage.error("上传失败");
    return false;
  }
  loadNum.value = 0;
  fileLoading.value.close();
  projectBackgroundUrl.value.push(res.data);
  dataForm.attachmentList = projectBackgroundUrl.value;
  projectBackgroundLength.value = projectBackgroundUrl.value.length;
};
const delImg = (url) => {
  // type用于页面多附件
  projectBackgroundUrl.value = projectBackgroundUrl.value.filter((item) => {
    return item.url !== url;
  });
  dataForm.attachmentList = projectBackgroundUrl.value;
  projectBackgroundLength.value = projectBackgroundUrl.value.length;
};
const beforeAvatarUpload = (file) => {
  projectBackgroundLength.value++;
  if (projectBackgroundLength.value > 10) {
    this.$message.error("不得超过10张图！");
    return false;
  }
  //图片验证
  const isJPG = file.type === "image/jpeg";
  const isPNG = file.type === "image/png";
  if (!(isJPG || isPNG)) {
    this.$message.error("上传图片只能是 JPG或者PNG 格式");
  }
  return isJPG || isPNG;
};
const init = (id) => {
  visible.value = true;
  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }
  projectBackgroundUrl.value = [];
  dataForm.attachmentList = [];
  dataForm.customerId = id;
  dataForm.followTime = dayjs().format("YYYY-MM-DD HH:mm:ss");
  // dataForm.nextFollowTime = getTimeFull();
};

// 表单提交
const submitHandle = () => {
  dataFormRef.value.validate((valid) => {
    if (!valid) {
      return false;
    }
    submitLoad.value = true;
    setTimeout(() => {
      dataForm.followTime = dataForm.followTime
        ? dayjs(dataForm.followTime + ":00").format("YYYY-MM-DD HH:mm:ss")
        : "";
      dataForm.nextFollowTime = dataForm.nextFollowTime
        ? dayjs(dataForm.nextFollowTime + ":00").format("YYYY-MM-DD HH:mm:ss")
        : "";
      customerFollowApi(dataForm)
        .then((res) => {
          setTimeout(() => {
            submitLoad.value = false;
          }, 3000);
          if(res.code===0){
            ElMessage.success({
              message: "操作成功",
              duration: 500,
              onClose: () => {
                visible.value = false;
                emit("refreshDataList");
              },
            });
          }
        })
        .catch(() => {
          submitLoad.value = false;
        });
    }, 500);
  });
};

defineExpose({
  init,
});
</script>
<style>
.avatar-uploader {
  /* float: left; */
  margin-right: 10px;
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  text-align: center;
  border: 1px dashed #ccc;
}
.avatar-uploader .avatar-uploader-icon,
.avatar-uploader .avatar {
  display: block;
  width: 120px;
  height: 120px;
  line-height: 120px;
}
.imgdeldiv {
  width: 100px;
  text-align: center;
  color: #409eff;
  margin-top: -10px;
}
.el-upload__tip {
  color: #e6a23c;
}
.minioimg {
  width: 120px;
  height: 120px;
}
.multipleImg {
  float: left;
  margin-right: 10px;
  margin-bottom: 10px;
}
.uploadimg .el-form-item__content {
  align-items: flex-start;
}
.upload-tip{
  color:#9E9E9E;
}
.clearfix{
  width:100%;
  display: flex;
}
.clearfix:after{
  content: "";
  display: table;
  clear: both;
}
</style>
