/*
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-03-19 14:34:29
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-06-03 10:54:04
 */
import { ITheme } from '@/store/theme/interface'

export const themeConfig: ITheme = {
	sidebarStyle: 'dark',
	headerStyle: 'light',
	primaryColor: '#409eff',
	layout: 'transverse',
	uniqueOpened: true,
	isLogo: true,
	isBreadcrumb: true,
	isTabsView: true,
	isTabsCache: true,
	tabsStyle: 'style-1'
}

