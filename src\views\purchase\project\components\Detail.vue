<template>
  <el-drawer
    v-model="state.show"
    :title="props.title"
    :size="950"
    class="project_detail_drawer"
    @close="onClickClose"
  >
    <el-card>
      <div class="detail">
        <el-form ref="dataFormRef" label-width="130px" class="detail-form">
          <el-form-item prop="projectName" label="项目名称">
            <div class="detail-form-value">
              {{ state.baseInfo.projectName }}
            </div>
          </el-form-item>
          <el-form-item prop="projectNo" label="项目编号">
            <div class="detail-form-value">
              {{ state.baseInfo.projectNo }}
            </div>
          </el-form-item>
          <el-form-item prop="projectNo" label="预算总金额（元）">
            <div class="detail-form-value">
              {{ state.baseInfo.budget }}
            </div>
          </el-form-item>
          <el-form-item prop="projectYear" label="年度">
            <div class="detail-form-value">
              {{ state.baseInfo.projectYear }}
            </div>
          </el-form-item>
          <el-form-item prop="orgName" label="项目所属机构">
            <div class="detail-form-value">
              {{ state.baseInfo.orgName?state.baseInfo.orgName:'-' }}
            </div>
          </el-form-item>
          <el-form-item prop="projectYear" label="项目内容">
            <div class="detail-form-value">
              <WangEditor
                v-model="state.baseInfo.projectContent"
                :disabled="true"
              ></WangEditor>
            </div>
          </el-form-item>
          <el-form-item prop="materialList" label="清单列表" label-position="top">
            <div class="detail-form-export">
              <el-button type="primary" @click="onClickExport()">导出</el-button>
            </div>
            <div class="detail-form-table" style="width: 100%; margin-top: 16px">
              <el-table :data="state.materialList" border max-height="500">
                <el-table-column
                  align="center"
                  type="index"
                  width="60"
                  label="序号"
                ></el-table-column>
                <el-table-column
                  align="center"
                  prop="materialName"
                  label="产品名称"
                ></el-table-column>
                <el-table-column
                  align="center"
                  prop="materialType"
                  label="产品型号（规格参数）"
                ></el-table-column>
                <el-table-column
                  align="center"
                  prop="materialUnit"
                  label="计量单位"
                ></el-table-column>
                <el-table-column
                  align="center"
                  prop="materialQuantity"
                  label="采购数量"
                ></el-table-column>
                <el-table-column
                  align="center"
                  prop="comment"
                  label="备注"
                ></el-table-column>
              </el-table>
            </div>
          </el-form-item>
          <el-form-item prop="attachmentList" label="附件">
            <div class="detail-form-list">
              <div
                class="detail-form-list-item"
                v-for="item in state.baseInfo.attachmentList"
              >
                <div class="detail-form-list-item-text">
                  {{ item.name }}
                </div>
                <div class="detail-form-list-item-action">
                  <el-icon class="action-icon" @click="onClickDownload(item)">
                    <Download />
                  </el-icon>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </el-drawer>
</template>
<script setup lang="ts">
import { getProjectInfo, exportProjectMaterialList } from "@/api/purchase/project";
import WangEditor from "@/components/wang-editor/index.vue";
import service from "@/utils/request";
import { Download } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { reactive, watch } from "vue";
interface IProps {
  show: boolean;
  title: string;
  id?: number;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  title: "",
  id: 4,
});

interface IAttachmentItem {
  name: string;
  url: string;
  platform: string;
  size: number;
}

interface IBaseInfo {
  projectName: string;
  projectNo: string;
  budget?: number;
  projectYear: string;
  orgName: string;
  projectContent: string;
  attachmentList: IAttachmentItem[];
}
interface IMaterialItem {
  id: number;
  materialNo: string;
  materialName: string;
  materialSpec: string;
  materialType: string;
  materialUnit: string;
  materialQuantity?: number;
  comment: string;
}

interface IState {
  show: boolean;
  baseInfo: IBaseInfo;
  materialList: IMaterialItem[];
}

const state = reactive<IState>({
  show: false,
  baseInfo: {
    projectName: "",
    projectNo: "",
    budget: void 0,
    projectYear: "2024",
    orgName:"",
    projectContent: "",
    attachmentList: [],
  },
  materialList: [],
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      state.show = true;
      if (props.id) {
        GetProjectInfo();
      }
    }
  }
);

const GetProjectInfo = () => {
  getProjectInfo(props.id).then((res: any) => {
    if (res.code === 0) {
      let newBaseInfo = {
        projectName: res.data.projectName,
        projectNo: res.data.projectNo,
        budget: res.data.budget,
        projectYear: res.data.projectYear,
        orgName:res.data.orgName,
        projectContent: res.data.projectContent,
        attachmentList: (res.data?.attachmentList || []).map((item: any) => {
          return {
            name: item.name,
            url: item.url,
            platform: item.platform,
            size: item.size,
          };
        }),
      };
      state.baseInfo = newBaseInfo;
      let newMaterialList = res.data.tbProjectMaterialVOS.map((item: any) => {
        return {
          id: item?.materialId ?? void 0,
          materialNo: item?.materialNo ?? "",
          materialName: item?.materialName ?? "",
          materialSpec: item?.materialSpec ?? "",
          materialType: item?.materialType ?? "",
          materialUnit: item?.materialUnit ?? "",
          materialQuantity: item.materialQuantity,
          comment: item?.comment ?? "",
        };
      });
      state.materialList = newMaterialList;
    }
  });
};

const emit = defineEmits<{
  (e: "close"): void;
}>();

const onClickClose = () => {
  state.show = false;
  emit("close");
};

const onClickDownload = async (item: IAttachmentItem) => {
  const response = await service.get(item.url, {
    responseType: "blob", // important
  });
  const url = window.URL.createObjectURL(new Blob([response.data]));
  const link = document.createElement("a");
  link.href = url;
  link.setAttribute("download", item.name);
  document.body.appendChild(link);
  link.click();
};

const onClickExport = () => {
  exportProjectMaterialList(props.id).then((res: any) => {
    const url = window.URL.createObjectURL(new Blob([res.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", `${state.baseInfo.projectName}清单列表.xlsx`);
    document.body.appendChild(link);
    link.click();
  });
};
</script>
<style lang="scss">
.project_detail_drawer {
  .el-drawer__body {
    padding: 14px !important;
    background: #f5f6fa !important;
  }
}
</style>

<style lang="scss" scoped>
.detail {
  &-form {
    &-value {
      color: #000;
    }
    &-list {
      &-item {
        display: flex;
        align-items: center;
        &-text {
          color: #409eff;
          cursor: pointer;
        }
        &-action {
          color: #545252;
          cursor: pointer;
          .action-icon {
            margin-left: 4px;
          }
        }
      }
    }
  }
}
</style>
