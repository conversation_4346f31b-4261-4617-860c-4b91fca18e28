<template>
  <el-drawer
    v-model="state.show"
    :title="title"
    :size="950"
    class="project_detail_drawer"
    @close="onClickClose"
  >
    <el-card>
      <div class="detail">
        <el-form ref="dataFormRef" label-width="130px" class="detail-form">
          <el-form-item prop="projectName" label="项目名称">
            <div class="detail-form-value">
              {{ state.baseInfo.projectName }}
            </div>
          </el-form-item>
          <el-form-item prop="projectNo" label="项目编号">
            <div class="detail-form-value">
              {{ state.baseInfo.projectNo }}
            </div>
          </el-form-item>
          <el-form-item prop="projectNo" label="预算总金额（元）">
            <div class="detail-form-value">
              {{ state.baseInfo.budget }}
            </div>
          </el-form-item>
          <el-form-item prop="projectYear" label="年度">
            <div class="detail-form-value">
              {{ state.baseInfo.projectYear }}
            </div>
          </el-form-item>
          <el-form-item prop="orgName" label="项目所属机构">
            <div class="detail-form-value">
              {{ state.baseInfo.orgName?state.baseInfo.orgName:'-' }}
            </div>
          </el-form-item>
          <el-form-item prop="projectYear" label="项目内容">
            <div class="detail-form-value">
              <WangEditor
                v-model="state.baseInfo.projectContent"
                :disabled="true"
              ></WangEditor>
            </div>
          </el-form-item>
          <el-form-item prop="materialList" label="清单列表" label-position="top">
            <div class="detail-form-export">
              <el-button type="primary" @click="onClickExport()">导出</el-button>
            </div>
            <div class="detail-form-table" style="width: 100%; margin-top: 16px">
              <el-table :data="state.materialList" border max-height="500">
                <el-table-column
                  align="center"
                  type="index"
                  width="60"
                  label="序号"
                ></el-table-column>
                <el-table-column
                  align="center"
                  prop="materialName"
                  label="产品名称"
                ></el-table-column>
                <el-table-column
                  align="center"
                  prop="materialType"
                  label="产品型号（规格参数）"
                ></el-table-column>
                <el-table-column
                  align="center"
                  prop="materialUnit"
                  label="计量单位"
                ></el-table-column>
                <el-table-column
                  align="center"
                  prop="materialQuantity"
                  label="采购数量"
                ></el-table-column>
                <el-table-column
                  align="center"
                  prop="comment"
                  label="备注"
                ></el-table-column>
              </el-table>
            </div>
          </el-form-item>
          <el-form-item prop="attachmentList" label="附件">
            <div class="detail-form-list">
              <div
                class="detail-form-list-item"
                v-for="item in state.baseInfo.attachmentList"
              >
                <div class="detail-form-list-item-text">
                  {{ item.name }}
                </div>
                <div class="detail-form-list-item-action">
                  <el-icon class="action-icon" @click="onClickDownload(item)">
                    <Download />
                  </el-icon>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </el-card>
  </el-drawer>
</template>
<script>
import { getProjectInfo, exportProjectMaterialList } from "@/api/purchase/project";
import WangEditor from "@/components/wang-editor/index.vue";
import service from "@/utils/request";
import { Download } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";

export default {
  name: "Detail",
  components: {
    WangEditor,
    Download,
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    id: {
      type: Number,
      default: 4,
    },
  },
  data() {
    return {
      state: {
        show: false,
        baseInfo: {
          projectName: "",
          projectNo: "",
          budget: undefined,
          projectYear: "2024",
          orgName: "",
          projectContent: "",
          attachmentList: [],
        },
        materialList: [],
      },
    };
  },

  watch: {
    show: {
      handler(newVal) {
        if (newVal) {
          this.state.show = true;
          if (this.id) {
            this.GetProjectInfo();
          }
        }
      },
    },
  },
  methods: {
    GetProjectInfo() {
      getProjectInfo(this.id).then((res) => {
        if (res.code === 0) {
          let newBaseInfo = {
            projectName: res.data.projectName,
            projectNo: res.data.projectNo,
            budget: res.data.budget,
            projectYear: res.data.projectYear,
            orgName: res.data.orgName,
            projectContent: res.data.projectContent,
            attachmentList: (res.data?.attachmentList || []).map((item) => {
              return {
                name: item.name,
                url: item.url,
                platform: item.platform,
                size: item.size,
              };
            }),
          };
          this.state.baseInfo = newBaseInfo;
          let newMaterialList = res.data.tbProjectMaterialVOS.map((item) => {
            return {
              id: item?.materialId ?? undefined,
              materialNo: item?.materialNo ?? "",
              materialName: item?.materialName ?? "",
              materialSpec: item?.materialSpec ?? "",
              materialType: item?.materialType ?? "",
              materialUnit: item?.materialUnit ?? "",
              materialQuantity: item.materialQuantity,
              comment: item?.comment ?? "",
            };
          });
          this.state.materialList = newMaterialList;
        }
      });
    },

    onClickClose() {
      this.state.show = false;
      this.$emit("close");
    },

    async onClickDownload(item) {
      const response = await service.get(item.url, {
        responseType: "blob", // important
      });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", item.name);
      document.body.appendChild(link);
      link.click();
    },

    onClickExport() {
      exportProjectMaterialList(this.id).then((res) => {
        const url = window.URL.createObjectURL(new Blob([res.data]));
        const link = document.createElement("a");
        link.href = url;
        link.setAttribute("download", `${this.state.baseInfo.projectName}清单列表.xlsx`);
        document.body.appendChild(link);
        link.click();
      });
    },
  },
};
</script>
<style lang="scss">
.project_detail_drawer {
  .el-drawer__body {
    padding: 14px !important;
    background: #f5f6fa !important;
  }
}
</style>

<style lang="scss" scoped>
.detail {
  &-form {
    &-value {
      color: #000;
    }
    &-list {
      &-item {
        display: flex;
        align-items: center;
        &-text {
          color: #409eff;
          cursor: pointer;
        }
        &-action {
          color: #545252;
          cursor: pointer;
          .action-icon {
            margin-left: 4px;
          }
        }
      }
    }
  }
}
</style>
