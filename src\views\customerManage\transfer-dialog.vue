<template>
	<el-dialog v-model="visible" title="转移客户给其他人跟进" :close-on-click-modal="false" width="500px">
		<el-form ref="dataFormRef" :model="dataForm" :rules="dataRules" label-width="110px">
			<el-row>
        <el-col :span="24">
					<el-form-item prop="header" label="选择负责人">
						<el-tree-select
              ref="treeSelectRef"
              v-model="dataForm.header"
              :data="treeData"
              check-strictly
              node-key="id"
              :props="defaultProps"
              :render-after-expand="false"
            />
					</el-form-item>
        </el-col>
        <el-col :span="24">
					<el-form-item prop="reason" label="转移原因">
						<el-input
              clearable
              v-model="dataForm.reason"
              type="textarea"
              :autosize="{ minRows: 3 }"
              placeholder="请输入转移原因"
              maxlength="50"
              show-word-limit
            ></el-input>
					</el-form-item>
        </el-col>
			</el-row>
		</el-form>
    
		<template #footer>
			<el-button @click="visible = false">取消</el-button>
			<el-button type="primary" @click="submitHandle()" :loading="submitLoad">确定</el-button>
		</template>
	</el-dialog>
</template>

<script setup lang="ts">
	import { reactive, ref } from 'vue'
	import { ElMessage } from 'element-plus/es'
  import {transferHeaderApi,} from '@/api/customerManage/common'
  import {
    getlistOrgUserTree,
} from "@/api/workbench";
	const emit = defineEmits(['refreshDataList','setVisible'])
  const props = defineProps({
    idList: {
      default:[],
      type:Array
    },
    fromPage: {
      default:'',
      type:String
    }
  })
  interface Tree {
    [key: string]: any;
  }
  const treeSelectRef = ref();
  const treeData = ref<Tree[]>([]);
	const visible = ref(false)
	const dataFormRef = ref()
  const submitLoad =ref(false)
	const dataForm = reactive({
		idList:[],
    header:null,
    reason:'',
	})
  const defaultProps = {
    children: "children",
    label: "name",
    disabled: "status",
  };
	const init = () => {
		visible.value = true
		// 重置表单数据
		if (dataFormRef.value) {
			dataFormRef.value.resetFields()
		}
    getUserTreeSingle();
	}

	const dataRules = ref({
		header: [{ required: true, message: '必填项不能为空', trigger: ["change"] }]
	})

  const getUserTreeSingle = () => {
    getlistOrgUserTree().then((res) => {
      if (res.code == 0) {
        treeData.value = res.data;
      }
    });
  };

	// 表单提交
	const submitHandle = () => {
		dataFormRef.value.validate((valid) => {
			if (!valid) {
				return false
			}
      
      submitLoad.value = true
      dataForm.idList=props.idList.map(item=>item.id);
      //转移他人
      transferHeaderApi(dataForm).then(() => {
        submitLoad.value = false
        ElMessage.success({
          message: '操作成功',
          duration: 500,
          onClose: () => {
            visible.value = false
            if(props.fromPage==="detail"){
              emit('setVisible')
            }
            emit('refreshDataList')
          }
        })
      }).catch(() => {
        submitLoad.value = false
      })
		})
	}
	defineExpose({
		init
	})
</script>
