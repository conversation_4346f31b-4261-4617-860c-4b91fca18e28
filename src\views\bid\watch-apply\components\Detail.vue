<template>
  <el-drawer
    v-model="drawer.show"
    :title="props.info.packageName"
    :size="850"
    class="watch-apply-detail-drawer"
    @close="onClickClose"
  >
    <el-card>
      <div class="detail">
        <div class="detail-info">
          <div class="detail-info-title">{{ props.info.packageName }}</div>
          <div class="detail-info-desc">
            <div class="detail-info-desc-item">
              采购方式：
              <span
                v-html="getDictLabelList('package_type', props.info.packageType)"
              ></span>
            </div>
            <div class="detail-info-desc-divider"></div>
            <div class="detail-info-desc-item">
              报价开始时间：{{ props.info.bidStartDate }}
            </div>
            <div class="detail-info-desc-divider"></div>
            <div class="detail-info-desc-item">
              报价结束时间：{{ props.info.bidEndDate }}
            </div>
          </div>
        </div>
        <div class="detail-divider"></div>
        <div class="detail-list">
          <div class="detail-list-title">参与列表</div>
          <div class="detail-list-table">
            <el-table
              v-loading="state.dataListLoading"
              show-overflow-tooltip
              :data="state.dataList"
              border
            >
              <el-table-column
                type="index"
                label="序号"
                header-align="center"
                align="center"
                width="70"
              ></el-table-column>
              <el-table-column
                prop="bidderName"
                label="供应商"
                header-align="center"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="contactName"
                label="联系人"
                header-align="center"
                align="center"
              ></el-table-column>
              <el-table-column
                prop="contactPhone"
                label="联系电话"
                header-align="center"
                align="center"
              ></el-table-column>
              <fast-table-column
                prop="auditStatus"
                label="参与审核状态"
                dict-type="reg_audit_status"
              ></fast-table-column>
              <el-table-column
                label="操作"
                fixed="right"
                header-align="center"
                align="center"
                width="120"
              >
                <template #default="scope">
                  <el-button type="primary" link @click="onClickDetail(scope.row)">
                    查看
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="detail-list-page">
            <el-pagination
              :current-page="state.pageNo"
              :page-sizes="state.pageSizes"
              :page-size="state.pageSize"
              :total="state.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="sizeChangeHandle"
              @current-change="currentChangeHandle"
            >
            </el-pagination>
          </div>
        </div>
        <WatchBidderInfo
          :show="detail.show"
          :id="detail.id"
          @on-close="onCloseDetail"
        ></WatchBidderInfo>
      </div>
    </el-card>
  </el-drawer>
</template>
<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { getDictLabelList } from "@/utils/tool";
import { reactive, watch } from "vue";
import WatchBidderInfo from "./WatchBidderInfo.vue";

interface IInfo {
  id?: number;
  packageName: string;
  packageType: string;
  bidStartDate: string;
  bidEndDate: string;
}

interface IProps {
  show: boolean;
  info: IInfo;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  info: () => {
    return {
      id: void 0,
      packageName: "",
      packageType: "",
      bidStartDate: "",
      bidEndDate: "",
    };
  },
});

interface IDrawer {
  show: boolean;
}

const drawer = reactive<IDrawer>({
  show: false,
});

watch(
  () => props.show,
  () => {
    if (props.show) {
      drawer.show = true;
      if (props.info.id) {
        state.queryForm.packageId = props.info.id;
        getDataList();
      }
    }
  }
);

const state = reactive<IHooksOptions>({
  queryForm: {
    packageId: void 0,
  },
  createdIsNeed: false,
  dataListUrl: "/purchase/registration/page",
});

const { getDataList, sizeChangeHandle, currentChangeHandle } = useCrud(state);

const emit = defineEmits<{
  (e: "on-close"): void;
}>();

const onClickClose = () => {
  emit("on-close");
};

interface IDetail {
  show: boolean;
  id?: number;
}

const detail = reactive<IDetail>({
  show: false,
  id: void 0,
});

const onClickDetail = (row: any) => {
  detail.id = row.id;
  detail.show = true;
};

const onCloseDetail = () => {
  detail.show = false;
  detail.id = void 0;
};
</script>
<style lang="scss">
.watch-apply-detail-drawer {
  .el-drawer__body {
    padding: 14px !important;
    background: #f5f6fa !important;
  }
}
</style>
<style lang="scss" scoped>
.detail {
  &-info {
    &-title {
      font-size: 18px;
      color: #030303;
    }
    &-desc {
      margin-top: 6px;
      display: flex;
      align-items: center;
      &-item {
        font-size: 14px;
        color: #949494;
      }
      &-divider {
        margin: 0 8px;
        width: 1px;
        height: 12px;
        background-color: #e8e8e8;
      }
    }
  }
  &-divider {
    width: 100%;
    height: 1px;
    background-color: #e8e8e8;
    margin: 16px 0;
  }
  &-list {
    &-title {
      color: #030303;
    }
    &-table {
      margin-top: 16px;
    }
  }
}
</style>
