<template>
  <el-card>
    <div style="display: flex; justify-content: space-between">
      <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
        <el-form-item>
          <el-date-picker
            v-model="selectDate"
            type="daterange"
            unlink-panels
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="onChangeDate"
            style="width: 350px"
          />
        </el-form-item>
        <el-form-item>
          <el-cascader
            :options="userData"
            :props="userProps"
            collapse-tags
            collapse-tags-tooltip
            clearable
            @change="userChange"
            placeholder="请选择员工"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList()">查询</el-button>
        </el-form-item>
      </el-form>
      <div>
        <el-button type="primary" @click="onClickExport()">导出</el-button>
      </div>
    </div>
    <el-table
      v-loading="state.dataListLoading"
      :data="state.dataList"
      border
      style="width: 100%"
      @selection-change="selectionChangeHandle"
    >
      <el-table-column
        prop="realName"
        label="员工"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="orgName"
        label="部门"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column prop="postNames" label="职位" header-align="center" align="center">
        <template #default="scope">
          <div>{{ scope.row.postNames.join("、") }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="groupName"
        label="考勤组"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="attendanceDate"
        label="考勤日期"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="attendanceTime"
        label="考勤时间"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="clockTime"
        label="打卡时间"
        header-align="center"
        align="center"
      ></el-table-column>
      <fast-table-column
        prop="clockResult"
        label="打卡状态"
        header-align="center"
        align="center"
        dict-type="clock_result"
      ></fast-table-column>
      <el-table-column
        prop="outdoorWorkCheckIn"
        label="打卡结果"
        header-align="center"
        align="center"
      >
        <template #default="scope">
          <div>
            <div v-if="(scope.row.outdoorWorkCheckIn || 0) === 1">
              <el-tag type="danger">外勤打卡</el-tag>
            </div>
            <div v-else>
              <el-tag type="success">正常打卡</el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="clockLocation"
        label="打卡地址"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="clockImg"
        label="打卡图片"
        header-align="center"
        align="center"
      >
        <template #default="scope">
          <div>
            <div v-if="(scope.row.clockImg ?? '') === ''">-</div>
            <div v-else>
              <el-image
                style="width: 100px; height: 100px"
                :src="scope.row.clockImg"
                :preview-src-list="[scope.row.clockImg]"
                :preview-teleported="true"
              />
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="clockRemark"
        label="打卡备注"
        header-align="center"
        align="center"
      >
        <template #default="scope">
          <div>
            <div v-if="(scope.row.clockRemark ?? '') === ''">-</div>
            <div v-else>{{ scope.row.clockRemark }}</div>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="state.pageNo"
      :page-sizes="state.pageSizes"
      :page-size="state.pageSize"
      :total="state.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    >
    </el-pagination>
  </el-card>
</template>

<script setup lang="ts">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { exportClockRecord } from "@/api/check-in/record";
import dayjs from "dayjs";
import { onMounted, reactive, ref } from "vue";
import { getlistOrgUserTree } from "@/api/workbench";
const userProps = { value: "id", label: "name", children: "children", multiple: true };
const userData = ref([]);

const state: IHooksOptions = reactive({
  dataListUrl: "/work/clockRecord/page",
  deleteUrl: "/work/clockRecord",
  queryForm: {
    startTime: "",
    endTime: "",
    userId: "",
  },
});

const selectDate = ref<string[]>([]);

const {
  getDataList,
  selectionChangeHandle,
  sizeChangeHandle,
  currentChangeHandle,
  deleteBatchHandle,
} = useCrud(state);

onMounted(() => {
  getUserList();
});

const onChangeDate = (value: any) => {
  if (value && value.length === 2) {
    state.queryForm.startTime = dayjs(value[0]).format("YYYY-MM-DD");
    state.queryForm.endTime = dayjs(value[1]).format("YYYY-MM-DD");
  } else {
    state.queryForm.startTime = "";
    state.queryForm.endTime = "";
  }
};

const onClickExport = () => {
  exportClockRecord(state.queryForm).then((res) => {
    const url = window.URL.createObjectURL(new Blob([res.data]));
    const link = document.createElement("a");
    link.href = url;
    link.setAttribute("download", "打卡记录.xlsx");
    document.body.appendChild(link);
    link.click();
  });
};

const userChange = (event) => {
  if (event && event.length > 0) {
    let arr = [];
    event.map((item) => {
      arr.push(item[item.length - 1]);
    });
    state.queryForm.userId = arr.toString();
  } else {
    state.queryForm.userId = "";
  }
};

const getUserList = () => {
  getlistOrgUserTree().then((res) => {
    if (res.code == 0) {
      userData.value = res.data;
    }
  });
};
</script>
