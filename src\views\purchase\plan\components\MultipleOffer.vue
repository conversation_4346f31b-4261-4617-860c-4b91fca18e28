<template>
  <el-dialog
    v-model="start.show"
    :title="`发起${props.roundNo + 1}轮报价`"
    width="800px"
    @close="onCancelStart"
  >
    <div class="start-content">
      <el-form
        ref="dataFormRef"
        :model="start.dataForm"
        :rules="start.dataRules"
        label-width="140px"
      >
        <el-form-item prop="selectTime" label="报价时间">
          <el-date-picker
            v-model="start.dataForm.selectTime"
            type="datetimerange"
            start-placeholder="报价开始时间"
            end-placeholder="报价结束时间"
            @change="onChangeTime"
            :disabled-seconds="onDisabledSeconds"
            @visible-change="onVisibleChangeSelectTime"
          />
        </el-form-item>
        <el-form-item prop="showLastQuotation" label="是否允许查看报价">
          <div>
            <fast-radio-group
              v-model="start.dataForm.showLastQuotation"
              dict-type="yesNo"
            ></fast-radio-group>
            <div style="color: #999; font-size: 12px">查看所有供应商上一轮历史报价</div>
          </div>
        </el-form-item>
        <el-form-item prop="quotationDescription" label="报价说明">
          <el-input
            v-model="start.dataForm.quotationDescription"
            type="textarea"
            :rows="4"
          ></el-input>
        </el-form-item>
        <el-form-item prop="bidderIdList" label="选择供应商">
          <div class="supplier-list">
            <div class="supplier-list-action">
              <el-button type="primary" @click="onSelectSupplier">选择供应商</el-button>
            </div>
            <div class="supplier-list-table">
              <el-table :data="start.dataForm.bidderIdList" border :max-height="450">
                <el-table-column
                  type="index"
                  label="序号"
                  header-align="center"
                  align="center"
                  width="70"
                ></el-table-column>

                <el-table-column
                  prop="bidderName"
                  label="供应商名称"
                  dict-type="user_gender"
                ></el-table-column>
                <el-table-column
                  prop="contractor"
                  label="联系人"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="quotationPrice"
                  label="报价（元）"
                  header-align="center"
                  align="center"
                ></el-table-column>
                <el-table-column
                  prop="action"
                  label="操作"
                  header-align="center"
                  align="center"
                >
                  <template #default="{ row }">
                    <div>
                      <el-button type="primary" link @click="onRemoveSupplier(row)"
                        >删除</el-button
                      >
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div style="display: flex; justify-content: flex-end; margin-top: 20px">
      <el-button type="primary" @click="onSubmitStart">确定</el-button>
      <el-button @click="onCancelStart">取消</el-button>
    </div>
  </el-dialog>
  <el-dialog v-model="supplierShow" title="选择供应商" width="800px">
    <div class="supplier-content">
      <div class="supplier-content-search">
        <el-input
          v-model="supplier.queryForm.bidderName"
          clearable
          placeholder="请输入供应商名称"
          style="width: 250px; margin-right: 10px"
        />
        <el-button type="primary" @click="getDataList">搜索</el-button>
      </div>
      <div class="supplier-content-table">
        <el-table
          :data="supplier.dataList"
          border
          :max-height="400"
          @selection-change="selectionChangeHandle"
          ref="tableRef"
          @select="selectHandle"
          @select-all="selectAllHandle"
        >
          <el-table-column type="selection" width="35"></el-table-column>
          <el-table-column
            type="index"
            label="序号"
            header-align="center"
            align="center"
            width="70"
          ></el-table-column>
          <el-table-column
            prop="bidderName"
            label="供应商名称"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="contractor"
            label="联系人"
            header-align="center"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="quotationPrice"
            label="报价（元）"
            header-align="center"
            align="center"
          ></el-table-column>
        </el-table>
      </div>
    </div>
    <template #footer>
      <div style="display: flex; justify-content: flex-end">
        <el-button type="primary" @click="onSubmitSupplier">确定</el-button>
        <el-button @click="onCancelSupplier">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { IStartQuotation, startQuotation } from "@/api/purchase/plan";
import { IHooksOptions } from "@/hooks/interface";
import dayjs from "dayjs";
import { ElMessage, FormRules } from "element-plus";
import { reactive, ref, watch } from "vue";
import { useCrud } from "@/hooks";

interface IProps {
  show: boolean;
  packageId?: number;
  roundNo?: number;
}

const props = withDefaults(defineProps<IProps>(), {
  show: false,
  roundNo: 1,
});

const dataFormRef = ref();

interface IStart {
  show: boolean;
  selectTimeShow: boolean;
  startTime: string;
  endTime: string;
  dataForm: {
    selectTime: string[];
    showLastQuotation: string;
    quotationDescription: string;
    bidderIdList: any[];
  };
  dataRules: FormRules;
}

const start = reactive<IStart>({
  show: false,
  selectTimeShow: false,
  dataForm: {
    selectTime: [],
    showLastQuotation: "1",
    quotationDescription: "",
    bidderIdList: [],
  },
  dataRules: {
    selectTime: [{ required: true, message: "请选择报价时间", trigger: "change" }],
    showLastQuotation: [
      { required: true, message: "请选择是否允许查看报价", trigger: "change" },
    ],
    quotationDescription: [
      { required: true, message: "请输入报价说明", trigger: "blur" },
    ],
    bidderIdList: [{ required: true, message: "请选择供应商", trigger: "change" }],
  },
  startTime: "",
  endTime: "",
});

watch(
  () => props.show,
  (val) => {
    if (val) {
      start.show = val;
    }
  }
);

const onDisabledSeconds = () => {
  let disabledSeconds = [];
  for (let i = 1; i < 60; i++) {
    disabledSeconds.push(i);
  }
  return disabledSeconds;
};

const onChangeTime = (value: any) => {
  if (value && value.length === 2) {
    start.startTime = dayjs(value[0]).format("YYYY-MM-DD HH:mm:ss");
    start.endTime = dayjs(value[1]).format("YYYY-MM-DD HH:mm:ss");
  }
};

const emit = defineEmits(["on-close", "start-quotation"]);

const onSubmitStart = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (valid) {
      let newBidderIdList = (supplier?.dataList ?? [])
        .filter((item: any) => {
          return (
            start.dataForm.bidderIdList.findIndex((i: any) => i.id === item.id) === -1
          );
        })
        .map((item: any) => {
          return item.bidderId;
        });
      let reqData: IStartQuotation = {
        packageId: props.packageId,
        roundNum: props.roundNo + 1,
        startTime: start.startTime,
        endTime: start.endTime,
        showLastQuotation: start.dataForm.showLastQuotation,
        quotationDescription: start.dataForm.quotationDescription,
        bidderIdList: newBidderIdList,
      };
      startQuotation(reqData).then((res: any) => {
        if (res.code === 0) {
          start.show = false;
          start.dataForm.selectTime = [];
          start.startTime = "";
          start.endTime = "";
          dataFormRef.value.resetFields();
          emit("start-quotation");
          ElMessage.success("操作成功");
        }
      });
    }
  });
};

const onCancelStart = () => {
  start.show = false;
  start.dataForm.selectTime = [];
  start.startTime = "";
  start.endTime = "";
  dataFormRef.value.resetFields();
  emit("on-close");
};

const onVisibleChangeSelectTime = (val: boolean) => {
  start.selectTimeShow = val;
};

const supplierShow = ref<boolean>(false);

const supplier = reactive<IHooksOptions>({
  createdIsNeed: false,
  isPage: false,
  queryForm: {
    packageId: void 0,
    roundNum: 1,
    bidderName: "",
  },
  dataListUrl: "/purchase/quotation/findInfoListByPackageIdAndRoundNum",
});

const tableRef = ref();

const { getDataList, selectionChangeHandle, selectHandle, selectAllHandle } = useCrud(
  supplier,
  tableRef
);

const onSelectSupplier = () => {
  supplierShow.value = true;
  supplier.queryForm.bidderName = "";
  supplier.queryForm.packageId = props.packageId;
  supplier.queryForm.roundNum = props.roundNo;
  supplier.selectedData = start.dataForm.bidderIdList;
  getDataList();
};

const onSubmitSupplier = () => {
  start.dataForm.bidderIdList = supplier.selectedData ?? [];
  supplierShow.value = false;
};

const onRemoveSupplier = (item: any) => {
  start.dataForm.bidderIdList = start.dataForm.bidderIdList.filter(
    (i: any) => i.id !== item.id
  );
};

const onCancelSupplier = () => {
  supplierShow.value = false;
};
</script>
<style lang="scss" scoped>
.supplier-list {
  width: 100%;
  &-table {
    margin-top: 10px;
  }
}

.supplier-content {
  &-search {
    display: flex;
    align-items: center;
  }
  &-table {
    margin-top: 16px;
  }
}
</style>
