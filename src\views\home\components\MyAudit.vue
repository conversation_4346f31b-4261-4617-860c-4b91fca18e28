<template>
  <div class="audit">
    <div class="audit-title">
      <HomeTitle icon="icon-daishenpi" title="E采智链审批"> </HomeTitle>
    </div>
    <div class="audit-content">
      <div class="audit-content-item" @click="onSkip('/purchase/plan/index')">
        <div class="audit-content-item-icon">
          <el-badge is-dot class="item" :hidden="state.packAuditNum<=0"><svg-icon icon="icon-daishenhejihua" class-name="svg-size" /></el-badge>
        </div>
        <div class="audit-content-item-content">
          <div
            class="audit-content-item-content-number"
          >
            {{ state.packAuditNum }}
          </div>
          <div class="audit-content-item-content-text">采购计划审批</div>
        </div>
      </div>
      <div class="audit-content-item"  @click="onSkip('/purchase/notice/checkindex')">
        <div class="audit-content-item-icon">
          <el-badge is-dot class="item" :hidden="state.bulletinAuditNum<=0"><svg-icon icon="icon-caigougonggaoshenpi" class-name="svg-size" /></el-badge>
        </div>
        <div class="audit-content-item-content">
          <div
            class="audit-content-item-content-number"
          >
            {{ state.bulletinAuditNum }}
          </div>
          <div class="audit-content-item-content-text">采购公告审批</div>
        </div>
      </div>
      <div class="audit-content-item" @click="onSkip('/purchase/invation/checkindex')">
        <div class="audit-content-item-icon">
          <el-badge is-dot class="item" :hidden="state.invitationAuditNum<=0"><svg-icon icon="icon-yaoqinghanshenpi" class-name="svg-size" /></el-badge>
        </div>
        <div class="audit-content-item-content">
          <div
            class="audit-content-item-content-number"
          >
            {{ state.invitationAuditNum }}
          </div>
          <div class="audit-content-item-content-text">邀请函审批</div>
        </div>
      </div>
      <div class="audit-content-item" @click="onSkip('/supplier/manage/index')">
        <div class="audit-content-item-icon">
          <el-badge is-dot class="item" :hidden="state.supplierAuditNum<=0"><svg-icon icon="icon-gongyingshangshenpi" class-name="svg-size" /></el-badge>
        </div>
        <div class="audit-content-item-content">
          <div
            class="audit-content-item-content-number"
          >
            {{ state.supplierAuditNum }}
          </div>
          <div class="audit-content-item-content-text">供应商入库审批</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { getAuditStatistics } from "@/api/home";
import { onMounted, reactive } from "vue";
import { useRouter } from "vue-router";
import HomeTitle from "./HomeTitle.vue";

const router = useRouter();

interface IState {
  bulletinAuditNum: number;
  invitationAuditNum: number;
  packAuditNum: number;
  supplierAuditNum: number;
}

const state = reactive<IState>({
  bulletinAuditNum: 0,
  invitationAuditNum: 0,
  packAuditNum: 0,
  supplierAuditNum: 0,
});
onMounted(() => {
  getAuditStatistics().then((res: any) => {
    console.log(res);
    if (res.code === 0) {
      state.bulletinAuditNum = res.data.bulletinAuditNum;
      state.invitationAuditNum = res.data.invitationAuditNum;
      state.packAuditNum = res.data.packAuditNum;
      state.supplierAuditNum = res.data.supplierAuditNum;
    }
  });
});

const onSkip = (url: string) => {
  router.push(url);
};
</script>
<style lang="scss" scoped>
.audit {
  background-color: #ffffff;
  padding: 16px 10px;
  border-radius: 4px;
  min-height: 283px;
  &-content {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    margin:15px 0;
    &-item {
      display: flex;
      width: calc(50% - 16px);
      align-items: center;
      justify-content: center;
      margin: 30px 0;
      &-icon {
        :deep(.svg-size) {
          font-size: 40px;
        }
      }
      &-content {
        margin-left: 16px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        &-number {
          font-size: 24px;
          font-weight: bold;
          color: #1a90fe;
          cursor: pointer;
        }
        &-text {
          font-size: 14px;
          color: #999999;
        }
      }
    }
    &-divider {
      width: 1px;
      height: 40px;
      background-color: #e8e8e8;
    }
  }
}
</style>
