<template>
  <el-dialog
    v-model="visible"
    :title="!dataForm.id ? '新增' : '修改'"
    :close-on-click-modal="false"
    draggable
  >
    <div class="action-certificate">
      <el-form :model="dataForm" :rules="dataRule" ref="dataFormRef" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item prop="certificateName" label="证书名称">
              <el-input
                v-model="dataForm.certificateName"
                placeholder="证书名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="certificateType" label="证书类型">
              <fast-select
                v-model="dataForm.certificateType"
                dict-type="certificate_type"
                placeholder="证书类型"
              ></fast-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="issuingUnit" label="发证机构">
              <el-input v-model="dataForm.issuingUnit" placeholder="发证机构"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="certificateCode" label="证书编号">
              <el-input
                v-model="dataForm.certificateCode"
                placeholder="证书编号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="issuingDate" label="发证日期">
              <el-date-picker
                v-model="dataForm.issuingDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="发证日期"
                style="width: 100%"
                :disabled-date="disabledIssuingDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="expirationDate" label="有效日期">
              <el-date-picker
                v-model="dataForm.expirationDate"
                type="date"
                value-format="YYYY-MM-DD"
                placeholder="有效日期"
                style="width: 100%"
                :disabled-date="disabledExpirationDate"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="remark" label="备注">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button type="primary" @click="submitHandle()">确定</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from "vue";
import { getUserCertificateInfo, useCertificateSubmitApi } from "@/api/employee/index";
import { ElMessage } from "element-plus";

const visible = ref(false);

const dataFormRef = ref();

const dataForm = reactive({
  id: "",
  certificateName: "",
  certificateType: "",
  issuingUnit: "",
  certificateCode: "",
  issuingDate: "",
  expirationDate: "",
  remark: "",
});

const dataRule = ref({
  certificateName: [{ required: true, message: "证书名称不能为空", trigger: "blur" }],
});

const init = (id) => {
  visible.value = true;
  dataForm.id = "";

  // 重置表单数据
  if (dataFormRef.value) {
    dataFormRef.value.resetFields();
  }

  // id 存在则为修改
  if (id) {
    GetUserCertificateInfo(id);
  }
};

const emit = defineEmits(["refreshDataList"]);

const submitHandle = () => {
  dataFormRef.value.validate((valid: boolean) => {
    if (!valid) {
      return false;
    }

    useCertificateSubmitApi(dataForm).then(() => {
      ElMessage.success({
        message: "操作成功",
        duration: 500,
        onClose: () => {
          visible.value = false;
          emit("refreshDataList");
        },
      });
    });
  });
};

const GetUserCertificateInfo = (id) => {
  getUserCertificateInfo(id).then((res) => {
    Object.assign(dataForm, res.data);
  });
};

const disabledExpirationDate = (date) => {
  if (date && dataForm.issuingDate) {
    return date.getTime() < new Date(dataForm.issuingDate).getTime();
  } else {
    return false;
  }
};

const disabledIssuingDate = (date) => {
  if (date && dataForm.expirationDate) {
    return date.getTime() > new Date(dataForm.expirationDate).getTime();
  } else {
    return false;
  }
};

defineExpose({
  init,
});
</script>
<style lang="scss" scoped></style>
