<template>
  <el-dialog
    v-model="innerState.visible"
    title="选择采购计划"
    width="60%"
    :close-on-click-modal="false"
  >
    <div class="notice-search">
      <el-form :inline="true" :model="innerState.queryForm" @keyup.enter="getDataList()">
        <el-form-item>
          <el-input v-model="innerState.queryForm.packageName" placeholder="采购计划名称" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()" type="primary">查询</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="list_content">
      <el-table
        v-loading="innerState.dataListLoading"
        :data="innerState.dataList"
        height='400px'
        border
      >
        <el-table-column
          type="index"
          label="序号"
          header-align="center"
          align="center"
          width="70"
        ></el-table-column>
        <el-table-column
          prop="packageNo"
          label="采购计划编号"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="packageName"
          label="采购计划名称"
          header-align="center"
          align="center"
        ></el-table-column>
        <fast-table-column
          prop="packageType"
          label="采购方式"
          header-align="center"
          align="center"
          dict-type="package_type"
        ></fast-table-column>
        <el-table-column
          prop="managerName"
          label="项目经理"
          header-align="center"
          align="center"
        ></el-table-column>
        <el-table-column
          label="操作"
          fixed="right"
          header-align="center"
          align="center"
          width="120"
        >
          <template #default="scope">
            <el-button
              type="primary"
              link
              @click="selectPlan(scope.row)"
            >
              选择
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="innerState.visible = false">取消</el-button>
        <el-button type="primary">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive } from "vue"
import { usebulletinListApi } from '@/api/pnotice'

const innerState = reactive({
  visible: false,
  dataListLoading: false,
  dataList:[],
  queryForm: {
    packageName:''
	}
})
const emit = defineEmits(['selectResult'])

const init = ()=>{
  innerState.visible = true
  getlist()
}
// 列表
const getlist = ()=>{
  innerState.dataListLoading = true
  usebulletinListApi().then((res) => {
    if(res.code == 0){
      innerState.dataList = res.data
      innerState.dataListLoading = false
    }
  })
}
// 选择结果
const selectPlan = (row)=>{
  let obj = {
    "packageId": row.id,
    "packageNo": row.packageNo,
    "packageName": row.packageName,
    "deliveryAddress":row.deliveryAddress
  }
  emit('selectResult',obj)
  innerState.visible = false
}

defineExpose({
	init
})
</script>
<style lang="scss" scoped></style>
