<template>
  <el-card>
    <div style="display: flex; justify-content: space-between">
      <el-form :inline="true" :model="state.queryForm" @keyup.enter="getDataList()">
        <el-form-item>
          <el-input
            v-model="state.queryForm.ruleName"
            placeholder="请输入节假日名称"
            clearable
            style="width: 250px"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList()">查询</el-button>
        </el-form-item>
      </el-form>
      <div>
        <el-button type="primary" @click="addOrUpdateHandle()">新增假期规则</el-button>
      </div>
    </div>
    <el-table
      v-loading="state.dataListLoading"
      :data="state.dataList"
      border
      style="width: 100%"
      @selection-change="selectionChangeHandle"
    >
      <el-table-column
        prop="ruleName"
        label="规则名称"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="applicableScope"
        label="适用范围"
        header-align="center"
        align="center"
      >
        <template #default="scope">
          <span>{{
            scope.row.applicableScope == 0
              ? "所有人"
              : (scope.row.userList || []).length === 0
              ? "指定人"
              : (scope.row.userList || []).length + "人"
          }}</span>
        </template>
      </el-table-column>
      <fast-table-column
        prop="leaveRequestingUnit"
        label="请假单位"
        header-align="center"
        align="center"
        dict-type="leave_requesting_unit"
      >
      </fast-table-column>
      <fast-table-column
        prop="status"
        label="状态"
        header-align="center"
        align="center"
        dict-type="enable_disable"
      ></fast-table-column>
      <el-table-column
        prop="updateTime"
        label="更新时间"
        header-align="center"
        align="center"
      ></el-table-column>
      <el-table-column
        label="操作"
        fixed="right"
        header-align="center"
        align="center"
        width="150"
      >
        <template #default="scope">
          <el-button type="primary" link @click="addOrUpdateHandle(scope.row.id)"
            >修改</el-button
          >
          <el-button type="primary" link @click="deleteBatchHandle(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      :current-page="state.pageNo"
      :page-sizes="state.pageSizes"
      :page-size="state.pageSize"
      :total="state.total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="sizeChangeHandle"
      @current-change="currentChangeHandle"
    >
    </el-pagination>

    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update ref="addOrUpdateRef" @refreshDataList="getDataList"></add-or-update>
  </el-card>
</template>

<script setup lang="ts" name="WorkHolidayRuleIndex">
import { useCrud } from "@/hooks";
import { IHooksOptions } from "@/hooks/interface";
import { reactive, ref } from "vue";
import AddOrUpdate from "./add-or-update.vue";

const state: IHooksOptions = reactive({
  dataListUrl: "/work/holidayRule/page",
  deleteUrl: "/work/holidayRule",
  queryForm: {},
});

const addOrUpdateRef = ref();
const addOrUpdateHandle = (id?: number) => {
  addOrUpdateRef.value.init(id);
};

const {
  getDataList,
  selectionChangeHandle,
  sizeChangeHandle,
  currentChangeHandle,
  deleteBatchHandle,
} = useCrud(state);
</script>
