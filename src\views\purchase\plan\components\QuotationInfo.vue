<template>
  <div>
    <el-descriptions :column="2" border>
      <el-descriptions-item label-align="right">
        <template #label>
          <div class="cell-item">采购计划编号</div>
        </template>
        {{ props.quotationInfo.packageNo }}
      </el-descriptions-item>
      <el-descriptions-item label-align="right">
        <template #label>
          <div class="cell-item">采购计划名称</div>
        </template>
        {{ props.quotationInfo.packageName }}
      </el-descriptions-item>
      <el-descriptions-item label-align="right">
        <template #label>
          <div class="cell-item">本次预算金额（元）</div>
        </template>
        {{ props.quotationInfo.packageBudget }}
      </el-descriptions-item>
      <el-descriptions-item label-align="right">
        <template #label>
          <div class="cell-item">报价开始时间</div>
        </template>
        {{ props.quotationInfo.offerStartTime }}
      </el-descriptions-item>
      <el-descriptions-item label-align="right">
        <template #label>
          <div class="cell-item">项目状态</div>
        </template>
        <template v-if="props.quotationInfo.roundStatus === 3"> 计划报价完成 </template>
        <template v-else>
          第 <span style="color: #f56c6c">{{ props.quotationInfo.roundNo }}</span> 轮<span
            v-if="props.quotationInfo.roundStatus === 0"
            >未开始 </span
          ><span v-if="props.quotationInfo.roundStatus === 1">报价中</span
          ><span v-if="props.quotationInfo.roundStatus === 2">已结束</span>
        </template>
      </el-descriptions-item>
      <el-descriptions-item label-align="right">
        <template #label>
          <div class="cell-item">报价截止时间</div>
        </template>
        <div style="color: #f56c6c">{{ props.quotationInfo.offerEndTime }}</div>
      </el-descriptions-item>
      <el-descriptions-item
        label-align="right"
        v-if="(props.quotationInfo.roundNo ?? 1) > 1"
      >
        <template #label>
          <div class="cell-item">报价说明</div>
        </template>
        <div>{{ props.quotationInfo.quotationDescription }}</div>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script setup lang="ts">
export interface IQuotationInfo {
  packageId?: number;
  packageNo: string;
  packageName: string;
  packageBudget: string;
  offerStartTime: string;
  offerEndTime: string;
  roundNo?: number;
  roundStatus?: number;
  maxOffer?: number;
  minOffer?: number;
  quotationDescription: string;
}

interface IProps {
  quotationInfo: IQuotationInfo;
}

const props = withDefaults(defineProps<IProps>(), {
  quotationInfo: () => {
    return {
      packageNo: "",
      packageName: "",
      packageBudget: "",
      offerStartTime: "",
      offerEndTime: "",
      roundNo: 1,
      roundStatus: 0,
      quotationDescription: "",
    };
  },
});
</script>
