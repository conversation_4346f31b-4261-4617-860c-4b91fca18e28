import service from '@/utils/request'
//获取我的审批数据
export const getAuditStatistics = () => {
  return service.get('/purchase/statistics/baseAuditInfo4Buyer')
}

//采购计划统计
export const getPlanStatistics = (year: string) => {
  return service.get(`/purchase/statistics/packageInfo4Buyer/${year}`)
}

//获取开标项目
export const getBidStatistics = (year: string, month: string) => {
  return service.get(`/purchase/statistics/openPack4Buyer/${year}/${month}`)
}

//获取开标项目详情数据
export const getBuyerDetail = (year: string, month: string, day: string) => {
  return service.get(`/purchase/statistics/openPack4BuyerDetail/${year}/${month}/${day}`)
}

//中标统计
export const getSuppelierSortList = () => {
  return service.get(`/purchase/statistics/getSuppelierSortList`)
}

//获取供应商统计数据
export const getSupplierStatistics = () => {
  return service.get(`/purchase/statistics/baseAuditInfo4Suppelier`)
}

//获取开标项目
export const getSuppelierStatistics = (year: string, month: string) => {
  return service.get(`/purchase/statistics/openPack4Suppelier/${year}/${month}`)
}

//获取开标项目详情数据
export const getSuppelierDetail = (year: string, month: string, day: string) => {
  return service.get(`/purchase/statistics/openPack4SuppelierDetail/${year}/${month}/${day}`)
}

//中标统计
export const getPlanSuppelierStatistics = (year: string) => {
  return service.get(`/purchase/statistics/packageInfo4Suppelier/${year}`)
}

//获取采购人任务中心数据
export const getPurchaseCenterStatistics = () => {
  return service.get(`purchase/statistics/baseAuditInfo4ZCZX`)
}

//获取采购人项目统计
export const getPurchaseProjectStatistics = (year: string) => {
  return service.get(`purchase/statistics/project4ZCZX/${year}`)
}

//按分类获取采购统计数据
export const getPurchaseCircleStatistics = (year: string) => {
  return service.get(`purchase/statistics/package4ZCZX/${year}`)
}