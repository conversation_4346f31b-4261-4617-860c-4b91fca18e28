<!--
 * @Descripttion: 
 * @Author: cuiyingchun
 * @Date: 2024-06-24 11:22:30
 * @LastEditors: cuiyingchun
 * @LastEditTime: 2024-07-03 15:39:04
-->
<template>
  <el-drawer
    v-model="state.show"
    :close-on-click-modal="false"
    title="审批详情"
    :size="850"
    class="detail_drawer"
  >
    <approvalcontent :id="state.id" :alldata="state.detailInfo" v-loading="state.loading">
      <template #center>
        <!-- 通用 -->
        <general :id="state.id" :alldata="state.detailInfo" v-if="state.type === 1" />
        <!-- 请假 -->
        <leave :id="state.id" :alldata="state.detailInfo" v-if="state.type === 2" />
        <!-- 费用报销 -->
        <expenseReim :id="state.id" :alldata="state.detailInfo" v-if="state.type === 3" />
        <!-- 付款申请 -->
        <payment :id="state.id" :alldata="state.detailInfo" v-if="state.type === 4" />
        <!-- 采购申请 -->
        <purchase :id="state.id" :alldata="state.detailInfo" v-if="state.type === 5" />
        <!-- 合同审批 -->
        <contract :id="state.id" :alldata="state.detailInfo" v-if="state.type === 6" />
      </template>
    </approvalcontent>
    <template #footer>
      <div style="flex: auto">
        <el-button
          @click="openChangever"
          v-if="state.detailInfo.auditStatus == 2 || state.detailInfo.auditStatus == 3"
          >更换审批人</el-button
        >
        <!-- <el-button @click="openSelectUser">抄送</el-button> -->
        <!-- 请假类型-审核通过的可以撤回,其他类型审核通过不能撤回 -->
        <el-button
          @click="cancelFlowHd"
          v-if="
            (state.detailInfo.infoType === 2 &&
              (state.detailInfo.auditStatus === 2 ||
                state.detailInfo.auditStatus === 3 ||
                state.detailInfo.auditStatus === 4)) ||
            (state.detailInfo.infoType !== 2 &&
              (state.detailInfo.auditStatus === 2 || state.detailInfo.auditStatus === 3))
          "
          >撤销</el-button
        >
        <el-button @click="onClickPrint" type="primary">打印</el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 变更审批人 -->
  <changeapprover
    v-if="state.changeapproverShow"
    ref="changeapproverRef"
    @refresh="getDetailInfo"
  ></changeapprover>
  <PrintDetail
    :show="print.show"
    :type="state.type"
    :alldata="state.detailInfo"
    @close="onClosePrint"
  ></PrintDetail>
</template>

<script setup lang="ts">
import { cancelFlow, workGetStatusById } from "@/api/workbench";
import { ElMessage, ElMessageBox } from "element-plus";
import { nextTick, reactive, ref } from "vue";
import approvalcontent from "../components/approvalcontent.vue";
import changeapprover from "../components/changeapprover.vue";
import contract from "../components/contract.vue";
import expenseReim from "../components/expenseReim.vue";
import general from "../components/general.vue";
import leave from "../components/leave.vue";
import payment from "../components/payment.vue";
import purchase from "../components/purchase.vue";
import { getDictLabel } from "@/utils/tool";
import { useAppStore } from "@/store/modules/app";
import PrintDetail from "../components/print-detail.vue";

const appStore = useAppStore();
interface IState {
  show: boolean;
  loading: boolean;
  detailInfo: Object;
  changeapproverShow: boolean;
  id: number;
  type: string | number;
}
const selectUserTreeRef = ref();
const changeapproverRef = ref();
const state = reactive<IState>({
  show: false,
  changeapproverShow: false,
  loading: false,
  detailInfo: {},
  id: 0,
  type: 1,
});
const emit = defineEmits(["refresh"]);

const init = (obj) => {
  state.show = true;
  state.id = obj.id;
  state.type = obj.infoType;
  getDetailInfo();
};
const getDetailInfo = () => {
  state.loading = true;
  workGetStatusById(state.id).then((res) => {
    if (res.code == 0) {
      state.detailInfo = res.data;
      if (state.type === 6) {
        let signType = state.detailInfo.oaContractFlowInfoVO.signType;
        if (signType) {
          let signTypeList = signType.split(",");
          let signTypeNameList = [];
          signTypeList.map((item) => {
            let name = getDictLabel(appStore.dictList, "sign_type", item);
            signTypeNameList.push(name);
          });
          state.detailInfo.oaContractFlowInfoVO.signTypeName = signTypeNameList.toString();
        }
      }
      state.loading = false;
    } else {
      state.loading = false;
    }
  });
};

const openChangever = () => {
  state.changeapproverShow = true;
  nextTick(() => {
    changeapproverRef.value.init(state.detailInfo, true);
  });
};
const cancelFlowHd = () => {
  ElMessageBox.prompt("确定要撤销该审批申请？", "操作确认", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
    inputPattern: /^.+$/,
    inputErrorMessage: "非空",
  })
    .then(({ value }) => {
      cancelFlow({ id: state.detailInfo.id, resultRemark: value }).then((res) => {
        if (res.code == 0) {
          ElMessage.success("提交成功");
          state.show = false;
          emit("refresh");
        }
      });
    })
    .catch(() => {});
};

//#region 打印相关
const print = reactive({
  show: false,
});
const onClickPrint = () => {
  print.show = true;
};

const onClosePrint = () => {
  print.show = false;
};
//#endregion

defineExpose({
  init,
});
</script>

<style scoped></style>
