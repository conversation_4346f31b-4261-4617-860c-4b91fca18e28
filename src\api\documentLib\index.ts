import service from '@/utils/request'

export const getDocumentTreeList = () => {
	return service.get('/work/folder/list')
}
export const getDocumentMyTreeList = () => {
	return service.get('/work/folder/myFolderList')
}
export const submitAddFolder = (data) => {
	return service.post('/work/folder', data)
}
export const managerIsCheck = () => {
	return service.get('/work/documentLibrary/managerIsCheck')
}
export const documentLibrary = (data) => {
	return service.post('/work/documentLibrary', data)
}
export const shareFile = (data) => {
	return service.put('/work/documentLibrary/shareFile', data)
}
export const moveFile = (data) => {
	return service.put('/work/documentLibrary/moveBatchFile', data)
}
export const resetName = (data) => {
	return service.post('/work/documentLibrary/rename', data)
}

export const deleteFile = (data) => {
	return service.delete('/work/documentLibrary/deleteFile', { data })
}

export const deleteFileShare = (data) => {
	return service.delete('/work/documentLibrary/deleteShareFile', { data })
}

export const deleteFolderApi = (id:any) => {
	return service.delete(`work/folder/deleteFolder/${id}`)
}